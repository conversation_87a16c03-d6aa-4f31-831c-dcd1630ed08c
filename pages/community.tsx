import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ForumIcon from '@mui/icons-material/Forum';
import EventIcon from '@mui/icons-material/Event';
import PeopleIcon from '@mui/icons-material/People';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import { FaD<PERSON>rd, FaTwitter, FaReddit, FaTwitch } from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(6),
  maxWidth: '800px',
}));

const CommunityCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const SocialCard = styled(Card)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: 60,
  height: 60,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& svg': {
    fontSize: 30,
    color: theme.palette.primary.main,
  },
}));

const EventCard = styled(Card)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const EventImage = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: 200,
  width: '100%',
}));

const EventTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const EventDate = styled(Typography)(({ theme }) => ({
  color: theme.palette.primary.main,
  fontWeight: 600,
  marginBottom: theme.spacing(2),
}));

// Sample community events
const events = [
  {
    title: 'Crypto Legends Tournament',
    description: 'Compete against the best players in our flagship game for a chance to win exclusive NFTs and tokens.',
    date: 'June 15, 2023',
    image: '/event1.jpg',
    link: '/events/crypto-legends-tournament',
  },
  {
    title: 'Developer Workshop',
    description: 'Learn how to build on the Gamestorme platform with our technical team in this interactive workshop.',
    date: 'June 22, 2023',
    image: '/event2.jpg',
    link: '/events/developer-workshop',
  },
  {
    title: 'Community AMA Session',
    description: 'Join our founders for a live Ask Me Anything session about the future of Gamestorme.',
    date: 'July 5, 2023',
    image: '/event3.jpg',
    link: '/events/ama-session',
  },
];

// Social platforms
const socialPlatforms = [
  {
    name: 'Discord',
    icon: <FaDiscord size={30} color="#5865F2" />,
    description: 'Join our Discord server to chat with other players and get real-time support.',
    members: '25K+ members',
    link: 'https://discord.gg/gamestorme',
  },
  {
    name: 'Twitter',
    icon: <FaTwitter size={30} color="#1DA1F2" />,
    description: 'Follow us on Twitter for the latest news, updates, and announcements.',
    members: '50K+ followers',
    link: 'https://twitter.com/gamestorme',
  },
  {
    name: 'Reddit',
    icon: <FaReddit size={30} color="#FF4500" />,
    description: 'Join our subreddit to discuss strategies, share content, and connect with the community.',
    members: '15K+ members',
    link: 'https://reddit.com/r/gamestorme',
  },
  {
    name: 'Twitch',
    icon: <FaTwitch size={30} color="#9146FF" />,
    description: 'Watch live streams of our games, tournaments, and developer sessions.',
    members: '10K+ followers',
    link: 'https://twitch.tv/gamestorme',
  },
];

const Community: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Join Our"
        highlightedTitle="Community"
        description="Connect with fellow gamers, developers, and blockchain enthusiasts in our vibrant community."
      />

      {/* Social Platforms Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <SectionTitle variant="h3">Connect With Us</SectionTitle>
            <SectionSubtitle variant="h6">
              Join our community across various platforms to stay updated, get support, and connect with other members.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {socialPlatforms.map((platform, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div variants={itemVariants}>
                    <SocialCard>
                      <Box sx={{ mb: 2 }}>
                        {platform.icon}
                      </Box>
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        {platform.name}
                      </Typography>
                      <Chip 
                        label={platform.members} 
                        size="small" 
                        sx={{ 
                          mb: 2,
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                        }} 
                      />
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        {platform.description}
                      </Typography>
                      <Button
                        variant="contained"
                        color="primary"
                        component="a"
                        href={platform.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        endIcon={<ArrowForwardIcon />}
                      >
                        Join Now
                      </Button>
                    </SocialCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Upcoming Events Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.5) }}>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Upcoming Events</SectionTitle>
            <SectionSubtitle variant="h6">
              Participate in our community events, tournaments, and workshops to learn, compete, and connect.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {events.map((event, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <motion.div variants={itemVariants} style={{ height: '100%' }}>
                    <EventCard>
                      <EventImage>
                        <Image
                          src={event.image}
                          alt={event.title}
                          fill
                          style={{ objectFit: 'cover' }}
                        />
                      </EventImage>
                      <CardContent sx={{ p: 3, flexGrow: 1 }}>
                        <EventDate variant="subtitle2">
                          <EventIcon sx={{ fontSize: 16, verticalAlign: 'middle', mr: 0.5 }} />
                          {event.date}
                        </EventDate>
                        <EventTitle variant="h5">{event.title}</EventTitle>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {event.description}
                        </Typography>
                        <Button
                          variant="outlined"
                          color="primary"
                          component={Link}
                          href={event.link}
                          endIcon={<ArrowForwardIcon />}
                        >
                          Learn More
                        </Button>
                      </CardContent>
                    </EventCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Community;
