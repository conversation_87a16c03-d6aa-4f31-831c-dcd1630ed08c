import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  <PERSON>rid,
  <PERSON>ton,
  Card,
  CardContent,
  Avatar,
  IconButton,
  useTheme,
  alpha,
  Badge,
  Chip,
  LinearProgress,
  TextField,
  Drawer,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  useMediaQuery,
  CircularProgress,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import EnhancedDashboard from '../../components/shared/EnhancedDashboard';
import EnhancedStatsCard from '../../components/shared/EnhancedStatsCard';
import GlassCard from '../../components/shared/GlassCard';
import EnhancedImage from '../../components/shared/EnhancedImage';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import GamesIcon from '@mui/icons-material/Games';
import PeopleIcon from '@mui/icons-material/People';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import FavoriteIcon from '@mui/icons-material/Favorite';
import SettingsIcon from '@mui/icons-material/Settings';
import ChatIcon from '@mui/icons-material/Chat';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SendIcon from '@mui/icons-material/Send';
import CloseIcon from '@mui/icons-material/Close';
import DownloadIcon from '@mui/icons-material/Download';
import TrophyIcon from '@mui/icons-material/EmojiEvents';

// Styled components

const GameCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 3,
  backgroundColor: alpha(theme.palette.background.paper, 0.9),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 16px 48px rgba(0,0,0,0.15)',
    border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const ChatDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 400,
    backgroundColor: alpha(theme.palette.background.paper, 0.95),
    backdropFilter: 'blur(20px)',
    borderLeft: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  },
}));

const StatsCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.9),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
  },
}));

const GameImage = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: 150,
  width: '100%',
  borderRadius: `${theme.shape.borderRadius * 2}px ${theme.shape.borderRadius * 2}px 0 0`,
  overflow: 'hidden',
}));

const GameTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const GameInfo = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
  display: 'flex',
  alignItems: 'center',
  '& svg': {
    fontSize: '1rem',
    marginRight: theme.spacing(0.5),
  },
}));



// Sample data
const sampleGames = [
  {
    id: 1,
    title: 'Agueybana',
    image: '/assets/images/games/agueybana.jpg',
    lastPlayed: '2 hours ago',
    hoursPlayed: 24,
    achievements: 12,
    totalAchievements: 30,
    progress: 40,
    status: 'installed',
  },
  {
    id: 2,
    title: 'Guaramania',
    image: '/assets/images/games/guaramania.png',
    lastPlayed: 'Yesterday',
    hoursPlayed: 8,
    achievements: 5,
    totalAchievements: 20,
    progress: 25,
    status: 'installed',
  },
  {
    id: 3,
    title: 'Mystic Realms',
    image: '/assets/images/games/mystic-realms.jpg',
    lastPlayed: '3 days ago',
    hoursPlayed: 42,
    achievements: 18,
    totalAchievements: 25,
    progress: 72,
    status: 'installed',
  },
];

const friends = [
  {
    id: 1,
    name: 'Alex Johnson',
    avatar: '/avatar1.jpg',
    status: 'online' as const,
    game: 'Playing Cosmic Odyssey',
  },
  {
    id: 2,
    name: 'Sarah Williams',
    avatar: '/avatar2.jpg',
    status: 'online' as const,
    game: 'Playing Neon Racer',
  },
  {
    id: 3,
    name: 'Mike Chen',
    avatar: '/avatar3.jpg',
    status: 'away' as const,
    game: 'Idle',
  },
  {
    id: 4,
    name: 'Emily Rodriguez',
    avatar: '/avatar4.jpg',
    status: 'offline' as const,
    game: 'Last online 3 hours ago',
  },
];

const GamerDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { currentUser, userProfile, logout, loading } = useAuth();

  const [activeSection, setActiveSection] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [chatType, setChatType] = useState<'friends' | 'support'>('friends');
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);
  const [chatMessages, setChatMessages] = useState([
    { id: 1, sender: 'friend', name: 'Alex', message: 'Hey! Want to play Agueybana together?', time: '2 min ago' },
    { id: 2, sender: 'me', name: 'You', message: 'Sure! Let me finish this level first.', time: '1 min ago' },
  ]);

  // Authentication check
  useEffect(() => {
    if (!loading) {
      if (!currentUser) {
        router.push('/login');
        return;
      }

      if (userProfile && userProfile.userType !== 'gamer') {
        // Redirect to appropriate dashboard based on user type
        if (userProfile.userType === 'developer') {
          router.push('/developer/dashboard');
        } else {
          router.push('/login');
        }
      }
    }
  }, [currentUser, userProfile, loading, router]);

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    if (isMobile) setSidebarOpen(false);
  };

  const handleLogout = async () => {
    try {
      setLogoutDialogOpen(false);
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Failed to log out:', error);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      setChatMessages(prev => [...prev, {
        id: Date.now(),
        sender: 'me',
        name: 'You',
        message: chatMessage,
        time: 'now'
      }]);
      setChatMessage('');
    }
  };

  // Show loading while checking authentication
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Don't render if not authenticated
  if (!currentUser || !userProfile) {
    return null;
  }

  // Sidebar items configuration with proper typing
  const sidebarItems: Array<{
    id: string;
    label: string;
    icon: React.ReactNode;
    badge?: string | number;
    color?: string;
  }> = [
    { id: 'dashboard', label: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'library', label: 'Game Library', icon: <GamesIcon />, badge: sampleGames.length },
    { id: 'store', label: 'Store', icon: <ShoppingCartIcon /> },
    { id: 'wishlist', label: 'Wishlist', icon: <FavoriteIcon />, badge: '5' },
    { id: 'friends', label: 'Friends', icon: <PeopleIcon />, badge: friends.filter(f => f.status === 'online').length, color: theme.palette.success.main },
    { id: 'achievements', label: 'Achievements', icon: <EmojiEventsIcon />, badge: '35' },
    { id: 'settings', label: 'Settings', icon: <SettingsIcon /> },
  ];

  const headerActions = (
    <>
      <Button
        variant="outlined"
        startIcon={<DownloadIcon />}
        sx={{ mr: 2 }}
      >
        Download Launcher
      </Button>
      <IconButton onClick={() => setChatOpen(true)}>
        <Badge badgeContent={2} color="primary">
          <ChatIcon />
        </Badge>
      </IconButton>
    </>
  );

  return (
    <EnhancedDashboard
      title="Gamer Dashboard"
      subtitle={`Welcome back, ${userProfile?.displayName || 'Gamer'}! Ready to play?`}
      userInitial={userProfile?.displayName?.charAt(0) || 'G'}
      notificationCount={3}
      sidebarItems={sidebarItems}
      activeSection={activeSection}
      onSectionChange={handleSectionChange}
      headerActions={headerActions}
    >

      {/* Dashboard Overview */}
      {activeSection === 'dashboard' && (
        <>
          {/* Welcome Banner */}
          <GlassCard sx={{ mb: 4, p: 4 }} hoverEffect="glow">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  Welcome back, {userProfile?.displayName || 'Gamer'}! 🎮
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3, fontSize: '1.1rem' }}>
                  Ready for your next gaming adventure? You have 3 new game recommendations waiting!
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    color="secondary"
                    size="large"
                    startIcon={<ShoppingCartIcon />}
                    onClick={() => handleSectionChange('store')}
                  >
                    Explore Store
                  </Button>
                  <Button
                    variant="outlined"
                    color="primary"
                    size="large"
                    startIcon={<PlayArrowIcon />}
                    onClick={() => handleSectionChange('library')}
                  >
                    Continue Playing
                  </Button>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <GamesIcon sx={{ fontSize: 120, color: theme.palette.primary.main, opacity: 0.8 }} />
                </Box>
              </Grid>
            </Grid>
          </GlassCard>

          {/* Gaming Stats */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Games Owned"
                value={sampleGames.length.toString()}
                change="+2"
                changeType="positive"
                icon={<GamesIcon />}
                color={theme.palette.primary.main}
                subtitle="This month"
                animationDelay={0}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Hours Played"
                value="74"
                icon={<AccessTimeIcon />}
                color={theme.palette.info.main}
                subtitle="This month"
                animationDelay={0.1}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Achievements"
                value="35"
                change="+5"
                changeType="positive"
                icon={<EmojiEventsIcon />}
                color={theme.palette.warning.main}
                subtitle="This week"
                animationDelay={0.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Friends Online"
                value={friends.filter(f => f.status === 'online').length.toString()}
                icon={<PeopleIcon />}
                color={theme.palette.success.main}
                subtitle={`of ${friends.length} friends`}
                animationDelay={0.3}
              />
            </Grid>
          </Grid>

          {/* Level Progress Card */}
          <GlassCard sx={{ mb: 4, p: 3 }} hoverEffect="lift">
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Player Progress
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{
                width: 60,
                height: 60,
                mr: 3,
                bgcolor: theme.palette.primary.main,
                fontSize: '1.5rem',
                fontWeight: 'bold',
              }}>
                24
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="h5" fontWeight="bold">
                  Level 24 Gamer
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  70% to Level 25
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={70}
                  sx={{
                    height: 12,
                    borderRadius: 6,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 6,
                      background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    }
                  }}
                />
              </Box>
            </Box>
          </GlassCard>

          {/* Recent Games */}
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            Recently Played
          </Typography>
          <Grid container spacing={3}>
            {sampleGames.map((game, index) => (
              <Grid item xs={12} sm={6} md={4} key={game.id}>
                <GlassCard hoverEffect="lift" animationDelay={index * 0.1}>
                  <Box sx={{ position: 'relative', height: 200, mb: 2 }}>
                    <EnhancedImage
                      src={game.image}
                      alt={game.title}
                      fill
                      hoverEffect="scale"
                      borderRadius={16}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        backgroundColor: alpha(theme.palette.background.paper, 0.9),
                        borderRadius: 2,
                        px: 2,
                        py: 1,
                        backdropFilter: 'blur(10px)',
                      }}
                    >
                      <Typography variant="caption" fontWeight="bold">
                        {game.progress}%
                      </Typography>
                    </Box>
                  </Box>
                  <CardContent sx={{ p: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {game.title}
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <AccessTimeIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        {game.lastPlayed} • {game.hoursPlayed}h played
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={game.progress}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          }
                        }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Chip
                        label={`${game.achievements}/${game.totalAchievements}`}
                        size="small"
                        icon={<TrophyIcon />}
                        sx={{
                          backgroundColor: alpha(theme.palette.warning.main, 0.1),
                          color: theme.palette.warning.main,
                          border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`,
                        }}
                      />
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<PlayArrowIcon />}
                        sx={{ borderRadius: 2 }}
                      >
                        Play
                      </Button>
                    </Box>
                  </CardContent>
                </GlassCard>
              </Grid>
            ))}
          </Grid>
        </>
      )}

          {/* Game Library Section */}
          {activeSection === 'library' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                My Game Library
              </Typography>
              <Grid container spacing={3}>
                {sampleGames.map((game) => (
                  <Grid item xs={12} sm={6} md={4} key={game.id}>
                    <GameCard>
                      <Box sx={{ position: 'relative', height: 200 }}>
                        <Image
                          src={game.image}
                          alt={game.title}
                          fill
                          style={{ objectFit: 'cover' }}
                        />
                        <Chip
                          label={game.status}
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            backgroundColor: theme.palette.success.main,
                            color: 'white',
                          }}
                        />
                      </Box>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {game.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Last played: {game.lastPlayed}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button variant="contained" size="small" startIcon={<PlayArrowIcon />}>
                            Play
                          </Button>
                          <Button variant="outlined" size="small">
                            Details
                          </Button>
                        </Box>
                      </CardContent>
                    </GameCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Store Section */}
          {activeSection === 'store' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                Game Store
              </Typography>
              <Grid container spacing={3}>
                {[
                  { id: 1, title: 'Cyber Runner 2077', price: '$29.99', image: '/assets/images/games/cyber-runner.jpg' },
                  { id: 2, title: 'Fantasy Quest', price: '$19.99', image: '/assets/images/games/fantasy-quest.jpg' },
                  { id: 3, title: 'Space Adventure', price: '$24.99', image: '/assets/images/games/space-adventure.jpg' },
                ].map((game) => (
                  <Grid item xs={12} sm={6} md={4} key={game.id}>
                    <GameCard>
                      <Box sx={{ position: 'relative', height: 200 }}>
                        <Image
                          src={game.image}
                          alt={game.title}
                          fill
                          style={{ objectFit: 'cover' }}
                        />
                      </Box>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {game.title}
                        </Typography>
                        <Typography variant="h6" color="primary" sx={{ mb: 2 }}>
                          {game.price}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button variant="contained" size="small" startIcon={<ShoppingCartIcon />}>
                            Buy Now
                          </Button>
                          <Button variant="outlined" size="small" startIcon={<FavoriteIcon />}>
                            Wishlist
                          </Button>
                        </Box>
                      </CardContent>
                    </GameCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Wishlist Section */}
          {activeSection === 'wishlist' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                My Wishlist
              </Typography>
              <Grid container spacing={3}>
                {[
                  { id: 1, title: 'Upcoming RPG', price: '$39.99', discount: '20%', image: '/assets/images/games/upcoming-rpg.jpg' },
                  { id: 2, title: 'Indie Puzzle Game', price: '$14.99', discount: null, image: '/assets/images/games/puzzle-game.jpg' },
                ].map((game) => (
                  <Grid item xs={12} sm={6} md={4} key={game.id}>
                    <GameCard>
                      <Box sx={{ position: 'relative', height: 200 }}>
                        <Image
                          src={game.image}
                          alt={game.title}
                          fill
                          style={{ objectFit: 'cover' }}
                        />
                        {game.discount && (
                          <Chip
                            label={`-${game.discount}`}
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              backgroundColor: theme.palette.error.main,
                              color: 'white',
                            }}
                          />
                        )}
                      </Box>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {game.title}
                        </Typography>
                        <Typography variant="h6" color="primary" sx={{ mb: 2 }}>
                          {game.price}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button variant="contained" size="small" startIcon={<ShoppingCartIcon />}>
                            Buy Now
                          </Button>
                          <Button variant="outlined" size="small" color="error">
                            Remove
                          </Button>
                        </Box>
                      </CardContent>
                    </GameCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Friends Section */}
          {activeSection === 'friends' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                Friends
              </Typography>
              <Grid container spacing={3}>
                {[
                  { id: 1, name: 'Alex Johnson', status: 'online', game: 'Playing Agueybana', avatar: 'AJ' },
                  { id: 2, name: 'Sarah Williams', status: 'online', game: 'Playing Guaramania', avatar: 'SW' },
                  { id: 3, name: 'Mike Chen', status: 'away', game: 'Away', avatar: 'MC' },
                  { id: 4, name: 'Emily Rodriguez', status: 'offline', game: 'Last online 2 hours ago', avatar: 'ER' },
                ].map((friend) => (
                  <Grid item xs={12} sm={6} md={4} key={friend.id}>
                    <StatsCard>
                      <Box sx={{ display: 'flex', alignItems: 'center', p: 3 }}>
                        <Avatar sx={{ width: 50, height: 50, mr: 2, bgcolor: theme.palette.primary.main }}>
                          {friend.avatar}
                        </Avatar>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" fontWeight="bold">
                            {friend.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {friend.game}
                          </Typography>
                          <Chip
                            label={friend.status}
                            size="small"
                            sx={{
                              mt: 1,
                              backgroundColor: friend.status === 'online' ? theme.palette.success.main :
                                             friend.status === 'away' ? theme.palette.warning.main :
                                             theme.palette.grey[500],
                              color: 'white',
                            }}
                          />
                        </Box>
                        <IconButton color="primary">
                          <ChatIcon />
                        </IconButton>
                      </Box>
                    </StatsCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Achievements Section */}
          {activeSection === 'achievements' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                Achievements
              </Typography>
              <Grid container spacing={3}>
                {[
                  { id: 1, title: 'First Victory', description: 'Win your first game', icon: '🏆', unlocked: true },
                  { id: 2, title: 'Speed Runner', description: 'Complete a level in under 2 minutes', icon: '⚡', unlocked: true },
                  { id: 3, title: 'Collector', description: 'Collect all items in a level', icon: '💎', unlocked: false },
                  { id: 4, title: 'Master Player', description: 'Reach level 50', icon: '👑', unlocked: false },
                ].map((achievement) => (
                  <Grid item xs={12} sm={6} md={3} key={achievement.id}>
                    <StatsCard sx={{ opacity: achievement.unlocked ? 1 : 0.6 }}>
                      <Box sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="h2" sx={{ mb: 2 }}>
                          {achievement.icon}
                        </Typography>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {achievement.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {achievement.description}
                        </Typography>
                        <Chip
                          label={achievement.unlocked ? 'Unlocked' : 'Locked'}
                          size="small"
                          sx={{
                            mt: 2,
                            backgroundColor: achievement.unlocked ? theme.palette.success.main : theme.palette.grey[500],
                            color: 'white',
                          }}
                        />
                      </Box>
                    </StatsCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Settings Section */}
          {activeSection === 'settings' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                Settings
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <StatsCard>
                    <Box sx={{ p: 3 }}>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Profile Settings
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <TextField
                          fullWidth
                          label="Display Name"
                          defaultValue="John Smith"
                          sx={{ mb: 2 }}
                        />
                        <TextField
                          fullWidth
                          label="Email"
                          defaultValue="<EMAIL>"
                          sx={{ mb: 2 }}
                        />
                      </Box>
                      <Button variant="contained">Save Changes</Button>
                    </Box>
                  </StatsCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <StatsCard>
                    <Box sx={{ p: 3 }}>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Game Preferences
                      </Typography>
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Enable notifications"
                        sx={{ mb: 2, display: 'block' }}
                      />
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Auto-update games"
                        sx={{ mb: 2, display: 'block' }}
                      />
                      <FormControlLabel
                        control={<Switch />}
                        label="Show online status"
                        sx={{ mb: 2, display: 'block' }}
                      />
                    </Box>
                  </StatsCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

      {/* Chat Drawer */}
      <ChatDrawer anchor="right" open={chatOpen} onClose={() => setChatOpen(false)}>
        <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6" fontWeight="bold">
              {chatType === 'friends' ? '💬 Friends Chat' : '🎧 Support Chat'}
            </Typography>
            <IconButton onClick={() => setChatOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Box sx={{ display: 'flex', mb: 3 }}>
            <Button
              variant={chatType === 'friends' ? 'contained' : 'outlined'}
              onClick={() => setChatType('friends')}
              sx={{ mr: 1, borderRadius: 2 }}
            >
              Friends
            </Button>
            <Button
              variant={chatType === 'support' ? 'contained' : 'outlined'}
              onClick={() => setChatType('support')}
              sx={{ borderRadius: 2 }}
            >
              Support
            </Button>
          </Box>

          <Box sx={{ flexGrow: 1, overflowY: 'auto', mb: 3 }}>
            {chatMessages.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.sender === 'me' ? 'flex-end' : 'flex-start',
                  mb: 2,
                }}
              >
                <Box
                  sx={{
                    maxWidth: '70%',
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: message.sender === 'me'
                      ? theme.palette.primary.main
                      : alpha(theme.palette.background.paper, 0.8),
                    color: message.sender === 'me' ? 'white' : theme.palette.text.primary,
                  }}
                >
                  <Typography variant="body2" fontWeight="bold" sx={{ mb: 0.5 }}>
                    {message.name}
                  </Typography>
                  <Typography variant="body2">{message.message}</Typography>
                  <Typography variant="caption" sx={{ opacity: 0.7, mt: 0.5, display: 'block' }}>
                    {message.time}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              fullWidth
              size="small"
              placeholder="Type a message..."
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              sx={{ borderRadius: 2 }}
            />
            <IconButton
              onClick={handleSendMessage}
              disabled={!chatMessage.trim()}
              sx={{
                backgroundColor: theme.palette.primary.main,
                color: 'white',
                '&:hover': { backgroundColor: theme.palette.primary.dark },
                '&:disabled': { backgroundColor: 'action.disabled' },
              }}
            >
              <SendIcon />
            </IconButton>
          </Box>
        </Box>
      </ChatDrawer>

      {/* Logout Dialog */}
      <Dialog open={logoutDialogOpen} onClose={() => setLogoutDialogOpen(false)}>
        <DialogTitle>Confirm Logout</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to logout from your gaming session?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLogoutDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleLogout} color="error" variant="contained">
            Logout
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedDashboard>
  );
};

export default GamerDashboard;
