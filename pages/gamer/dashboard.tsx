import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  TextField,
  IconButton,
  Badge,
  Chip,
  LinearProgress,
  useMedia<PERSON><PERSON>y,
  Drawer,
  AppBar,
  Too<PERSON><PERSON>,
  Rating,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';

// Firebase imports
import { firestore, auth } from '../../lib/firebase';
import { collection, query, where, orderBy, onSnapshot, updateDoc, doc, getDocs, limit } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

// Analytics imports
import { gamestormeAnalytics } from '../../lib/firebaseAnalytics';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import FavoriteIcon from '@mui/icons-material/Favorite';
import TrophyIcon from '@mui/icons-material/EmojiEvents';
import GroupIcon from '@mui/icons-material/Group';
import SettingsIcon from '@mui/icons-material/Settings';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MenuIcon from '@mui/icons-material/Menu';
import SearchIcon from '@mui/icons-material/Search';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ShareIcon from '@mui/icons-material/Share';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LogoutIcon from '@mui/icons-material/Logout';
import FilterListIcon from '@mui/icons-material/FilterList';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
}));

const MainContent = styled(Box)(({ theme }) => ({
  marginLeft: 280,
  padding: theme.spacing(3),
  [theme.breakpoints.down('md')]: {
    marginLeft: 0,
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const GameCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

// Interfaces
interface Game {
  id: string;
  title: string;
  developer: {
    name: string;
    uid: string;
  };
  description: string;
  genre: string[];
  pricing: {
    isFree: boolean;
    price: number;
  };
  stats: {
    downloads: number;
    views: number;
    likes: number;
    rating: number;
    reviews: number;
  };
  images: {
    thumbnail: string;
    screenshots: string[];
  };
  status: 'approved' | 'pending' | 'rejected';
  createdAt: Date;
}

interface GamerStats {
  totalGames: number;
  totalPlaytime: number;
  totalAchievements: number;
  favoriteGenres: string[];
  recentActivity: any[];
  friendsCount: number;
  level: number;
  experience: number;
  badges: string[];
}

const GamerDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [user, loading, error] = useAuthState(auth);

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [games, setGames] = useState<Game[]>([]);
  const [gamerStats, setGamerStats] = useState<GamerStats | null>(null);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Real-time data fetching
  useEffect(() => {
    if (!user) return;

    // Track dashboard view
    gamestormeAnalytics.setUser(user.uid, {
      user_type: 'gamer',
      registration_date: user.metadata.creationTime,
    });
    gamestormeAnalytics.trackPageView('gamer_dashboard');

    // Fetch approved games
    const gamesQuery = query(
      collection(firestore, 'games'),
      where('status', '==', 'approved'),
      orderBy('createdAt', 'desc'),
      limit(50)
    );

    const unsubscribeGames = onSnapshot(gamesQuery, (snapshot) => {
      const gamesData: Game[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        gamesData.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
        } as Game);
      });
      setGames(gamesData);
    });

    // Initialize gamer stats
    const initialStats: GamerStats = {
      totalGames: 0,
      totalPlaytime: 0,
      totalAchievements: 0,
      favoriteGenres: [],
      recentActivity: [],
      friendsCount: 0,
      level: 1,
      experience: 250,
      badges: [],
    };
    setGamerStats(initialStats);

    return () => {
      unsubscribeGames();
    };
  }, [user]);

  const handleGamePlay = (gameId: string, gameTitle: string) => {
    gamestormeAnalytics.trackGameView(gameId, gameTitle);
    console.log('Launching game:', gameTitle);
  };

  const handleGameLike = async (gameId: string, gameTitle: string) => {
    try {
      gamestormeAnalytics.trackCustomEvent('game_like', { gameId, gameTitle });
      await updateDoc(doc(firestore, 'games', gameId), {
        'stats.likes': (games.find(g => g.id === gameId)?.stats.likes || 0) + 1,
      });
    } catch (error) {
      console.error('Error liking game:', error);
    }
  };

  const sidebarItems = [
    { label: 'Overview', icon: <DashboardIcon />, index: 0 },
    { label: 'Game Library', icon: <SportsEsportsIcon />, index: 1 },
    { label: 'Achievements', icon: <TrophyIcon />, index: 2 },
    { label: 'Friends', icon: <GroupIcon />, index: 3 },
    { label: 'Activity', icon: <TrendingUpIcon />, index: 4 },
    { label: 'Settings', icon: <SettingsIcon />, index: 5 },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading your gaming dashboard...</Typography>
      </Box>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <DashboardContainer>
      {/* Mobile Header */}
      {isMobile && (
        <AppBar position="fixed" sx={{ bgcolor: 'background.paper', color: 'text.primary' }}>
          <Toolbar>
            <IconButton onClick={() => setSidebarOpen(true)} edge="start">
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
              Gamer Dashboard
            </Typography>
            <IconButton>
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            background: `linear-gradient(180deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
            backdropFilter: 'blur(20px)',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" fontWeight="bold" color="primary.main" gutterBottom>
            Gamestorme
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Gamer Dashboard
          </Typography>
        </Box>

        <Divider />

        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar src={user.photoURL || ''} sx={{ mr: 2 }}>
              {user.displayName?.[0] || user.email?.[0] || 'G'}
            </Avatar>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                {user.displayName || 'Gamer'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Level {gamerStats?.level || 1}
              </Typography>
            </Box>
          </Box>
          
          {gamerStats && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Experience: {gamerStats.experience}/1000
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={(gamerStats.experience / 1000) * 100} 
                sx={{ mt: 0.5 }}
              />
            </Box>
          )}
        </Box>

        <Divider />

        <List sx={{ px: 2, py: 1 }}>
          {sidebarItems.map((item) => (
            <ListItem
              key={item.index}
              button
              selected={activeTab === item.index}
              onClick={() => {
                setActiveTab(item.index);
                if (isMobile) setSidebarOpen(false);
              }}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&.Mui-selected': {
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.15),
                  },
                },
              }}
            >
              <ListItemIcon sx={{ color: activeTab === item.index ? 'primary.main' : 'text.secondary' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: activeTab === item.index ? 'bold' : 'normal',
                  color: activeTab === item.index ? 'primary.main' : 'text.primary',
                }}
              />
            </ListItem>
          ))}
        </List>

        <Box sx={{ mt: 'auto', p: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<LogoutIcon />}
            onClick={() => auth.signOut()}
          >
            Sign Out
          </Button>
        </Box>
      </Drawer>

      {/* Main Content */}
      <MainContent sx={{ mt: isMobile ? 8 : 0 }}>
        <Container maxWidth="xl">
          {/* Overview Tab */}
          {activeTab === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Welcome back, {user.displayName || 'Gamer'}!
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Ready to continue your gaming adventure? Here's what's happening in your world.
              </Typography>

              {/* Stats Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SportsEsportsIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {games.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Games Available
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AccessTimeIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {Math.floor((gamerStats?.totalPlaytime || 0) / 60)}h
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Playtime
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <TrophyIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {gamerStats?.totalAchievements || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Achievements
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <GroupIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {gamerStats?.friendsCount || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Friends
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>

              {/* Available Games */}
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                Available Games
              </Typography>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {games.slice(0, 8).map((game) => (
                  <Grid item xs={12} sm={6} md={3} key={game.id}>
                    <GameCard onClick={() => handleGamePlay(game.id, game.title)}>
                      <Box sx={{ position: 'relative' }}>
                        <Avatar
                          src={game.images.thumbnail}
                          sx={{ width: '100%', height: 160, borderRadius: '16px 16px 0 0' }}
                          variant="rounded"
                        />
                        <Chip
                          label={game.pricing.isFree ? 'Free' : `$${game.pricing.price}`}
                          size="small"
                          sx={{ position: 'absolute', top: 8, right: 8, bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                        />
                      </Box>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom noWrap>
                          {game.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          by {game.developer.name}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          <Rating value={game.stats.rating} readOnly size="small" />
                          <Typography variant="caption">
                            ({game.stats.reviews})
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<PlayArrowIcon />}
                            fullWidth
                          >
                            {game.pricing.isFree ? 'Play' : 'Buy & Play'}
                          </Button>
                          <IconButton 
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleGameLike(game.id, game.title);
                            }}
                          >
                            <FavoriteIcon />
                          </IconButton>
                        </Box>
                      </CardContent>
                    </GameCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Game Library Tab */}
          {activeTab === 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4" fontWeight="bold">
                  Game Library
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    size="small"
                    placeholder="Search games..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                    }}
                  />
                  <Button variant="outlined" startIcon={<FilterListIcon />}>
                    Filter
                  </Button>
                </Box>
              </Box>

              <Grid container spacing={3}>
                {games
                  .filter(game => 
                    game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    game.developer.name.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                  .map((game) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={game.id}>
                      <GameCard>
                        <Box sx={{ position: 'relative' }}>
                          <Avatar
                            src={game.images.thumbnail}
                            sx={{ width: '100%', height: 200, borderRadius: '16px 16px 0 0' }}
                            variant="rounded"
                          />
                          <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
                            <IconButton
                              size="small"
                              sx={{ bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              onClick={() => handleGameLike(game.id, game.title)}
                            >
                              <FavoriteIcon />
                            </IconButton>
                          </Box>
                        </Box>
                        <CardContent>
                          <Typography variant="h6" fontWeight="bold" gutterBottom noWrap>
                            {game.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            by {game.developer.name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <Rating value={game.stats.rating} readOnly size="small" />
                            <Typography variant="caption">
                              ({game.stats.reviews})
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              variant="contained"
                              size="small"
                              startIcon={<PlayArrowIcon />}
                              onClick={() => handleGamePlay(game.id, game.title)}
                              fullWidth
                            >
                              {game.pricing.isFree ? 'Play' : `$${game.pricing.price}`}
                            </Button>
                            <IconButton size="small">
                              <ShareIcon />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </GameCard>
                    </Grid>
                  ))}
              </Grid>
            </motion.div>
          )}

          {/* Other tabs placeholders */}
          {activeTab === 2 && (
            <Typography variant="h4">Achievements coming soon...</Typography>
          )}
          {activeTab === 3 && (
            <Typography variant="h4">Friends coming soon...</Typography>
          )}
          {activeTab === 4 && (
            <Typography variant="h4">Activity coming soon...</Typography>
          )}
          {activeTab === 5 && (
            <Typography variant="h4">Settings coming soon...</Typography>
          )}
        </Container>
      </MainContent>
    </DashboardContainer>
  );
};

export default GamerDashboard;
