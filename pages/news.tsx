import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  useTheme,
  alpha,
  Divider,
  Avatar,
  Skeleton,
  Alert,
  Pagination,
  InputBase,
  Paper,
  IconButton,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import SearchIcon from '@mui/icons-material/Search';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PersonIcon from '@mui/icons-material/Person';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NewsArticle } from '../types/database';

const NewsContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const SearchBar = styled(Paper)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(1, 2),
  marginBottom: theme.spacing(4),
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
}));

const FeaturedCard = styled(Card)(({ theme }) => ({
  height: 400,
  position: 'relative',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const NewsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const FeaturedOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%)',
  padding: theme.spacing(3),
  color: 'white',
}));

const CategoryChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  fontWeight: 'bold',
  '&:hover': {
    background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
  },
}));

const News: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [featuredArticles, setFeaturedArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchNews();
    fetchFeaturedNews();
  }, [page, searchTerm]);

  const fetchNews = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '9',
        status: 'published',
      });

      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/news?${params}`);
      const result = await response.json();

      if (result.success) {
        setArticles(result.data);
        setTotalPages(result.pagination.totalPages);
      } else {
        setError(result.error || 'Failed to load news');
      }
    } catch (err) {
      setError('Failed to load news');
    } finally {
      setLoading(false);
    }
  };

  const fetchFeaturedNews = async () => {
    try {
      const response = await fetch('/api/news?featured=true&limit=3&status=published');
      const result = await response.json();

      if (result.success) {
        setFeaturedArticles(result.data);
      }
    } catch (err) {
      console.error('Failed to load featured news:', err);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleArticleClick = (articleId: string) => {
    router.push(`/news/${articleId}`);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const renderFeaturedArticle = (article: NewsArticle, index: number) => (
    <Grid item xs={12} md={index === 0 ? 8 : 4} key={article.id}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <FeaturedCard onClick={() => handleArticleClick(article.id)}>
          <Image
            src={article.images.featured}
            alt={article.title}
            fill
            style={{ objectFit: 'cover' }}
          />
          <FeaturedOverlay>
            <CategoryChip label={article.category} size="small" sx={{ mb: 2 }} />
            <Typography variant={index === 0 ? 'h4' : 'h6'} fontWeight="bold" gutterBottom>
              {article.title}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 2 }}>
              {article.excerpt}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Avatar src={article.author.avatar} sx={{ width: 24, height: 24 }}>
                  {article.author.name.charAt(0)}
                </Avatar>
                <Typography variant="caption">{article.author.name}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CalendarTodayIcon sx={{ fontSize: 16 }} />
                <Typography variant="caption">
                  {formatDate(article.publishedAt || article.createdAt)}
                </Typography>
              </Box>
            </Box>
          </FeaturedOverlay>
        </FeaturedCard>
      </motion.div>
    </Grid>
  );

  const renderNewsCard = (article: NewsArticle, index: number) => (
    <Grid item xs={12} sm={6} md={4} key={article.id}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <NewsCard onClick={() => handleArticleClick(article.id)}>
          <CardMedia
            component="div"
            sx={{ height: 200, position: 'relative' }}
          >
            <Image
              src={article.images.featured}
              alt={article.title}
              fill
              style={{ objectFit: 'cover' }}
            />
          </CardMedia>
          <CardContent sx={{ flexGrow: 1, p: 2 }}>
            <CategoryChip label={article.category} size="small" sx={{ mb: 2 }} />
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              {article.title}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, height: 60, overflow: 'hidden' }}>
              {article.excerpt}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Avatar src={article.author.avatar} sx={{ width: 24, height: 24 }}>
                  {article.author.name.charAt(0)}
                </Avatar>
                <Typography variant="caption" color="text.secondary">
                  {article.author.name}
                </Typography>
              </Box>
              <Typography variant="caption" color="text.secondary">
                {formatDate(article.publishedAt || article.createdAt)}
              </Typography>
            </Box>

            <Divider sx={{ mb: 2 }} />

            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <VisibilityIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {article.stats.views}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <FavoriteIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {article.stats.likes}
                  </Typography>
                </Box>
              </Box>
              <Button
                size="small"
                endIcon={<ArrowForwardIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleArticleClick(article.id);
                }}
              >
                Read More
              </Button>
            </Box>
          </CardContent>
        </NewsCard>
      </motion.div>
    </Grid>
  );

  const renderLoadingSkeleton = () => (
    <Grid container spacing={3}>
      {Array.from({ length: 9 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card>
            <Skeleton variant="rectangular" height={200} />
            <CardContent>
              <Skeleton variant="text" height={32} />
              <Skeleton variant="text" height={20} width="80%" />
              <Skeleton variant="text" height={20} width="60%" />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Skeleton variant="circular" width={24} height={24} />
                <Skeleton variant="text" width={80} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  return (
    <Layout>
      <PageHeader
        title="Gaming"
        highlightedTitle="News"
        description="Stay updated with the latest gaming news, industry insights, and Gamestorme platform updates."
      />

      <NewsContainer>
        <Container maxWidth="lg">
          {/* Search Bar */}
          <SearchBar elevation={0}>
            <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <InputBase
              placeholder="Search news articles..."
              value={searchTerm}
              onChange={handleSearch}
              sx={{ flex: 1 }}
            />
          </SearchBar>

          {/* Featured Articles */}
          {featuredArticles.length > 0 && (
            <Box sx={{ mb: 6 }}>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Featured Stories
              </Typography>
              <Grid container spacing={3}>
                {featuredArticles.map(renderFeaturedArticle)}
              </Grid>
            </Box>
          )}

          {/* Latest News */}
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Latest News
          </Typography>

          {loading ? (
            renderLoadingSkeleton()
          ) : error ? (
            <Alert severity="error" sx={{ mb: 4 }}>
              {error}
            </Alert>
          ) : articles.length === 0 ? (
            <Alert severity="info" sx={{ mb: 4 }}>
              No news articles found. Try adjusting your search.
            </Alert>
          ) : (
            <>
              <Grid container spacing={3}>
                {articles.map(renderNewsCard)}
              </Grid>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                    size="large"
                  />
                </Box>
              )}
            </>
          )}
        </Container>
      </NewsContainer>
    </Layout>
  );
};

export default News;
