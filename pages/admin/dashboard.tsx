import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  TextField,
  IconButton,
  Badge,
  Chip,
  LinearProgress,
  useMediaQuery,
  Drawer,
  AppBar,
  Toolbar,
  Rating,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { useRouter } from 'next/router';

// Firebase imports
import { firestore, auth } from '../../lib/firebase';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  updateDoc,
  doc,
  getDocs,
  limit,
  addDoc,
  deleteDoc,
  Timestamp
} from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

// Analytics imports
import { gamestormeAnalytics } from '../../lib/firebaseAnalytics';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import PeopleIcon from '@mui/icons-material/People';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import SettingsIcon from '@mui/icons-material/Settings';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MenuIcon from '@mui/icons-material/Menu';
import SearchIcon from '@mui/icons-material/Search';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import LogoutIcon from '@mui/icons-material/Logout';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import PublishIcon from '@mui/icons-material/Publish';
import UnpublishedIcon from '@mui/icons-material/Unpublished';
import ReplyIcon from '@mui/icons-material/Reply';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
}));

const MainContent = styled(Box)(({ theme }) => ({
  marginLeft: 280,
  padding: theme.spacing(3),
  [theme.breakpoints.down('md')]: {
    marginLeft: 0,
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const ActionCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 10px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
  },
}));

// Interfaces
interface Game {
  id: string;
  title: string;
  developer: {
    name: string;
    uid: string;
    email: string;
  };
  description: string;
  genre: string[];
  pricing: {
    isFree: boolean;
    price: number;
  };
  stats: {
    downloads: number;
    views: number;
    likes: number;
    rating: number;
    reviews: number;
  };
  images: {
    thumbnail: string;
    screenshots: string[];
  };
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
  rejectionReason?: string;
  adminNotes?: string;
}

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'billing' | 'feature-request' | 'bug-report' | 'general';
  developerId: string;
  developerName: string;
  developerEmail: string;
  adminResponse?: string;
  adminId?: string;
  tags: string[];
  escalated: boolean;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}

interface PlatformStats {
  totalGames: number;
  pendingGames: number;
  approvedGames: number;
  rejectedGames: number;
  totalDevelopers: number;
  activeDevelopers: number;
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalDownloads: number;
  monthlyDownloads: number;
  openTickets: number;
  resolvedTickets: number;
  averageResponseTime: number;
}

interface AnalyticsEvent {
  id: string;
  type: string;
  gameId?: string;
  developerId?: string;
  userId?: string;
  sessionId: string;
  timestamp: Date;
  metadata: any;
}

const AdminDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [user, loading, error] = useAuthState(auth);

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [games, setGames] = useState<Game[]>([]);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats | null>(null);
  const [analyticsEvents, setAnalyticsEvents] = useState<AnalyticsEvent[]>([]);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [gameDialogOpen, setGameDialogOpen] = useState(false);
  const [ticketDialogOpen, setTicketDialogOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');

  // Real-time data fetching
  useEffect(() => {
    if (!user) return;

    // Check if user is admin (you might want to implement proper admin role checking)
    // For now, we'll assume any authenticated user can access admin dashboard
    // In production, you should check user roles/permissions

    // Track admin dashboard view
    gamestormeAnalytics.setUser(user.uid, {
      user_type: 'admin',
      registration_date: user.metadata.creationTime,
    });
    gamestormeAnalytics.trackPageView('admin_dashboard');

    // Fetch all games (including pending ones)
    const gamesQuery = query(
      collection(firestore, 'games'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribeGames = onSnapshot(gamesQuery, (snapshot) => {
      const gamesData: Game[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        gamesData.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as Game);
      });
      setGames(gamesData);
    });

    // Fetch all support tickets
    const ticketsQuery = query(
      collection(firestore, 'supportTickets'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribeTickets = onSnapshot(ticketsQuery, (snapshot) => {
      const ticketsData: SupportTicket[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        ticketsData.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          resolvedAt: data.resolvedAt?.toDate(),
        } as SupportTicket);
      });
      setSupportTickets(ticketsData);
    });

    // Fetch analytics events
    const analyticsQuery = query(
      collection(firestore, 'analyticsEvents'),
      orderBy('timestamp', 'desc'),
      limit(100)
    );

    const unsubscribeAnalytics = onSnapshot(analyticsQuery, (snapshot) => {
      const eventsData: AnalyticsEvent[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        eventsData.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
        } as AnalyticsEvent);
      });
      setAnalyticsEvents(eventsData);
    });

    // Fetch admin notifications
    const notificationsQuery = query(
      collection(firestore, 'adminNotifications'),
      where('read', '==', false),
      orderBy('createdAt', 'desc'),
      limit(10)
    );

    const unsubscribeNotifications = onSnapshot(notificationsQuery, (snapshot) => {
      const notificationsData: any[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        notificationsData.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
        });
      });
      setNotifications(notificationsData);
    });

    return () => {
      unsubscribeGames();
      unsubscribeTickets();
      unsubscribeAnalytics();
      unsubscribeNotifications();
    };
  }, [user]);

  // Calculate platform stats
  useEffect(() => {
    if (games.length > 0 || supportTickets.length > 0) {
      const stats: PlatformStats = {
        totalGames: games.length,
        pendingGames: games.filter(g => g.status === 'pending').length,
        approvedGames: games.filter(g => g.status === 'approved').length,
        rejectedGames: games.filter(g => g.status === 'rejected').length,
        totalDevelopers: new Set(games.map(g => g.developer.uid)).size,
        activeDevelopers: new Set(games.filter(g => g.status === 'approved').map(g => g.developer.uid)).size,
        totalUsers: 0, // This would come from user analytics
        activeUsers: 0, // This would come from user analytics
        totalRevenue: games.reduce((sum, g) => sum + (g.stats.downloads * (g.pricing.price || 0)), 0),
        monthlyRevenue: 0, // Calculate based on recent downloads
        totalDownloads: games.reduce((sum, g) => sum + g.stats.downloads, 0),
        monthlyDownloads: 0, // Calculate based on recent analytics
        openTickets: supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').length,
        resolvedTickets: supportTickets.filter(t => t.status === 'resolved' || t.status === 'closed').length,
        averageResponseTime: 0, // Calculate based on ticket response times
      };
      setPlatformStats(stats);
    }
  }, [games, supportTickets]);

  const handleGameApproval = async (gameId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      const updateData: any = {
        status: action === 'approve' ? 'approved' : 'rejected',
        updatedAt: Timestamp.now(),
        adminId: user?.uid,
      };

      if (action === 'reject' && reason) {
        updateData.rejectionReason = reason;
      }

      await updateDoc(doc(firestore, 'games', gameId), updateData);

      // Log admin action
      await addDoc(collection(firestore, 'adminLogs'), {
        action: `game_${action}`,
        gameId,
        adminId: user?.uid,
        adminEmail: user?.email,
        reason: reason || '',
        timestamp: Timestamp.now(),
      });

      // Send notification to developer
      const game = games.find(g => g.id === gameId);
      if (game) {
        await addDoc(collection(firestore, 'notifications'), {
          userId: game.developer.uid,
          type: action === 'approve' ? 'game_approved' : 'game_rejected',
          title: action === 'approve' ? 'Game Approved!' : 'Game Rejected',
          message: action === 'approve'
            ? `Your game "${game.title}" has been approved and is now live!`
            : `Your game "${game.title}" was rejected. Reason: ${reason}`,
          read: false,
          createdAt: Timestamp.now(),
        });
      }

      setSnackbarMessage(`Game ${action === 'approve' ? 'approved' : 'rejected'} successfully!`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setGameDialogOpen(false);
    } catch (error) {
      console.error(`Error ${action}ing game:`, error);
      setSnackbarMessage(`Error ${action}ing game. Please try again.`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleTicketResponse = async (ticketId: string, response: string, newStatus: string) => {
    try {
      await updateDoc(doc(firestore, 'supportTickets', ticketId), {
        adminResponse: response,
        adminId: user?.uid,
        status: newStatus,
        updatedAt: Timestamp.now(),
        resolvedAt: newStatus === 'resolved' ? Timestamp.now() : null,
      });

      // Log admin action
      await addDoc(collection(firestore, 'adminLogs'), {
        action: 'ticket_response',
        ticketId,
        adminId: user?.uid,
        adminEmail: user?.email,
        response,
        newStatus,
        timestamp: Timestamp.now(),
      });

      // Send notification to developer
      const ticket = supportTickets.find(t => t.id === ticketId);
      if (ticket) {
        await addDoc(collection(firestore, 'notifications'), {
          userId: ticket.developerId,
          type: 'ticket_response',
          title: 'Support Ticket Update',
          message: `Your support ticket "${ticket.title}" has been updated.`,
          read: false,
          createdAt: Timestamp.now(),
        });
      }

      setSnackbarMessage('Ticket response sent successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setTicketDialogOpen(false);
    } catch (error) {
      console.error('Error responding to ticket:', error);
      setSnackbarMessage('Error sending response. Please try again.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const sidebarItems = [
    { label: 'Overview', icon: <DashboardIcon />, index: 0 },
    { label: 'Game Management', icon: <SportsEsportsIcon />, index: 1 },
    { label: 'Support Tickets', icon: <SupportAgentIcon />, index: 2 },
    { label: 'User Management', icon: <PeopleIcon />, index: 3 },
    { label: 'Analytics', icon: <AnalyticsIcon />, index: 4 },
    { label: 'Settings', icon: <SettingsIcon />, index: 5 },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading admin dashboard...</Typography>
      </Box>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <DashboardContainer>
      {/* Mobile Header */}
      {isMobile && (
        <AppBar position="fixed" sx={{ bgcolor: 'background.paper', color: 'text.primary' }}>
          <Toolbar>
            <IconButton onClick={() => setSidebarOpen(true)} edge="start">
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
              Admin Dashboard
            </Typography>
            <IconButton>
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            background: `linear-gradient(180deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
            backdropFilter: 'blur(20px)',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" fontWeight="bold" color="primary.main" gutterBottom>
            Gamestorme
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Admin Dashboard
          </Typography>
        </Box>

        <Divider />

        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar src={user.photoURL || ''} sx={{ mr: 2, bgcolor: 'error.main' }}>
              {user.displayName?.[0] || user.email?.[0] || 'A'}
            </Avatar>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                {user.displayName || 'Admin'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Administrator
              </Typography>
            </Box>
          </Box>

          {platformStats && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Platform Health: {platformStats.pendingGames > 5 ? 'Needs Attention' : 'Good'}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={platformStats.pendingGames > 5 ? 60 : 90}
                color={platformStats.pendingGames > 5 ? 'warning' : 'success'}
                sx={{ mt: 0.5 }}
              />
            </Box>
          )}
        </Box>

        <Divider />

        <List sx={{ px: 2, py: 1 }}>
          {sidebarItems.map((item) => (
            <ListItem
              key={item.index}
              button
              selected={activeTab === item.index}
              onClick={() => {
                setActiveTab(item.index);
                if (isMobile) setSidebarOpen(false);
              }}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&.Mui-selected': {
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.15),
                  },
                },
              }}
            >
              <ListItemIcon sx={{ color: activeTab === item.index ? 'primary.main' : 'text.secondary' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: activeTab === item.index ? 'bold' : 'normal',
                  color: activeTab === item.index ? 'primary.main' : 'text.primary',
                }}
              />
              {/* Show badges for pending items */}
              {item.index === 1 && platformStats && platformStats.pendingGames > 0 && (
                <Badge badgeContent={platformStats.pendingGames} color="warning" />
              )}
              {item.index === 2 && platformStats && platformStats.openTickets > 0 && (
                <Badge badgeContent={platformStats.openTickets} color="error" />
              )}
            </ListItem>
          ))}
        </List>

        <Box sx={{ mt: 'auto', p: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<LogoutIcon />}
            onClick={() => auth.signOut()}
          >
            Sign Out
          </Button>
        </Box>
      </Drawer>

      {/* Main Content */}
      <MainContent sx={{ mt: isMobile ? 8 : 0 }}>
        <Container maxWidth="xl">
          {/* Overview Tab */}
          {activeTab === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4" fontWeight="bold">
                  Platform Overview
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => window.location.reload()}
                >
                  Refresh Data
                </Button>
              </Box>

              {/* Key Metrics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SportsEsportsIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalGames || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Games
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 1 }}>
                        <Chip
                          label={`${platformStats?.pendingGames || 0} Pending`}
                          size="small"
                          color="warning"
                        />
                        <Chip
                          label={`${platformStats?.approvedGames || 0} Live`}
                          size="small"
                          color="success"
                        />
                      </Box>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <PeopleIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalDevelopers || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Developers
                      </Typography>
                      <Typography variant="caption" color="success.main">
                        {platformStats?.activeDevelopers || 0} Active
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SupportAgentIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.openTickets || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Open Tickets
                      </Typography>
                      <Typography variant="caption" color="success.main">
                        {platformStats?.resolvedTickets || 0} Resolved
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AnalyticsIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalDownloads || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Downloads
                      </Typography>
                      <Typography variant="caption" color="primary.main">
                        ${(platformStats?.totalRevenue || 0).toFixed(2)} Revenue
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>

              {/* Recent Activity */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Recent Game Submissions
                      </Typography>
                      <List>
                        {games.filter(g => g.status === 'pending').slice(0, 5).map((game) => (
                          <ListItem key={game.id} disablePadding>
                            <ListItemIcon>
                              <Avatar src={game.images.thumbnail} sx={{ width: 40, height: 40 }}>
                                {game.title[0]}
                              </Avatar>
                            </ListItemIcon>
                            <ListItemText
                              primary={game.title}
                              secondary={`by ${game.developer.name} • ${game.createdAt.toLocaleDateString()}`}
                            />
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                size="small"
                                color="success"
                                onClick={() => {
                                  setSelectedGame(game);
                                  setGameDialogOpen(true);
                                }}
                              >
                                <CheckCircleIcon />
                              </IconButton>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => {
                                  setSelectedGame(game);
                                  setGameDialogOpen(true);
                                }}
                              >
                                <CancelIcon />
                              </IconButton>
                            </Box>
                          </ListItem>
                        ))}
                      </List>
                      {games.filter(g => g.status === 'pending').length === 0 && (
                        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                          No pending game submissions
                        </Typography>
                      )}
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Recent Support Tickets
                      </Typography>
                      <List>
                        {supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').slice(0, 5).map((ticket) => (
                          <ListItem key={ticket.id} disablePadding>
                            <ListItemIcon>
                              <PriorityHighIcon
                                color={
                                  ticket.priority === 'urgent' ? 'error' :
                                  ticket.priority === 'high' ? 'warning' :
                                  ticket.priority === 'medium' ? 'info' : 'disabled'
                                }
                              />
                            </ListItemIcon>
                            <ListItemText
                              primary={ticket.title}
                              secondary={`${ticket.developerName} • ${ticket.category} • ${ticket.createdAt.toLocaleDateString()}`}
                            />
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => {
                                setSelectedTicket(ticket);
                                setTicketDialogOpen(true);
                              }}
                            >
                              <ReplyIcon />
                            </IconButton>
                          </ListItem>
                        ))}
                      </List>
                      {supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').length === 0 && (
                        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                          No open support tickets
                        </Typography>
                      )}
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Game Management Tab */}
          {activeTab === 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4" fontWeight="bold">
                  Game Management
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    size="small"
                    placeholder="Search games..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                    }}
                  />
                  <Button variant="outlined" startIcon={<FilterListIcon />}>
                    Filter
                  </Button>
                </Box>
              </Box>

              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Game</TableCell>
                      <TableCell>Developer</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Submitted</TableCell>
                      <TableCell>Downloads</TableCell>
                      <TableCell>Revenue</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {games
                      .filter(game =>
                        game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        game.developer.name.toLowerCase().includes(searchQuery.toLowerCase())
                      )
                      .map((game) => (
                        <TableRow key={game.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar src={game.images.thumbnail} sx={{ width: 40, height: 40 }}>
                                {game.title[0]}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  {game.title}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {game.genre.join(', ')}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">{game.developer.name}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {game.developer.email}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={game.status}
                              color={
                                game.status === 'approved' ? 'success' :
                                game.status === 'pending' ? 'warning' : 'error'
                              }
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {game.createdAt.toLocaleDateString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {game.stats.downloads.toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              ${(game.stats.downloads * (game.pricing.price || 0)).toFixed(2)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setSelectedGame(game);
                                  setGameDialogOpen(true);
                                }}
                              >
                                <VisibilityIcon />
                              </IconButton>
                              {game.status === 'pending' && (
                                <>
                                  <IconButton
                                    size="small"
                                    color="success"
                                    onClick={() => handleGameApproval(game.id, 'approve')}
                                  >
                                    <CheckCircleIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => {
                                      setSelectedGame(game);
                                      setGameDialogOpen(true);
                                    }}
                                  >
                                    <CancelIcon />
                                  </IconButton>
                                </>
                              )}
                              {game.status === 'approved' && (
                                <IconButton
                                  size="small"
                                  color="warning"
                                  onClick={() => handleGameApproval(game.id, 'reject', 'Admin review required')}
                                >
                                  <UnpublishedIcon />
                                </IconButton>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </motion.div>
          )}

          {/* Support Tickets Tab */}
          {activeTab === 2 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Support Tickets
              </Typography>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Ticket</TableCell>
                      <TableCell>Developer</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Priority</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {supportTickets.map((ticket) => (
                      <TableRow key={ticket.id}>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {ticket.title}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {ticket.description.substring(0, 100)}...
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">{ticket.developerName}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {ticket.developerEmail}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip label={ticket.category} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={ticket.priority}
                            size="small"
                            color={
                              ticket.priority === 'urgent' ? 'error' :
                              ticket.priority === 'high' ? 'warning' :
                              ticket.priority === 'medium' ? 'info' : 'default'
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={ticket.status}
                            size="small"
                            color={
                              ticket.status === 'resolved' || ticket.status === 'closed' ? 'success' :
                              ticket.status === 'in-progress' ? 'info' : 'warning'
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {ticket.createdAt.toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => {
                              setSelectedTicket(ticket);
                              setTicketDialogOpen(true);
                            }}
                          >
                            <ReplyIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </motion.div>
          )}

          {/* Other tabs placeholders */}
          {activeTab === 3 && (
            <Typography variant="h4">User Management coming soon...</Typography>
          )}
          {activeTab === 4 && (
            <Typography variant="h4">Analytics coming soon...</Typography>
          )}
          {activeTab === 5 && (
            <Typography variant="h4">Settings coming soon...</Typography>
          )}
        </Container>
      </MainContent>

      {/* Game Approval Dialog */}
      <Dialog open={gameDialogOpen} onClose={() => setGameDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedGame ? `Review Game: ${selectedGame.title}` : 'Game Review'}
        </DialogTitle>
        <DialogContent>
          {selectedGame && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Avatar
                  src={selectedGame.images.thumbnail}
                  sx={{ width: '100%', height: 200, borderRadius: 2 }}
                  variant="rounded"
                />
              </Grid>
              <Grid item xs={12} md={8}>
                <Typography variant="h6" gutterBottom>{selectedGame.title}</Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  by {selectedGame.developer.name} ({selectedGame.developer.email})
                </Typography>
                <Typography variant="body2" paragraph>
                  {selectedGame.description}
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Genres:</Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {selectedGame.genre.map((g) => (
                      <Chip key={g} label={g} size="small" />
                    ))}
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Pricing:</Typography>
                  <Typography variant="body2">
                    {selectedGame.pricing.isFree ? 'Free' : `$${selectedGame.pricing.price}`}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Current Status:</Typography>
                  <Chip
                    label={selectedGame.status}
                    color={
                      selectedGame.status === 'approved' ? 'success' :
                      selectedGame.status === 'pending' ? 'warning' : 'error'
                    }
                  />
                </Box>
                {selectedGame.rejectionReason && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>Previous Rejection Reason:</Typography>
                    <Typography variant="body2" color="error.main">
                      {selectedGame.rejectionReason}
                    </Typography>
                  </Box>
                )}
              </Grid>
              {selectedGame.status === 'pending' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Admin Notes / Rejection Reason (if rejecting)"
                    placeholder="Enter any notes or reason for rejection..."
                    id="admin-notes"
                  />
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setGameDialogOpen(false)}>Cancel</Button>
          {selectedGame?.status === 'pending' && (
            <>
              <Button
                variant="contained"
                color="error"
                onClick={() => {
                  const reason = (document.getElementById('admin-notes') as HTMLInputElement)?.value || 'No reason provided';
                  handleGameApproval(selectedGame.id, 'reject', reason);
                }}
              >
                Reject Game
              </Button>
              <Button
                variant="contained"
                color="success"
                onClick={() => handleGameApproval(selectedGame.id, 'approve')}
              >
                Approve Game
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Support Ticket Response Dialog */}
      <Dialog open={ticketDialogOpen} onClose={() => setTicketDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedTicket ? `Respond to Ticket: ${selectedTicket.title}` : 'Ticket Response'}
        </DialogTitle>
        <DialogContent>
          {selectedTicket && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>{selectedTicket.title}</Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  From: {selectedTicket.developerName} ({selectedTicket.developerEmail})
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Category: {selectedTicket.category} | Priority: {selectedTicket.priority}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Created: {selectedTicket.createdAt.toLocaleDateString()}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>Description:</Typography>
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="body2">{selectedTicket.description}</Typography>
                </Paper>
              </Grid>
              {selectedTicket.adminResponse && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>Previous Admin Response:</Typography>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                    <Typography variant="body2">{selectedTicket.adminResponse}</Typography>
                  </Paper>
                </Grid>
              )}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Your Response"
                  placeholder="Enter your response to the developer..."
                  id="ticket-response"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>New Status</InputLabel>
                  <Select
                    defaultValue={selectedTicket.status}
                    label="New Status"
                    id="ticket-status"
                  >
                    <MenuItem value="open">Open</MenuItem>
                    <MenuItem value="in-progress">In Progress</MenuItem>
                    <MenuItem value="resolved">Resolved</MenuItem>
                    <MenuItem value="closed">Closed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTicketDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={() => {
              const response = (document.getElementById('ticket-response') as HTMLInputElement)?.value;
              const newStatus = (document.getElementById('ticket-status') as HTMLSelectElement)?.value;
              if (response && newStatus && selectedTicket) {
                handleTicketResponse(selectedTicket.id, response, newStatus);
              }
            }}
          >
            Send Response
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </DashboardContainer>
  );
};

export default AdminDashboard;