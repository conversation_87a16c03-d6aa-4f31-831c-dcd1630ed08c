import React from 'react';
import {
  Box,
  Container,
  Typography,
  useTheme,
  alpha,
  Avatar,
  Chip,
  <PERSON>ton,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../../components/layout/Layout';
import { motion } from 'framer-motion';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import HandshakeIcon from '@mui/icons-material/Handshake';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import SecurityIcon from '@mui/icons-material/Security';
import SpeedIcon from '@mui/icons-material/Speed';
import Link from 'next/link';

const ArticleContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const HeroSection = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '50vh',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  marginBottom: theme.spacing(4),
  background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
}));

const HeroOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
  display: 'flex',
  alignItems: 'flex-end',
  padding: theme.spacing(4),
}));

const ArticleContent = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  marginBottom: theme.spacing(4),
}));

const CategoryChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
  color: 'white',
  fontWeight: 'bold',
}));

const Partnership: React.FC = () => {
  const theme = useTheme();

  return (
    <Layout>
      <ArticleContainer>
        <Container maxWidth="md">
          {/* Back Button */}
          <Link href="/ecosystem" passHref style={{ textDecoration: 'none' }}>
            <Button
              startIcon={<ArrowBackIcon />}
              sx={{ mb: 3 }}
            >
              Back to Ecosystem
            </Button>
          </Link>

          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              <HeroOverlay>
                <Box>
                  <CategoryChip label="Partnership News" sx={{ mb: 2 }} />
                  <Typography variant="h3" fontWeight="bold" color="white" gutterBottom>
                    Strategic Partnership Announced
                  </Typography>
                  <Typography variant="h6" color="white" sx={{ opacity: 0.9 }}>
                    Gamestorme partners with leading blockchain platform to enhance gaming infrastructure
                  </Typography>
                </Box>
              </HeroOverlay>
            </HeroSection>
          </motion.div>

          {/* Article Meta */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 4 }}>
              <Avatar sx={{ bgcolor: 'secondary.main' }}>
                <HandshakeIcon />
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Partnership Team
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'text.secondary' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <CalendarTodayIcon sx={{ fontSize: 16 }} />
                    <Typography variant="caption">
                      December 27, 2024
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </motion.div>

          {/* Article Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <ArticleContent>
              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We're excited to announce a groundbreaking strategic partnership with a leading blockchain 
                platform that will revolutionize how games are distributed, monetized, and experienced 
                on the Gamestorme platform.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Partnership Overview
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                This partnership brings together Gamestorme's AI-powered game distribution platform with 
                cutting-edge blockchain technology to create new opportunities for developers and players alike. 
                The collaboration will focus on enhancing security, enabling new monetization models, and 
                improving the overall gaming experience.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                By integrating blockchain technology into our platform, we're opening up new possibilities 
                for digital ownership, cross-game assets, and innovative reward systems that benefit both 
                developers and players.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Key Benefits
              </Typography>

              <List sx={{ mb: 3 }}>
                <ListItem>
                  <ListItemIcon>
                    <SecurityIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Enhanced Security"
                    secondary="Blockchain-based security measures to protect user data and digital assets"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <RocketLaunchIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="New Monetization Models"
                    secondary="Innovative revenue streams for developers through NFTs and digital collectibles"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <SpeedIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Faster Transactions"
                    secondary="Lightning-fast payment processing and instant developer payouts"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <HandshakeIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Cross-Platform Assets"
                    secondary="Digital assets that work across multiple games and platforms"
                  />
                </ListItem>
              </List>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                What This Means for Developers
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Developers on the Gamestorme platform will gain access to new tools and technologies that 
                enable them to create more engaging and profitable games. The blockchain integration will 
                support features like:
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                • True digital ownership for in-game items and achievements<br/>
                • Cross-game compatibility for digital assets<br/>
                • New revenue streams through NFT marketplaces<br/>
                • Enhanced player engagement through blockchain-based rewards<br/>
                • Transparent and immutable transaction records
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Player Benefits
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Players will experience a new level of ownership and control over their gaming assets. 
                The blockchain integration enables true digital ownership, meaning players can trade, 
                sell, or transfer their in-game items across different games and platforms.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Additionally, players will benefit from enhanced security, faster transactions, and new 
                ways to earn rewards through gameplay and community participation.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Implementation Timeline
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                The integration will be rolled out in phases over the next six months, starting with 
                basic blockchain functionality and gradually expanding to include more advanced features. 
                We'll be working closely with our developer community to ensure a smooth transition and 
                provide comprehensive support throughout the process.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Beta testing will begin in Q1 2025, with full deployment expected by mid-2025. We're 
                committed to maintaining the highest standards of security and user experience throughout 
                this exciting transformation.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Looking Forward
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                This partnership represents a significant milestone in our mission to revolutionize the 
                gaming industry. By combining our AI-powered platform with cutting-edge blockchain technology, 
                we're creating new possibilities for developers and players that were previously unimaginable.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We're excited about the future and look forward to sharing more details about this 
                partnership and its benefits in the coming weeks.
              </Typography>

              <Box sx={{ mt: 4, p: 3, bgcolor: alpha(theme.palette.secondary.main, 0.1), borderRadius: 2 }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Stay Updated
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Follow our blog and social media channels for the latest updates on this partnership and other exciting developments.
                </Typography>
                <Link href="/news" passHref style={{ textDecoration: 'none' }}>
                  <Button variant="contained" color="secondary">
                    Read More News
                  </Button>
                </Link>
              </Box>
            </ArticleContent>
          </motion.div>
        </Container>
      </ArticleContainer>
    </Layout>
  );
};

export default Partnership;
