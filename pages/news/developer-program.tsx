import React from 'react';
import {
  Box,
  Container,
  Typography,
  useTheme,
  alpha,
  Avatar,
  Chip,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../../components/layout/Layout';
import { motion } from 'framer-motion';
import Image from 'next/image';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PersonIcon from '@mui/icons-material/Person';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import Link from 'next/link';

const ArticleContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const HeroSection = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '50vh',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  marginBottom: theme.spacing(4),
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
}));

const HeroOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
  display: 'flex',
  alignItems: 'flex-end',
  padding: theme.spacing(4),
}));

const ArticleContent = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  marginBottom: theme.spacing(4),
}));

const CategoryChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  fontWeight: 'bold',
}));

const DeveloperProgramNews: React.FC = () => {
  const theme = useTheme();

  return (
    <Layout>
      <ArticleContainer>
        <Container maxWidth="md">
          {/* Back Button */}
          <Link href="/ecosystem" passHref style={{ textDecoration: 'none' }}>
            <Button
              startIcon={<ArrowBackIcon />}
              sx={{ mb: 3 }}
            >
              Back to Ecosystem
            </Button>
          </Link>

          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              <HeroOverlay>
                <Box>
                  <CategoryChip label="Developer News" sx={{ mb: 2 }} />
                  <Typography variant="h3" fontWeight="bold" color="white" gutterBottom>
                    New Developer Program Launched
                  </Typography>
                  <Typography variant="h6" color="white" sx={{ opacity: 0.9 }}>
                    Empowering indie developers with better tools, support, and revenue opportunities
                  </Typography>
                </Box>
              </HeroOverlay>
            </HeroSection>
          </motion.div>

          {/* Article Meta */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 4 }}>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                GS
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Gamestorme Team
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'text.secondary' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <CalendarTodayIcon sx={{ fontSize: 16 }} />
                    <Typography variant="caption">
                      December 27, 2024
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </motion.div>

          {/* Article Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <ArticleContent>
              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We're thrilled to announce the launch of our enhanced Developer Program, designed specifically 
                to support indie game developers and help them succeed in today's competitive gaming market.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                What's New in Our Developer Program
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Our new developer program includes a comprehensive suite of tools and services designed to 
                help developers at every stage of their journey, from initial concept to global distribution.
              </Typography>

              <List sx={{ mb: 3 }}>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="AI-Powered Marketing Tools"
                    secondary="Access to Stormie AI for market analysis, ASO optimization, and content creation"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Enhanced Revenue Sharing"
                    secondary="Up to 85% revenue share for developers, with transparent reporting"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Global Distribution Network"
                    secondary="Reach millions of players worldwide through our platform"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Developer Support Hub"
                    secondary="24/7 technical support and marketing consultation"
                  />
                </ListItem>
              </List>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                How to Join
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Joining our developer program is simple and free. We welcome developers of all sizes, 
                from solo indie developers to established studios looking to expand their reach.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                To get started, simply create a developer account on our platform and submit your first game 
                for review. Our team will work with you to ensure your game meets our quality standards 
                and help you optimize it for success.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Success Stories
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Since launching our beta program earlier this year, we've seen incredible success stories 
                from developers who have joined our platform. Many have reported significant increases 
                in downloads, revenue, and player engagement.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                "Gamestorme's AI marketing tools helped us increase our game's visibility by 300% in just 
                three months. The platform's analytics and insights are game-changing for indie developers 
                like us." - Sarah Chen, Indie Developer
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Looking Forward
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                This is just the beginning. We're continuously working on new features and improvements 
                to make our platform the best place for developers to publish and promote their games. 
                Stay tuned for more exciting announcements in the coming months.
              </Typography>

              <Box sx={{ mt: 4, p: 3, bgcolor: alpha(theme.palette.primary.main, 0.1), borderRadius: 2 }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Ready to Join?
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Start your journey with Gamestorme today and take your games to the next level.
                </Typography>
                <Link href="/developers" passHref style={{ textDecoration: 'none' }}>
                  <Button variant="contained" size="large">
                    Learn More About Our Developer Program
                  </Button>
                </Link>
              </Box>
            </ArticleContent>
          </motion.div>
        </Container>
      </ArticleContainer>
    </Layout>
  );
};

export default DeveloperProgramNews;
