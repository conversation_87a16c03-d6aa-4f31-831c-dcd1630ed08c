import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  useTheme,
  alpha,
  Avatar,
  Chip,
  Divider,
  Button,
  IconButton,
  Skeleton,
  Alert,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../../components/layout/Layout';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { NewsArticle } from '../../types/database';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PersonIcon from '@mui/icons-material/Person';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const ArticleContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const HeroSection = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '50vh',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  marginBottom: theme.spacing(4),
}));

const HeroOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
  display: 'flex',
  alignItems: 'flex-end',
  padding: theme.spacing(4),
}));

const ArticleContent = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  marginBottom: theme.spacing(4),
}));

const AuthorCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
}));

const ShareButton = styled(IconButton)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  '&:hover': {
    background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
    transform: 'scale(1.1)',
  },
  transition: 'all 0.3s ease',
}));

const CategoryChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  fontWeight: 'bold',
}));

const NewsArticleDetail: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const theme = useTheme();
  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [liked, setLiked] = useState(false);

  useEffect(() => {
    if (id) {
      fetchArticle(id as string);
    }
  }, [id]);

  const fetchArticle = async (articleId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/news/${articleId}`);
      const result = await response.json();

      if (result.success) {
        setArticle(result.data);
      } else {
        setError(result.error || 'Failed to load article');
      }
    } catch (err) {
      setError('Failed to load article');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = () => {
    setLiked(!liked);
    // TODO: Implement like functionality with API
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = article?.title || '';
    
    let shareUrl = '';
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <Layout>
        <ArticleContainer>
          <Container maxWidth="md">
            <Skeleton variant="rectangular" height={400} sx={{ mb: 4, borderRadius: 2 }} />
            <Skeleton variant="text" height={60} />
            <Skeleton variant="text" height={40} width="60%" />
            <Box sx={{ display: 'flex', gap: 2, my: 2 }}>
              <Skeleton variant="circular" width={40} height={40} />
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" height={24} width="30%" />
                <Skeleton variant="text" height={20} width="20%" />
              </Box>
            </Box>
            <Skeleton variant="rectangular" height={300} sx={{ mt: 4 }} />
          </Container>
        </ArticleContainer>
      </Layout>
    );
  }

  if (error || !article) {
    return (
      <Layout>
        <ArticleContainer>
          <Container maxWidth="md">
            <Alert severity="error" sx={{ mt: 4 }}>
              {error || 'Article not found'}
            </Alert>
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={() => router.push('/news')}
              sx={{ mt: 2 }}
            >
              Back to News
            </Button>
          </Container>
        </ArticleContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <ArticleContainer>
        <Container maxWidth="md">
          {/* Back Button */}
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => router.push('/news')}
            sx={{ mb: 3 }}
          >
            Back to News
          </Button>

          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              <Image
                src={article.images.featured}
                alt={article.title}
                fill
                style={{ objectFit: 'cover' }}
              />
              <HeroOverlay>
                <Box>
                  <CategoryChip label={article.category} sx={{ mb: 2 }} />
                  <Typography variant="h3" fontWeight="bold" color="white" gutterBottom>
                    {article.title}
                  </Typography>
                  <Typography variant="h6" color="white" sx={{ opacity: 0.9 }}>
                    {article.excerpt}
                  </Typography>
                </Box>
              </HeroOverlay>
            </HeroSection>
          </motion.div>

          {/* Article Meta */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar src={article.author.avatar} sx={{ width: 48, height: 48 }}>
                  {article.author.name.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {article.author.name}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'text.secondary' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <CalendarTodayIcon sx={{ fontSize: 16 }} />
                      <Typography variant="caption">
                        {formatDate(article.publishedAt || article.createdAt)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <VisibilityIcon sx={{ fontSize: 16 }} />
                      <Typography variant="caption">
                        {article.stats.views} views
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Social Actions */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton
                  onClick={handleLike}
                  color={liked ? 'error' : 'default'}
                  sx={{ 
                    transition: 'all 0.3s ease',
                    '&:hover': { transform: 'scale(1.1)' }
                  }}
                >
                  <FavoriteIcon />
                </IconButton>
                <Typography variant="caption" color="text.secondary">
                  {article.stats.likes + (liked ? 1 : 0)}
                </Typography>
                
                <ShareButton onClick={() => handleShare('facebook')}>
                  <FacebookIcon />
                </ShareButton>
                <ShareButton onClick={() => handleShare('twitter')}>
                  <TwitterIcon />
                </ShareButton>
                <ShareButton onClick={() => handleShare('linkedin')}>
                  <LinkedInIcon />
                </ShareButton>
              </Box>
            </Box>
          </motion.div>

          {/* Article Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <ArticleContent>
              <Typography 
                variant="body1" 
                sx={{ 
                  lineHeight: 1.8,
                  fontSize: '1.1rem',
                  '& p': { mb: 2 },
                  '& h1, & h2, & h3, & h4, & h5, & h6': { 
                    mt: 3, 
                    mb: 2, 
                    fontWeight: 'bold' 
                  },
                  '& img': { 
                    maxWidth: '100%', 
                    height: 'auto', 
                    borderRadius: 1,
                    my: 2 
                  },
                  '& blockquote': {
                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                    pl: 2,
                    py: 1,
                    my: 2,
                    fontStyle: 'italic',
                    background: alpha(theme.palette.primary.main, 0.05),
                  }
                }}
                dangerouslySetInnerHTML={{ __html: article.content }}
              />

              {/* Tags */}
              {article.tags.length > 0 && (
                <Box sx={{ mt: 4, pt: 3, borderTop: 1, borderColor: 'divider' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {article.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        variant="outlined"
                        size="small"
                        clickable
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </ArticleContent>
          </motion.div>

          {/* Author Bio */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <AuthorCard>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                <Avatar src={article.author.avatar} sx={{ width: 80, height: 80 }}>
                  {article.author.name.charAt(0)}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    About {article.author.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Gaming journalist and industry expert with years of experience covering the latest trends in gaming technology and culture.
                  </Typography>
                </Box>
              </Box>
            </AuthorCard>
          </motion.div>

          {/* Related Articles Section */}
          <Box sx={{ mt: 6 }}>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Related Articles
            </Typography>
            <Alert severity="info">
              Related articles feature coming soon! We'll show you more articles from the same category and author.
            </Alert>
          </Box>
        </Container>
      </ArticleContainer>
    </Layout>
  );
};

export default NewsArticleDetail;
