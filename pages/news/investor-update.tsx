import React from 'react';
import {
  Box,
  Container,
  Typography,
  useTheme,
  alpha,
  Avatar,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../../components/layout/Layout';
import { motion } from 'framer-motion';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PeopleIcon from '@mui/icons-material/People';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import Link from 'next/link';

const ArticleContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const HeroSection = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '50vh',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  marginBottom: theme.spacing(4),
  background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`,
}));

const HeroOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
  display: 'flex',
  alignItems: 'flex-end',
  padding: theme.spacing(4),
}));

const ArticleContent = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  marginBottom: theme.spacing(4),
}));

const StatCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  textAlign: 'center',
  height: '100%',
}));

const CategoryChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`,
  color: 'white',
  fontWeight: 'bold',
}));

const InvestorUpdate: React.FC = () => {
  const theme = useTheme();

  return (
    <Layout>
      <ArticleContainer>
        <Container maxWidth="md">
          {/* Back Button */}
          <Link href="/ecosystem" passHref style={{ textDecoration: 'none' }}>
            <Button
              startIcon={<ArrowBackIcon />}
              sx={{ mb: 3 }}
            >
              Back to Ecosystem
            </Button>
          </Link>

          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              <HeroOverlay>
                <Box>
                  <CategoryChip label="Investor Relations" sx={{ mb: 2 }} />
                  <Typography variant="h3" fontWeight="bold" color="white" gutterBottom>
                    Investor Relations Update
                  </Typography>
                  <Typography variant="h6" color="white" sx={{ opacity: 0.9 }}>
                    Strong growth in user acquisition and revenue drives platform expansion
                  </Typography>
                </Box>
              </HeroOverlay>
            </HeroSection>
          </motion.div>

          {/* Article Meta */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 4 }}>
              <Avatar sx={{ bgcolor: 'success.main' }}>
                IR
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Investor Relations Team
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'text.secondary' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <CalendarTodayIcon sx={{ fontSize: 16 }} />
                    <Typography variant="caption">
                      December 27, 2024
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </motion.div>

          {/* Key Metrics */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mb: 3 }}>
              Q4 2024 Key Metrics
            </Typography>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={4}>
                <StatCard>
                  <TrendingUpIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    +150%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Revenue Growth YoY
                  </Typography>
                </StatCard>
              </Grid>
              <Grid item xs={12} md={4}>
                <StatCard>
                  <PeopleIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                  <Typography variant="h4" fontWeight="bold" color="primary.main">
                    2.5M+
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Users
                  </Typography>
                </StatCard>
              </Grid>
              <Grid item xs={12} md={4}>
                <StatCard>
                  <AttachMoneyIcon sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    $12M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Developer Payouts
                  </Typography>
                </StatCard>
              </Grid>
            </Grid>
          </motion.div>

          {/* Article Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <ArticleContent>
              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We're pleased to share our Q4 2024 investor relations update, highlighting the strong 
                performance and continued growth of the Gamestorme platform across all key metrics.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Financial Performance
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Our revenue growth of 150% year-over-year demonstrates the strong market demand for our 
                platform and the effectiveness of our AI-powered marketing tools. This growth has been 
                driven by increased developer adoption, higher user engagement, and successful expansion 
                into new markets.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We've maintained healthy profit margins while investing heavily in platform development, 
                AI technology improvements, and global expansion initiatives. Our focus on sustainable 
                growth continues to drive long-term value creation.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                User Growth and Engagement
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                Our active user base has grown to over 2.5 million users, representing a 200% increase 
                from the previous year. User engagement metrics continue to improve, with average session 
                times increasing by 45% and monthly active users showing consistent growth across all regions.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                The introduction of our enhanced discovery algorithms and personalized recommendations 
                has significantly improved user retention and game discovery, leading to higher conversion 
                rates for developers on our platform.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Developer Ecosystem Growth
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We've paid out over $12 million to developers in Q4 alone, representing a 180% increase 
                from the previous quarter. Our developer program now includes over 5,000 active developers, 
                with new registrations increasing by 300% following the launch of our enhanced AI marketing tools.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                The success of our Stormie AI platform has been particularly noteworthy, with developers 
                reporting average increases of 250% in game visibility and 180% in conversion rates when 
                using our AI-powered marketing recommendations.
              </Typography>

              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Looking Ahead
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                As we enter 2025, we remain focused on our core mission of empowering game developers 
                through innovative technology and global distribution. Our roadmap includes exciting 
                developments in AI technology, blockchain integration, and international expansion.
              </Typography>

              <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem', mb: 3 }}>
                We're confident in our ability to continue delivering strong returns for our investors 
                while building the future of game distribution and marketing technology.
              </Typography>

              <Box sx={{ mt: 4, p: 3, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 2 }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Investor Information
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  For detailed financial reports and investor materials, please contact our investor relations team.
                </Typography>
                <Link href="/contact" passHref style={{ textDecoration: 'none' }}>
                  <Button variant="contained" color="success">
                    Contact Investor Relations
                  </Button>
                </Link>
              </Box>
            </ArticleContent>
          </motion.div>
        </Container>
      </ArticleContainer>
    </Layout>
  );
};

export default InvestorUpdate;
