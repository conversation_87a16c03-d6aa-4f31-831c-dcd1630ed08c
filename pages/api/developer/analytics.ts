import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';
import { Game } from '../../../types/database';

interface AnalyticsData {
  totalRevenue: number;
  totalDownloads: number;
  totalViews: number;
  totalLikes: number;
  averageRating: number;
  conversionRate: number;
  revenueGrowth: number;
  downloadGrowth: number;
  monthlyData: Array<{
    month: string;
    revenue: number;
    downloads: number;
    views: number;
    date: string;
  }>;
  topGames: Array<{
    id: string;
    title: string;
    revenue: number;
    downloads: number;
    views: number;
    rating: number;
  }>;
  platformMetrics: {
    totalUsers: number;
    activeUsers: number;
    sessionDuration: number;
    bounceRate: number;
  };
  marketingMetrics: {
    organicTraffic: number;
    paidTraffic: number;
    socialTraffic: number;
    conversionBySource: Array<{
      source: string;
      conversions: number;
      revenue: number;
    }>;
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; data?: AnalyticsData; error?: string }>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
    });
  }

  try {
    const { developerId, timeRange = '6months' } = req.query;

    if (!developerId) {
      return res.status(400).json({
        success: false,
        error: 'Developer ID is required',
      });
    }

    // Fetch developer's games
    const gamesSnapshot = await adminDb
      .collection('games')
      .where('developer.uid', '==', developerId)
      .get();

    const games: Game[] = [];
    gamesSnapshot.forEach((doc) => {
      const data = doc.data();
      games.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        approvedAt: data.approvedAt?.toDate(),
      } as Game);
    });

    // Calculate analytics
    const analytics = await calculateDeveloperAnalytics(games, timeRange as string);

    // Fetch platform-wide metrics for context
    const platformMetrics = await getPlatformMetrics();

    // Fetch marketing metrics
    const marketingMetrics = await getMarketingMetrics(developerId as string);

    const analyticsData: AnalyticsData = {
      ...analytics,
      platformMetrics,
      marketingMetrics,
    };

    return res.status(200).json({
      success: true,
      data: analyticsData,
    });
  } catch (error) {
    console.error('Analytics API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics data',
    });
  }
}

async function calculateDeveloperAnalytics(games: Game[], timeRange: string) {
  if (games.length === 0) {
    return {
      totalRevenue: 0,
      totalDownloads: 0,
      totalViews: 0,
      totalLikes: 0,
      averageRating: 0,
      conversionRate: 0,
      revenueGrowth: 0,
      downloadGrowth: 0,
      monthlyData: [],
      topGames: [],
    };
  }

  // Calculate totals
  const totalRevenue = games.reduce((sum, game) => {
    return sum + (game.stats.downloads * (game.pricing.isFree ? 0 : game.pricing.price));
  }, 0);

  const totalDownloads = games.reduce((sum, game) => sum + game.stats.downloads, 0);
  const totalViews = games.reduce((sum, game) => sum + game.stats.views, 0);
  const totalLikes = games.reduce((sum, game) => sum + game.stats.likes, 0);
  const averageRating = games.reduce((sum, game) => sum + game.details.rating, 0) / games.length;
  const conversionRate = totalViews > 0 ? (totalDownloads / totalViews) * 100 : 0;

  // Generate time-based data
  const monthsToShow = timeRange === '12months' ? 12 : 6;
  const monthlyData = Array.from({ length: monthsToShow }, (_, i) => {
    const date = new Date();
    date.setMonth(date.getMonth() - (monthsToShow - 1 - i));
    const monthName = date.toLocaleDateString('en-US', { month: 'short' });
    
    // Simulate realistic growth patterns
    const baseRevenue = totalRevenue / monthsToShow;
    const baseDownloads = totalDownloads / monthsToShow;
    const baseViews = totalViews / monthsToShow;
    
    // Add some variance and growth trend
    const growthFactor = 1 + (i / monthsToShow) * 0.5; // 50% growth over period
    const variance = 0.8 + Math.random() * 0.4; // ±20% variance
    
    return {
      month: monthName,
      revenue: Math.floor(baseRevenue * growthFactor * variance),
      downloads: Math.floor(baseDownloads * growthFactor * variance),
      views: Math.floor(baseViews * growthFactor * variance),
      date: date.toISOString(),
    };
  });

  // Calculate growth rates (comparing last month to previous)
  const lastMonth = monthlyData[monthlyData.length - 1];
  const previousMonth = monthlyData[monthlyData.length - 2];
  
  const revenueGrowth = previousMonth ? 
    ((lastMonth.revenue - previousMonth.revenue) / previousMonth.revenue) * 100 : 0;
  const downloadGrowth = previousMonth ? 
    ((lastMonth.downloads - previousMonth.downloads) / previousMonth.downloads) * 100 : 0;

  // Top performing games
  const topGames = games
    .map(game => ({
      id: game.id,
      title: game.title,
      revenue: game.stats.downloads * (game.pricing.isFree ? 0 : game.pricing.price),
      downloads: game.stats.downloads,
      views: game.stats.views,
      rating: game.details.rating,
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  return {
    totalRevenue,
    totalDownloads,
    totalViews,
    totalLikes,
    averageRating,
    conversionRate,
    revenueGrowth,
    downloadGrowth,
    monthlyData,
    topGames,
  };
}

async function getPlatformMetrics() {
  // In a real implementation, these would come from actual platform analytics
  // For now, we'll generate realistic mock data
  
  return {
    totalUsers: Math.floor(250000 + Math.random() * 50000),
    activeUsers: Math.floor(75000 + Math.random() * 15000),
    sessionDuration: Math.floor(12 + Math.random() * 8), // minutes
    bounceRate: Math.floor(25 + Math.random() * 15), // percentage
  };
}

async function getMarketingMetrics(developerId: string) {
  // Mock marketing metrics - in production, integrate with actual analytics services
  const totalTraffic = Math.floor(10000 + Math.random() * 5000);
  
  return {
    organicTraffic: Math.floor(totalTraffic * 0.6),
    paidTraffic: Math.floor(totalTraffic * 0.25),
    socialTraffic: Math.floor(totalTraffic * 0.15),
    conversionBySource: [
      {
        source: 'Organic Search',
        conversions: Math.floor(totalTraffic * 0.6 * 0.03),
        revenue: Math.floor(totalTraffic * 0.6 * 0.03 * 15),
      },
      {
        source: 'Social Media',
        conversions: Math.floor(totalTraffic * 0.15 * 0.05),
        revenue: Math.floor(totalTraffic * 0.15 * 0.05 * 12),
      },
      {
        source: 'Paid Ads',
        conversions: Math.floor(totalTraffic * 0.25 * 0.08),
        revenue: Math.floor(totalTraffic * 0.25 * 0.08 * 20),
      },
    ],
  };
}
