import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';

interface MarketingInsight {
  type: 'optimization' | 'trend' | 'recommendation' | 'alert' | 'prediction';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  confidence: number; // 0-100
  actionable: boolean;
  category: 'pricing' | 'marketing' | 'content' | 'audience' | 'platform' | 'competition';
  data?: any;
  createdAt: Date;
}

interface AIMarketingResponse {
  insights: MarketingInsight[];
  summary: {
    totalInsights: number;
    highImpactInsights: number;
    actionableInsights: number;
    averageConfidence: number;
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
  };
  predictions: {
    revenueGrowth: number;
    downloadGrowth: number;
    marketTrends: string[];
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; data?: AIMarketingResponse; error?: string }>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
    });
  }

  try {
    const { developerId, gameIds, timeRange = '30days' } = req.body;

    if (!developerId) {
      return res.status(400).json({
        success: false,
        error: 'Developer ID is required',
      });
    }

    // Fetch developer's data
    const developerData = await fetchDeveloperData(developerId, gameIds, timeRange);
    
    // Generate AI insights based on the data
    const insights = await generateMarketingInsights(developerData);
    
    // Generate recommendations
    const recommendations = await generateRecommendations(developerData, insights);
    
    // Generate predictions
    const predictions = await generatePredictions(developerData);

    const response: AIMarketingResponse = {
      insights,
      summary: {
        totalInsights: insights.length,
        highImpactInsights: insights.filter(i => i.impact === 'high').length,
        actionableInsights: insights.filter(i => i.actionable).length,
        averageConfidence: insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length,
      },
      recommendations,
      predictions,
    };

    return res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error('AI Marketing Insights error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to generate marketing insights',
    });
  }
}

async function fetchDeveloperData(developerId: string, gameIds?: string[], timeRange: string) {
  // Fetch developer's games
  let gamesQuery = adminDb.collection('games').where('developer.uid', '==', developerId);
  
  if (gameIds && gameIds.length > 0) {
    gamesQuery = gamesQuery.where('__name__', 'in', gameIds);
  }

  const gamesSnapshot = await gamesQuery.get();
  const games = gamesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

  // Fetch analytics data
  const analyticsSnapshot = await adminDb
    .collection('analyticsEvents')
    .where('developerId', '==', developerId)
    .where('timestamp', '>=', getTimeRangeStart(timeRange))
    .get();

  const analyticsEvents = analyticsSnapshot.docs.map(doc => doc.data());

  // Fetch platform metrics for comparison
  const platformMetricsSnapshot = await adminDb
    .collection('platformMetrics')
    .orderBy('date', 'desc')
    .limit(30)
    .get();

  const platformMetrics = platformMetricsSnapshot.docs.map(doc => doc.data());

  return {
    games,
    analyticsEvents,
    platformMetrics,
    developerId,
    timeRange,
  };
}

async function generateMarketingInsights(data: any): Promise<MarketingInsight[]> {
  const insights: MarketingInsight[] = [];
  const { games, analyticsEvents, platformMetrics } = data;

  // Analyze pricing strategy
  const pricingInsight = analyzePricingStrategy(games, platformMetrics);
  if (pricingInsight) insights.push(pricingInsight);

  // Analyze conversion rates
  const conversionInsight = analyzeConversionRates(analyticsEvents);
  if (conversionInsight) insights.push(conversionInsight);

  // Analyze audience engagement
  const engagementInsight = analyzeAudienceEngagement(analyticsEvents);
  if (engagementInsight) insights.push(engagementInsight);

  // Analyze market trends
  const trendInsight = analyzeMarketTrends(games, platformMetrics);
  if (trendInsight) insights.push(trendInsight);

  // Analyze competition
  const competitionInsight = analyzeCompetition(games, platformMetrics);
  if (competitionInsight) insights.push(competitionInsight);

  // Analyze content performance
  const contentInsight = analyzeContentPerformance(games, analyticsEvents);
  if (contentInsight) insights.push(contentInsight);

  return insights;
}

function analyzePricingStrategy(games: any[], platformMetrics: any[]): MarketingInsight | null {
  const paidGames = games.filter(game => !game.pricing.isFree);
  if (paidGames.length === 0) return null;

  const averagePrice = paidGames.reduce((sum, game) => sum + game.pricing.price, 0) / paidGames.length;
  const platformAveragePrice = 15.99; // Mock platform average

  const priceComparison = averagePrice / platformAveragePrice;

  if (priceComparison > 1.5) {
    return {
      type: 'optimization',
      title: 'Pricing Strategy Optimization',
      description: `Your average game price ($${averagePrice.toFixed(2)}) is ${((priceComparison - 1) * 100).toFixed(0)}% higher than platform average. Consider testing lower price points to increase conversion.`,
      impact: 'high',
      confidence: 85,
      actionable: true,
      category: 'pricing',
      data: { averagePrice, platformAveragePrice, priceComparison },
      createdAt: new Date(),
    };
  } else if (priceComparison < 0.7) {
    return {
      type: 'optimization',
      title: 'Pricing Opportunity',
      description: `Your games are priced below market average. Consider testing higher price points to maximize revenue without significantly impacting downloads.`,
      impact: 'medium',
      confidence: 75,
      actionable: true,
      category: 'pricing',
      data: { averagePrice, platformAveragePrice, priceComparison },
      createdAt: new Date(),
    };
  }

  return null;
}

function analyzeConversionRates(analyticsEvents: any[]): MarketingInsight | null {
  const views = analyticsEvents.filter(e => e.type === 'game_view').length;
  const downloads = analyticsEvents.filter(e => e.type === 'game_download').length;
  
  if (views === 0) return null;

  const conversionRate = (downloads / views) * 100;
  const benchmarkConversionRate = 3.5; // Industry benchmark

  if (conversionRate < benchmarkConversionRate * 0.7) {
    return {
      type: 'alert',
      title: 'Low Conversion Rate Alert',
      description: `Your conversion rate (${conversionRate.toFixed(1)}%) is below industry benchmark (${benchmarkConversionRate}%). Focus on improving game descriptions, screenshots, and pricing.`,
      impact: 'high',
      confidence: 90,
      actionable: true,
      category: 'marketing',
      data: { conversionRate, benchmarkConversionRate, views, downloads },
      createdAt: new Date(),
    };
  } else if (conversionRate > benchmarkConversionRate * 1.3) {
    return {
      type: 'recommendation',
      title: 'High-Performing Content',
      description: `Excellent conversion rate (${conversionRate.toFixed(1)}%)! Consider increasing marketing spend to drive more traffic to these high-converting games.`,
      impact: 'medium',
      confidence: 85,
      actionable: true,
      category: 'marketing',
      data: { conversionRate, benchmarkConversionRate, views, downloads },
      createdAt: new Date(),
    };
  }

  return null;
}

function analyzeAudienceEngagement(analyticsEvents: any[]): MarketingInsight | null {
  const likes = analyticsEvents.filter(e => e.type === 'game_like').length;
  const views = analyticsEvents.filter(e => e.type === 'game_view').length;
  
  if (views === 0) return null;

  const engagementRate = (likes / views) * 100;
  const benchmarkEngagementRate = 8.0;

  if (engagementRate < benchmarkEngagementRate * 0.6) {
    return {
      type: 'recommendation',
      title: 'Improve Audience Engagement',
      description: `Low engagement rate (${engagementRate.toFixed(1)}%). Consider improving game trailers, adding more interactive elements, or engaging with your community more actively.`,
      impact: 'medium',
      confidence: 80,
      actionable: true,
      category: 'audience',
      data: { engagementRate, benchmarkEngagementRate, likes, views },
      createdAt: new Date(),
    };
  }

  return null;
}

function analyzeMarketTrends(games: any[], platformMetrics: any[]): MarketingInsight | null {
  // Analyze genre trends
  const genres = games.flatMap(game => game.details.genre);
  const genreCount = genres.reduce((acc, genre) => {
    acc[genre] = (acc[genre] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const trendingGenres = ['Adventure', 'Puzzle', 'Simulation']; // Mock trending data
  const yourGenres = Object.keys(genreCount);
  const missingTrends = trendingGenres.filter(genre => !yourGenres.includes(genre));

  if (missingTrends.length > 0) {
    return {
      type: 'trend',
      title: 'Market Trend Opportunity',
      description: `Trending genres you're not exploring: ${missingTrends.join(', ')}. Consider developing games in these categories to capitalize on market demand.`,
      impact: 'medium',
      confidence: 70,
      actionable: true,
      category: 'content',
      data: { missingTrends, yourGenres, trendingGenres },
      createdAt: new Date(),
    };
  }

  return null;
}

function analyzeCompetition(games: any[], platformMetrics: any[]): MarketingInsight | null {
  // Mock competitive analysis
  return {
    type: 'recommendation',
    title: 'Competitive Positioning',
    description: 'Your games are well-positioned in the market. Consider highlighting unique features more prominently in your marketing materials to differentiate from competitors.',
    impact: 'medium',
    confidence: 75,
    actionable: true,
    category: 'competition',
    data: {},
    createdAt: new Date(),
  };
}

function analyzeContentPerformance(games: any[], analyticsEvents: any[]): MarketingInsight | null {
  const topPerformingGame = games.reduce((top, game) => 
    game.stats.downloads > (top?.stats.downloads || 0) ? game : top, null);

  if (topPerformingGame) {
    return {
      type: 'recommendation',
      title: 'Content Success Pattern',
      description: `"${topPerformingGame.title}" is your top performer. Analyze its success factors (genre: ${topPerformingGame.details.genre.join(', ')}, price: $${topPerformingGame.pricing.price}) and apply similar strategies to other games.`,
      impact: 'high',
      confidence: 85,
      actionable: true,
      category: 'content',
      data: { topPerformingGame },
      createdAt: new Date(),
    };
  }

  return null;
}

async function generateRecommendations(data: any, insights: MarketingInsight[]) {
  const immediate = [
    'Update game descriptions with more compelling copy',
    'Optimize screenshot selection for better conversion',
    'Review and adjust pricing strategy based on market data',
  ];

  const shortTerm = [
    'Develop content for trending game genres',
    'Implement A/B testing for game store pages',
    'Create targeted marketing campaigns for high-performing games',
  ];

  const longTerm = [
    'Build a community around your most successful games',
    'Explore partnerships with other developers',
    'Consider expanding to new platforms based on audience data',
  ];

  return { immediate, shortTerm, longTerm };
}

async function generatePredictions(data: any) {
  // Mock predictions based on current trends
  return {
    revenueGrowth: 15 + Math.random() * 20, // 15-35% growth prediction
    downloadGrowth: 10 + Math.random() * 15, // 10-25% growth prediction
    marketTrends: [
      'Increased demand for puzzle games',
      'Growing mobile gaming market',
      'Rising interest in indie titles',
    ],
  };
}

function getTimeRangeStart(timeRange: string): Date {
  const now = new Date();
  switch (timeRange) {
    case '7days':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30days':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    case '90days':
      return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
}
