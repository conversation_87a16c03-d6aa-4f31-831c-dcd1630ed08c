// Test Firebase connectivity
import type { NextApiRequest, NextApiResponse } from 'next';
import { auth, firestore } from '../../lib/firebase';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Test Firebase Auth
    const authTest = auth.app.name;
    
    // Test Firestore
    const testDoc = await firestore.collection('test').limit(1).get();
    
    res.status(200).json({
      success: true,
      message: 'Firebase connection successful',
      auth: authTest,
      firestore: 'Connected',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('Firebase test error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
