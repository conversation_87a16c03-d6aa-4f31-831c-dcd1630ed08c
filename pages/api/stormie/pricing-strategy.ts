import type { NextApiRequest, NextApiResponse } from 'next';
import { HfInference } from '@huggingface/inference';

const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

interface GameData {
  title: string;
  description?: string;
  genre?: string;
  platform?: string;
  targetAudience?: string;
  features?: string[];
  developmentCost?: number;
  targetRevenue?: number;
}

interface PricingResponse {
  success: boolean;
  pricingStrategy?: {
    recommended: {
      strategy: string;
      price: string;
      reasoning: string;
      expectedRevenue: string;
    };
    alternatives: Array<{
      strategy: string;
      price: string;
      pros: string[];
      cons: string[];
    }>;
    monetization: {
      model: string;
      recommendations: string[];
      inAppPurchases?: string[];
    };
    testing: {
      approach: string;
      pricePoints: string[];
      duration: string;
    };
    timeline: Array<{
      phase: string;
      duration: string;
      actions: string[];
    }>;
  };
  error?: string;
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PricingResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { gameData, marketData }: { gameData: GameData; marketData?: any } = req.body;

    if (!gameData || !gameData.title) {
      return res.status(400).json({
        success: false,
        error: 'Game title is required',
        timestamp: new Date().toISOString()
      });
    }

    const pricingStrategy = await optimizePricingStrategy(gameData, marketData);
    
    res.status(200).json({
      success: true,
      pricingStrategy,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Pricing strategy error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to optimize pricing strategy',
      timestamp: new Date().toISOString()
    });
  }
}

async function optimizePricingStrategy(gameData: GameData, marketData?: any) {
  const prompt = `Analyze and recommend pricing strategy for this game:

Game: ${gameData.title}
Genre: ${gameData.genre || 'Mobile Game'}
Platform: ${gameData.platform || 'Mobile'}
Target Audience: ${gameData.targetAudience || 'Mobile gamers'}
Key Features: ${gameData.features?.join(', ') || 'Standard game features'}

Consider:
1. Genre-specific pricing trends
2. Platform monetization best practices
3. Target audience spending behavior
4. Competitive landscape
5. Monetization model options (premium, freemium, subscription)

Provide comprehensive pricing recommendations with reasoning.`;

  try {
    const response = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: prompt,
      parameters: {
        max_new_tokens: 400,
        temperature: 0.7,
        return_full_text: false
      }
    });

    return parsePricingResponse(response.generated_text, gameData);
  } catch (error) {
    console.error('AI pricing analysis failed:', error);
    return generateFallbackPricingStrategy(gameData);
  }
}

function parsePricingResponse(aiText: string, gameData: GameData) {
  try {
    // Extract pricing insights from AI response
    const recommended = extractRecommendedStrategy(aiText, gameData);
    const alternatives = generateAlternativeStrategies(gameData);
    const monetization = analyzeMonetizationModel(gameData);
    const testing = createPricingTestPlan(gameData);
    const timeline = createPricingTimeline(gameData);

    return {
      recommended,
      alternatives,
      monetization,
      testing,
      timeline
    };
  } catch (error) {
    return generateFallbackPricingStrategy(gameData);
  }
}

function generateFallbackPricingStrategy(gameData: GameData) {
  const recommended = getRecommendedStrategy(gameData);
  const alternatives = generateAlternativeStrategies(gameData);
  const monetization = analyzeMonetizationModel(gameData);
  const testing = createPricingTestPlan(gameData);
  const timeline = createPricingTimeline(gameData);

  return {
    recommended,
    alternatives,
    monetization,
    testing,
    timeline
  };
}

function getRecommendedStrategy(gameData: GameData) {
  const genrePricing = getGenrePricingInsights(gameData.genre);
  const platformPricing = getPlatformPricingInsights(gameData.platform);
  
  // Determine best strategy based on genre and platform
  let strategy = 'freemium';
  let price = 'Free with IAP';
  let reasoning = 'Freemium model recommended for broad market reach';
  
  if (gameData.genre?.toLowerCase().includes('premium') || 
      gameData.features?.some(f => f.toLowerCase().includes('premium'))) {
    strategy = 'premium';
    price = genrePricing.premiumPrice;
    reasoning = 'Premium pricing justified by high-quality features and target audience';
  } else if (gameData.platform?.toLowerCase().includes('console')) {
    strategy = 'premium';
    price = '$19.99 - $29.99';
    reasoning = 'Console players expect premium pricing for quality games';
  }

  return {
    strategy,
    price,
    reasoning,
    expectedRevenue: calculateExpectedRevenue(strategy, price, gameData)
  };
}

function generateAlternativeStrategies(gameData: GameData) {
  const strategies = [
    {
      strategy: 'Freemium',
      price: 'Free with In-App Purchases',
      pros: [
        'Low barrier to entry',
        'Large potential user base',
        'Scalable revenue through IAP',
        'Viral growth potential'
      ],
      cons: [
        'Lower conversion rates',
        'Requires engaging progression system',
        'Competition with other free games',
        'Need for continuous content updates'
      ]
    },
    {
      strategy: 'Premium',
      price: getGenrePricingInsights(gameData.genre).premiumPrice,
      pros: [
        'Immediate revenue per download',
        'Higher revenue per user',
        'No need for IAP integration',
        'Better user experience without ads'
      ],
      cons: [
        'Higher barrier to entry',
        'Smaller potential user base',
        'Need for strong marketing',
        'Price sensitivity in mobile market'
      ]
    },
    {
      strategy: 'Subscription',
      price: '$2.99 - $9.99/month',
      pros: [
        'Recurring revenue stream',
        'Predictable income',
        'High lifetime value',
        'Continuous engagement'
      ],
      cons: [
        'Subscription fatigue',
        'Need for regular content updates',
        'Higher churn risk',
        'Complex retention strategies'
      ]
    },
    {
      strategy: 'Ad-Supported',
      price: 'Free with Advertisements',
      pros: [
        'No user payment required',
        'Large user base potential',
        'Multiple revenue streams',
        'Low conversion friction'
      ],
      cons: [
        'Lower revenue per user',
        'User experience impact',
        'Dependence on ad networks',
        'Need for high user volume'
      ]
    }
  ];

  return strategies;
}

function analyzeMonetizationModel(gameData: GameData) {
  const genre = gameData.genre?.toLowerCase() || '';
  
  if (genre.includes('puzzle') || genre.includes('casual')) {
    return {
      model: 'Freemium with IAP',
      recommendations: [
        'Offer hint systems and power-ups as IAP',
        'Create level packs for purchase',
        'Implement cosmetic customizations',
        'Add ad removal option'
      ],
      inAppPurchases: [
        'Hint packages ($0.99 - $2.99)',
        'Power-up bundles ($1.99 - $4.99)',
        'Level pack expansions ($2.99 - $7.99)',
        'Ad removal ($3.99)',
        'Premium themes ($1.99 - $3.99)'
      ]
    };
  } else if (genre.includes('rpg') || genre.includes('strategy')) {
    return {
      model: 'Freemium with Premium Features',
      recommendations: [
        'Offer character/equipment upgrades',
        'Create premium content areas',
        'Implement battle passes',
        'Add convenience features'
      ],
      inAppPurchases: [
        'Character upgrades ($2.99 - $9.99)',
        'Premium areas access ($4.99 - $14.99)',
        'Battle pass ($9.99/season)',
        'Inventory expansion ($1.99 - $4.99)',
        'Experience boosters ($0.99 - $2.99)'
      ]
    };
  } else {
    return {
      model: 'Hybrid Monetization',
      recommendations: [
        'Combine multiple revenue streams',
        'Test different monetization approaches',
        'Focus on user experience balance',
        'Implement gradual monetization introduction'
      ],
      inAppPurchases: [
        'Premium features ($2.99 - $7.99)',
        'Cosmetic items ($0.99 - $4.99)',
        'Convenience upgrades ($1.99 - $5.99)',
        'Content expansions ($3.99 - $12.99)'
      ]
    };
  }
}

function createPricingTestPlan(gameData: GameData) {
  return {
    approach: 'A/B Testing with Gradual Rollout',
    pricePoints: generateTestPricePoints(gameData),
    duration: '4-6 weeks per test phase'
  };
}

function generateTestPricePoints(gameData: GameData): string[] {
  const basePricing = getGenrePricingInsights(gameData.genre);
  
  return [
    'Free (baseline)',
    basePricing.lowPrice,
    basePricing.midPrice,
    basePricing.premiumPrice,
    'Freemium with $0.99 starter pack'
  ];
}

function createPricingTimeline(gameData: GameData) {
  return [
    {
      phase: 'Pre-Launch',
      duration: '2-4 weeks',
      actions: [
        'Conduct market research',
        'Analyze competitor pricing',
        'Set initial pricing strategy',
        'Prepare A/B testing framework'
      ]
    },
    {
      phase: 'Soft Launch',
      duration: '2-3 weeks',
      actions: [
        'Test initial pricing in limited markets',
        'Gather user feedback',
        'Monitor conversion rates',
        'Adjust pricing based on data'
      ]
    },
    {
      phase: 'Global Launch',
      duration: '1-2 weeks',
      actions: [
        'Implement optimized pricing',
        'Monitor performance metrics',
        'Prepare for post-launch adjustments',
        'Set up ongoing testing'
      ]
    },
    {
      phase: 'Post-Launch Optimization',
      duration: 'Ongoing',
      actions: [
        'Continuous A/B testing',
        'Seasonal pricing adjustments',
        'Monitor market changes',
        'Optimize based on user behavior'
      ]
    }
  ];
}

function getGenrePricingInsights(genre?: string) {
  const pricingMap: { [key: string]: { lowPrice: string; midPrice: string; premiumPrice: string } } = {
    'action': { lowPrice: '$1.99', midPrice: '$4.99', premiumPrice: '$9.99' },
    'puzzle': { lowPrice: '$0.99', midPrice: '$2.99', premiumPrice: '$4.99' },
    'strategy': { lowPrice: '$2.99', midPrice: '$7.99', premiumPrice: '$14.99' },
    'rpg': { lowPrice: '$3.99', midPrice: '$9.99', premiumPrice: '$19.99' },
    'casual': { lowPrice: '$0.99', midPrice: '$1.99', premiumPrice: '$2.99' },
    'simulation': { lowPrice: '$4.99', midPrice: '$12.99', premiumPrice: '$24.99' },
    'racing': { lowPrice: '$1.99', midPrice: '$4.99', premiumPrice: '$9.99' }
  };

  return pricingMap[genre?.toLowerCase() || 'action'] || pricingMap.action;
}

function getPlatformPricingInsights(platform?: string) {
  const platformMap: { [key: string]: { avgPrice: string; range: string } } = {
    'mobile': { avgPrice: '$2.99', range: '$0.99 - $9.99' },
    'pc': { avgPrice: '$14.99', range: '$4.99 - $29.99' },
    'console': { avgPrice: '$24.99', range: '$9.99 - $59.99' },
    'web': { avgPrice: 'Free', range: 'Free - $4.99' }
  };

  return platformMap[platform?.toLowerCase() || 'mobile'] || platformMap.mobile;
}

function calculateExpectedRevenue(strategy: string, price: string, gameData: GameData): string {
  // Simplified revenue calculation based on strategy
  const baseUsers = 10000; // Estimated user base
  
  if (strategy === 'freemium') {
    const conversionRate = 0.02; // 2% conversion rate
    const avgSpend = 5.99;
    const revenue = baseUsers * conversionRate * avgSpend;
    return `$${revenue.toLocaleString()} (estimated monthly)`;
  } else if (strategy === 'premium') {
    const conversionRate = 0.005; // 0.5% conversion rate for premium
    const priceNum = parseFloat(price.replace(/[^0-9.]/g, '')) || 4.99;
    const revenue = baseUsers * conversionRate * priceNum;
    return `$${revenue.toLocaleString()} (estimated monthly)`;
  } else {
    return '$2,000 - $15,000 (estimated monthly range)';
  }
}

function extractRecommendedStrategy(aiText: string, gameData: GameData) {
  // Try to extract strategy from AI response, fallback to generated strategy
  const hasFreemium = aiText.toLowerCase().includes('freemium') || aiText.toLowerCase().includes('free');
  const hasPremium = aiText.toLowerCase().includes('premium') || aiText.toLowerCase().includes('paid');
  
  if (hasFreemium && !hasPremium) {
    return {
      strategy: 'Freemium',
      price: 'Free with In-App Purchases',
      reasoning: 'AI analysis suggests freemium model for maximum market penetration',
      expectedRevenue: calculateExpectedRevenue('freemium', 'Free', gameData)
    };
  } else if (hasPremium) {
    const genrePricing = getGenrePricingInsights(gameData.genre);
    return {
      strategy: 'Premium',
      price: genrePricing.midPrice,
      reasoning: 'AI analysis indicates premium pricing is viable for this game',
      expectedRevenue: calculateExpectedRevenue('premium', genrePricing.midPrice, gameData)
    };
  } else {
    return getRecommendedStrategy(gameData);
  }
}
