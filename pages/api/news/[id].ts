import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';
import { NewsArticle, ApiResponse } from '../../../types/database';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<NewsArticle>>
) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'News article ID is required',
    });
  }

  try {
    if (req.method === 'GET') {
      return await getNewsArticle(id, req, res);
    } else if (req.method === 'PUT') {
      return await updateNewsArticle(id, req, res);
    } else if (req.method === 'DELETE') {
      return await deleteNewsArticle(id, req, res);
    } else {
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('News API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
}

async function getNewsArticle(
  id: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<NewsArticle>>
) {
  try {
    const doc = await adminDb.collection('news').doc(id).get();

    if (!doc.exists) {
      return res.status(404).json({
        success: false,
        error: 'News article not found',
      });
    }

    const data = doc.data();
    const article: NewsArticle = {
      id: doc.id,
      ...data,
      createdAt: data?.createdAt?.toDate() || new Date(),
      updatedAt: data?.updatedAt?.toDate() || new Date(),
      publishedAt: data?.publishedAt?.toDate(),
    } as NewsArticle;

    // Increment view count
    await adminDb.collection('news').doc(id).update({
      'stats.views': (article.stats.views || 0) + 1,
      updatedAt: new Date(),
    });

    return res.status(200).json({
      success: true,
      data: article,
    });
  } catch (error) {
    console.error('Get news article error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch news article',
    });
  }
}

async function updateNewsArticle(
  id: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<NewsArticle>>
) {
  try {
    const updateData = req.body;

    // Add update timestamp
    updateData.updatedAt = new Date();

    // If publishing the article, add publication timestamp
    if (updateData.status === 'published' && !updateData.publishedAt) {
      updateData.publishedAt = new Date();
    }

    await adminDb.collection('news').doc(id).update(updateData);

    // Fetch updated article
    const doc = await adminDb.collection('news').doc(id).get();
    const data = doc.data();
    const article: NewsArticle = {
      id: doc.id,
      ...data,
      createdAt: data?.createdAt?.toDate() || new Date(),
      updatedAt: data?.updatedAt?.toDate() || new Date(),
      publishedAt: data?.publishedAt?.toDate(),
    } as NewsArticle;

    return res.status(200).json({
      success: true,
      data: article,
      message: 'News article updated successfully',
    });
  } catch (error) {
    console.error('Update news article error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to update news article',
    });
  }
}

async function deleteNewsArticle(
  id: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<NewsArticle>>
) {
  try {
    // Check if article exists
    const doc = await adminDb.collection('news').doc(id).get();
    if (!doc.exists) {
      return res.status(404).json({
        success: false,
        error: 'News article not found',
      });
    }

    // Delete the article
    await adminDb.collection('news').doc(id).delete();

    return res.status(200).json({
      success: true,
      message: 'News article deleted successfully',
    });
  } catch (error) {
    console.error('Delete news article error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete news article',
    });
  }
}
