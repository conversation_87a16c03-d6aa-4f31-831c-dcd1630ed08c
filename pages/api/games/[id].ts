import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';
import { Game, ApiResponse } from '../../../types/database';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Game>>
) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Game ID is required',
    });
  }

  try {
    if (req.method === 'GET') {
      return await getGame(id, req, res);
    } else if (req.method === 'PUT') {
      return await updateGame(id, req, res);
    } else if (req.method === 'DELETE') {
      return await deleteGame(id, req, res);
    } else {
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Game API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
}

async function getGame(
  id: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Game>>
) {
  try {
    const doc = await adminDb.collection('games').doc(id).get();

    if (!doc.exists) {
      return res.status(404).json({
        success: false,
        error: 'Game not found',
      });
    }

    const data = doc.data();
    const game: Game = {
      id: doc.id,
      ...data,
      createdAt: data?.createdAt?.toDate() || new Date(),
      updatedAt: data?.updatedAt?.toDate() || new Date(),
      approvedAt: data?.approvedAt?.toDate(),
    } as Game;

    // Increment view count
    await adminDb.collection('games').doc(id).update({
      'stats.views': (game.stats.views || 0) + 1,
      updatedAt: new Date(),
    });

    return res.status(200).json({
      success: true,
      data: game,
    });
  } catch (error) {
    console.error('Get game error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch game',
    });
  }
}

async function updateGame(
  id: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Game>>
) {
  try {
    const updateData = req.body;

    // Add update timestamp
    updateData.updatedAt = new Date();

    // If approving the game, add approval timestamp
    if (updateData.status === 'approved' && updateData.approvedBy) {
      updateData.approvedAt = new Date();
    }

    await adminDb.collection('games').doc(id).update(updateData);

    // Fetch updated game
    const doc = await adminDb.collection('games').doc(id).get();
    const data = doc.data();
    const game: Game = {
      id: doc.id,
      ...data,
      createdAt: data?.createdAt?.toDate() || new Date(),
      updatedAt: data?.updatedAt?.toDate() || new Date(),
      approvedAt: data?.approvedAt?.toDate(),
    } as Game;

    return res.status(200).json({
      success: true,
      data: game,
      message: 'Game updated successfully',
    });
  } catch (error) {
    console.error('Update game error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to update game',
    });
  }
}

async function deleteGame(
  id: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Game>>
) {
  try {
    // Check if game exists
    const doc = await adminDb.collection('games').doc(id).get();
    if (!doc.exists) {
      return res.status(404).json({
        success: false,
        error: 'Game not found',
      });
    }

    // Delete the game
    await adminDb.collection('games').doc(id).delete();

    return res.status(200).json({
      success: true,
      message: 'Game deleted successfully',
    });
  } catch (error) {
    console.error('Delete game error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete game',
    });
  }
}
