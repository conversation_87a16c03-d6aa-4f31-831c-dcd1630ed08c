import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'billing' | 'general' | 'feature-request' | 'bug-report';
  developerId: string;
  developerName: string;
  developerEmail: string;
  adminResponse?: string;
  adminId?: string;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  tags: string[];
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
  }>;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === 'GET') {
      return await getTickets(req, res);
    } else if (req.method === 'POST') {
      return await createTicket(req, res);
    } else if (req.method === 'PUT') {
      return await updateTicket(req, res);
    } else {
      res.setHeader('Allow', ['GET', 'POST', 'PUT']);
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Support tickets API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
}

async function getTickets(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { developerId, status, priority, limit = '10' } = req.query;

    let query = adminDb.collection('supportTickets');

    // Apply filters
    if (developerId) {
      query = query.where('developerId', '==', developerId);
    }

    if (status) {
      query = query.where('status', '==', status);
    }

    if (priority) {
      query = query.where('priority', '==', priority);
    }

    // Order by creation date (newest first)
    query = query.orderBy('createdAt', 'desc');

    // Apply limit
    query = query.limit(parseInt(limit as string));

    const snapshot = await query.get();

    const tickets: SupportTicket[] = [];
    snapshot.forEach((doc) => {
      const data = doc.data();
      tickets.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        resolvedAt: data.resolvedAt?.toDate(),
      } as SupportTicket);
    });

    return res.status(200).json({
      success: true,
      data: tickets,
    });
  } catch (error) {
    console.error('Get tickets error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch support tickets',
    });
  }
}

async function createTicket(req: NextApiRequest, res: NextApiResponse) {
  try {
    const {
      title,
      description,
      priority = 'medium',
      category = 'general',
      developerId,
      developerName,
      developerEmail,
      tags = [],
    } = req.body;

    // Validate required fields
    if (!title || !description || !developerId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: title, description, developerId',
      });
    }

    // Create the ticket
    const ticketData = {
      title,
      description,
      status: 'open',
      priority,
      category,
      developerId,
      developerName: developerName || 'Unknown Developer',
      developerEmail: developerEmail || '',
      tags,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const docRef = await adminDb.collection('supportTickets').add(ticketData);

    // Create notification for admins
    await adminDb.collection('adminNotifications').add({
      type: 'new_support_ticket',
      title: 'New Support Ticket',
      message: `New ${priority} priority ticket: ${title}`,
      ticketId: docRef.id,
      developerId,
      read: false,
      createdAt: new Date(),
    });

    // Auto-assign based on category and priority
    const assignedAdmin = await autoAssignTicket(category, priority);
    if (assignedAdmin) {
      await adminDb.collection('supportTickets').doc(docRef.id).update({
        assignedTo: assignedAdmin.id,
        assignedAt: new Date(),
      });
    }

    return res.status(201).json({
      success: true,
      data: {
        id: docRef.id,
        ...ticketData,
      },
      message: 'Support ticket created successfully',
    });
  } catch (error) {
    console.error('Create ticket error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to create support ticket',
    });
  }
}

async function updateTicket(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { ticketId } = req.query;
    const updateData = req.body;

    if (!ticketId) {
      return res.status(400).json({
        success: false,
        error: 'Ticket ID is required',
      });
    }

    // Add update timestamp
    updateData.updatedAt = new Date();

    // If resolving the ticket, add resolution timestamp
    if (updateData.status === 'resolved' && !updateData.resolvedAt) {
      updateData.resolvedAt = new Date();
    }

    await adminDb.collection('supportTickets').doc(ticketId as string).update(updateData);

    // Create notification for developer if admin responded
    if (updateData.adminResponse) {
      const ticketDoc = await adminDb.collection('supportTickets').doc(ticketId as string).get();
      const ticketData = ticketDoc.data();

      if (ticketData) {
        await adminDb.collection('notifications').add({
          userId: ticketData.developerId,
          type: 'support_response',
          title: 'Support Ticket Update',
          message: `Your support ticket "${ticketData.title}" has been updated`,
          ticketId: ticketId,
          read: false,
          createdAt: new Date(),
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Support ticket updated successfully',
    });
  } catch (error) {
    console.error('Update ticket error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to update support ticket',
    });
  }
}

async function autoAssignTicket(category: string, priority: string) {
  try {
    // Get available admin users
    const adminsSnapshot = await adminDb
      .collection('adminUsers')
      .where('isActive', '==', true)
      .where('specialties', 'array-contains', category)
      .limit(1)
      .get();

    if (!adminsSnapshot.empty) {
      const adminDoc = adminsSnapshot.docs[0];
      return {
        id: adminDoc.id,
        ...adminDoc.data(),
      };
    }

    // Fallback to any available admin
    const fallbackSnapshot = await adminDb
      .collection('adminUsers')
      .where('isActive', '==', true)
      .limit(1)
      .get();

    if (!fallbackSnapshot.empty) {
      const adminDoc = fallbackSnapshot.docs[0];
      return {
        id: adminDoc.id,
        ...adminDoc.data(),
      };
    }

    return null;
  } catch (error) {
    console.error('Auto-assign error:', error);
    return null;
  }
}
