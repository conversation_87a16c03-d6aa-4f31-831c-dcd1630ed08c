import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  useTheme,
  alpha,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ForumIcon from '@mui/icons-material/Forum';
import ArticleIcon from '@mui/icons-material/Article';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import { toast } from 'react-toastify';
import Link from 'next/link';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(6),
  maxWidth: '800px',
}));

const SupportCard = styled(Card)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: 70,
  height: 70,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& svg': {
    fontSize: 35,
    color: theme.palette.primary.main,
  },
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: `${theme.shape.borderRadius}px !important`,
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  '&.Mui-expanded': {
    margin: theme.spacing(1, 0),
  },
  marginBottom: theme.spacing(2),
  overflow: 'hidden',
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  '& .MuiAccordionSummary-content': {
    margin: theme.spacing(1, 0),
  },
}));

const SearchBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  borderRadius: theme.shape.borderRadius * 2,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  padding: theme.spacing(1, 2),
  marginBottom: theme.spacing(6),
}));

// Sample FAQs
const faqs = [
  {
    question: 'How do I create an account?',
    answer: 'To create an account, click on the "Get Started" button in the top right corner of the homepage. Follow the prompts to enter your email, create a password, and set up your profile. You\'ll receive a verification email to confirm your account.',
  },
  {
    question: 'How do I connect my wallet?',
    answer: 'After logging in, go to your account settings and click on "Connect Wallet". We support multiple wallet providers including MetaMask, WalletConnect, and Coinbase Wallet. Follow the prompts to authorize the connection between your wallet and Gamestorme.',
  },
  {
    question: 'What are pNFTs?',
    answer: 'Playable Non-Fungible Tokens (pNFTs) are unique digital assets that exist on the blockchain and can be used across multiple games within the Gamestorme ecosystem. Unlike traditional in-game items, pNFTs offer true ownership and can be traded on our marketplace.',
  },
  {
    question: 'How do I earn rewards?',
    answer: 'You can earn rewards through various activities on our platform: playing games, participating in tournaments, completing quests, staking tokens, and contributing to the community. Rewards can include tokens, pNFTs, and exclusive in-game items.',
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept various payment methods including credit/debit cards, PayPal, and cryptocurrencies such as ETH, BTC, and our native GSTR token. Payment options may vary depending on your region.',
  },
];

// Support options
const supportOptions = [
  {
    title: 'Help Center',
    description: 'Browse our comprehensive knowledge base for answers to common questions and detailed guides.',
    icon: <ArticleIcon />,
    link: '/docs',
  },
  {
    title: 'Community Forum',
    description: 'Connect with other users to discuss issues, share solutions, and exchange ideas.',
    icon: <ForumIcon />,
    link: '/community',
  },
  {
    title: 'Live Support',
    description: 'Chat with our support team in real-time for immediate assistance with urgent issues.',
    icon: <SupportAgentIcon />,
    link: '/live-support',
  },
];

const Support: React.FC = () => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    
    // Simulate search delay
    setTimeout(() => {
      setLoading(false);
      toast.info(`Search results for "${searchQuery}" will be displayed here.`);
    }, 1000);
  };

  return (
    <Layout>
      <PageHeader
        title="Support &"
        highlightedTitle="Help"
        description="Find answers to your questions and get the help you need to make the most of Gamestorme."
      />

      {/* Search Section */}
      <SectionContainer>
        <Container maxWidth="md">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              How can we help you?
            </Typography>
            <Typography variant="body1" color="text.secondary" textAlign="center" paragraph sx={{ mb: 4 }}>
              Search our knowledge base for answers or browse the frequently asked questions below.
            </Typography>
            
            <Box component="form" onSubmit={handleSearch}>
              <SearchBox>
                <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
                <TextField
                  fullWidth
                  placeholder="Search for help articles..."
                  variant="standard"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    disableUnderline: true,
                  }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={loading}
                  sx={{ ml: 2 }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : 'Search'}
                </Button>
              </SearchBox>
            </Box>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* FAQ Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.5) }}>
        <Container maxWidth="md">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Frequently Asked Questions</SectionTitle>
            <SectionSubtitle variant="h6">
              Find quick answers to common questions about Gamestorme.
            </SectionSubtitle>

            <Box>
              {faqs.map((faq, index) => (
                <motion.div key={index} variants={itemVariants}>
                  <StyledAccordion>
                    <StyledAccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls={`panel${index}a-content`}
                      id={`panel${index}a-header`}
                    >
                      <Typography variant="h6" fontWeight="600">
                        <HelpOutlineIcon sx={{ fontSize: 20, verticalAlign: 'middle', mr: 1, color: theme.palette.primary.main }} />
                        {faq.question}
                      </Typography>
                    </StyledAccordionSummary>
                    <AccordionDetails sx={{ p: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        {faq.answer}
                      </Typography>
                    </AccordionDetails>
                  </StyledAccordion>
                </motion.div>
              ))}
            </Box>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Support Options Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Support Options</SectionTitle>
            <SectionSubtitle variant="h6">
              Choose the support option that works best for you.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {supportOptions.map((option, index) => (
                <Grid item xs={12} md={4} key={index}>
                  <motion.div variants={itemVariants}>
                    <SupportCard>
                      <IconWrapper>
                        {option.icon}
                      </IconWrapper>
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        {option.title}
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                        {option.description}
                      </Typography>
                      <Button
                        variant="contained"
                        color="primary"
                        component={Link}
                        href={option.link}
                      >
                        Go to {option.title}
                      </Button>
                    </SupportCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Support;
