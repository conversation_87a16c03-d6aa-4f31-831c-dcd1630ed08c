import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>,
  CircularProgress,
  Chip,
  Grid,
} from '@mui/material';

const TestStormie: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  const testGameData = {
    title: 'Cosmic Adventure',
    genre: 'Space Exploration',
    description: 'An epic space adventure game with stunning visuals and immersive gameplay',
    platform: 'PC, Mobile',
    targetAudience: 'Core gamers aged 18-35',
    features: ['Multiplayer', 'Open World', 'Customization', 'Story Mode']
  };

  const runTests = async () => {
    setLoading(true);
    setError(null);
    setResults([]);

    const tests = [
      {
        name: 'Game Analysis',
        endpoint: '/api/stormie/analyze-game',
        data: { gameData: testGameData }
      },
      {
        name: 'ASO Keywords',
        endpoint: '/api/stormie/aso-keywords',
        data: { gameData: testGameData }
      },
      {
        name: 'Social Content',
        endpoint: '/api/stormie/social-content',
        data: { gameData: testGameData, platform: 'twitter', contentType: 'post' }
      },
      {
        name: 'Pricing Strategy',
        endpoint: '/api/stormie/pricing-strategy',
        data: { gameData: testGameData }
      },
      {
        name: 'Market Insights',
        endpoint: '/api/stormie/market-insights',
        data: { gameData: testGameData }
      }
    ];

    const testResults = [];

    for (const test of tests) {
      try {
        const response = await fetch(test.endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(test.data),
        });

        const data = await response.json();
        
        testResults.push({
          name: test.name,
          success: response.ok,
          data: data,
          status: response.status
        });
      } catch (err) {
        testResults.push({
          name: test.name,
          success: false,
          error: err instanceof Error ? err.message : 'Unknown error',
          status: 'Error'
        });
      }
    }

    setResults(testResults);
    setLoading(false);
  };

  const successCount = results.filter(r => r.success).length;
  const totalTests = results.length;

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" fontWeight="bold" gutterBottom>
          🧠 Stormie AI Test Suite
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Testing GameStorme AI Marketing Platform
        </Typography>
        
        <Button
          variant="contained"
          size="large"
          onClick={runTests}
          disabled={loading}
          sx={{ mt: 2, px: 4, py: 1.5 }}
        >
          {loading ? <CircularProgress size={24} sx={{ mr: 1 }} /> : '🚀'} 
          {loading ? 'Running Tests...' : 'Run All Tests'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {results.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Alert 
            severity={successCount === totalTests ? 'success' : successCount > 0 ? 'warning' : 'error'}
            sx={{ mb: 3 }}
          >
            <Typography variant="h6">
              Test Results: {successCount}/{totalTests} Passed
            </Typography>
            {successCount === totalTests ? (
              <Typography>🎉 All Stormie AI endpoints are working perfectly!</Typography>
            ) : successCount > 0 ? (
              <Typography>⚠️ Some tests passed. Check individual results below.</Typography>
            ) : (
              <Typography>❌ All tests failed. Check API endpoints and dependencies.</Typography>
            )}
          </Alert>

          <Grid container spacing={3}>
            {results.map((result, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card 
                  sx={{ 
                    height: '100%',
                    border: result.success ? '2px solid #4caf50' : '2px solid #f44336'
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {result.name}
                      </Typography>
                      <Chip 
                        label={result.success ? 'PASS' : 'FAIL'}
                        color={result.success ? 'success' : 'error'}
                        variant="filled"
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Status: {result.status}
                    </Typography>

                    {result.success ? (
                      <Box>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          ✅ API endpoint is working correctly
                        </Typography>
                        {result.data?.analysis && (
                          <Typography variant="caption" display="block">
                            Analysis generated successfully
                          </Typography>
                        )}
                        {result.data?.keywords && (
                          <Typography variant="caption" display="block">
                            Keywords: {result.data.keywords.primary?.slice(0, 3).join(', ')}
                          </Typography>
                        )}
                        {result.data?.content && (
                          <Typography variant="caption" display="block">
                            Content generated for {result.data.content.platform}
                          </Typography>
                        )}
                        {result.data?.pricingStrategy && (
                          <Typography variant="caption" display="block">
                            Strategy: {result.data.pricingStrategy.recommended?.strategy}
                          </Typography>
                        )}
                        {result.data?.insights && (
                          <Typography variant="caption" display="block">
                            Market insights generated
                          </Typography>
                        )}
                      </Box>
                    ) : (
                      <Box>
                        <Typography variant="body2" color="error" sx={{ mb: 1 }}>
                          ❌ {result.error || 'API endpoint failed'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Check console for detailed error information
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          🎮 Next Steps:
        </Typography>
        <Typography variant="body2" paragraph>
          1. If tests pass: Navigate to <strong>/developer/dashboard</strong> and click "Marketing AI"
        </Typography>
        <Typography variant="body2" paragraph>
          2. Test Stormie Chat interface with real game data
        </Typography>
        <Typography variant="body2" paragraph>
          3. Try different AI features: Analysis, Keywords, Social Content, Pricing, Insights
        </Typography>
        <Typography variant="body2">
          4. For production: Add a valid Hugging Face API key to .env.local
        </Typography>
      </Box>
    </Container>
  );
};

export default TestStormie;
