import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Grid,
  Typography,
  Button,
  Chip,
  useTheme,
  alpha,
  Rating,
  Divider,
  Card,
  CardContent,
  CardMedia,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
  Alert,
  Tab,
  Tabs,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../../components/layout/Layout';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Game } from '../../types/database';
import DownloadIcon from '@mui/icons-material/Download';
import StarIcon from '@mui/icons-material/Star';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PersonIcon from '@mui/icons-material/Person';
import DevicesIcon from '@mui/icons-material/Devices';
import CategoryIcon from '@mui/icons-material/Category';
import LanguageIcon from '@mui/icons-material/Language';
import StorageIcon from '@mui/icons-material/Storage';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';

const GameDetailContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const HeroSection = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '60vh',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  marginBottom: theme.spacing(4),
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
}));

const HeroOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)',
  display: 'flex',
  alignItems: 'flex-end',
  padding: theme.spacing(4),
}));

const GameCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const PriceBox = styled(Box)(({ theme }) => ({
  background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`,
  color: 'white',
  padding: theme.spacing(2),
  borderRadius: theme.spacing(1),
  textAlign: 'center',
  marginBottom: theme.spacing(2),
}));

const ScreenshotCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: `0 10px 30px ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`game-tabpanel-${index}`}
      aria-labelledby={`game-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const GameDetail: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const theme = useTheme();
  const [game, setGame] = useState<Game | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [selectedScreenshot, setSelectedScreenshot] = useState(0);

  useEffect(() => {
    if (id) {
      fetchGame(id as string);
    }
  }, [id]);

  const fetchGame = async (gameId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/games/${gameId}`);
      const result = await response.json();

      if (result.success) {
        setGame(result.data);
      } else {
        setError(result.error || 'Failed to load game');
      }
    } catch (err) {
      setError('Failed to load game');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePurchase = () => {
    // Implement purchase logic
    console.log('Purchase game:', game?.id);
  };

  const handleDownload = () => {
    // Implement download logic
    console.log('Download game:', game?.id);
  };

  if (loading) {
    return (
      <Layout>
        <GameDetailContainer>
          <Container maxWidth="lg">
            <Skeleton variant="rectangular" height={400} sx={{ mb: 4, borderRadius: 2 }} />
            <Grid container spacing={4}>
              <Grid item xs={12} md={8}>
                <Skeleton variant="text" height={60} />
                <Skeleton variant="text" height={40} width="60%" />
                <Skeleton variant="rectangular" height={200} sx={{ mt: 2 }} />
              </Grid>
              <Grid item xs={12} md={4}>
                <Skeleton variant="rectangular" height={300} />
              </Grid>
            </Grid>
          </Container>
        </GameDetailContainer>
      </Layout>
    );
  }

  if (error || !game) {
    return (
      <Layout>
        <GameDetailContainer>
          <Container maxWidth="lg">
            <Alert severity="error" sx={{ mt: 4 }}>
              {error || 'Game not found'}
            </Alert>
          </Container>
        </GameDetailContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <GameDetailContainer>
        <Container maxWidth="lg">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              {game.images.banner && (
                <Image
                  src={game.images.banner}
                  alt={game.title}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              )}
              <HeroOverlay>
                <Box>
                  <Typography variant="h2" fontWeight="bold" color="white" gutterBottom>
                    {game.title}
                  </Typography>
                  <Typography variant="h6" color="white" sx={{ opacity: 0.9, mb: 2 }}>
                    {game.description}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Rating value={game.details.rating} readOnly precision={0.1} />
                    <Typography color="white">
                      {game.details.rating}/5 ({game.stats.reviews} reviews)
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {game.details.genre.map((genre) => (
                      <Chip
                        key={genre}
                        label={genre}
                        sx={{
                          bgcolor: alpha('#ffffff', 0.2),
                          color: 'white',
                          backdropFilter: 'blur(10px)',
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              </HeroOverlay>
            </HeroSection>
          </motion.div>

          <Grid container spacing={4}>
            {/* Main Content */}
            <Grid item xs={12} md={8}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <GameCard>
                  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={tabValue} onChange={handleTabChange}>
                      <Tab label="Overview" />
                      <Tab label="Screenshots" />
                      <Tab label="System Requirements" />
                      <Tab label="Reviews" />
                    </Tabs>
                  </Box>

                  <TabPanel value={tabValue} index={0}>
                    <Typography variant="h6" gutterBottom>
                      About This Game
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {game.longDescription || game.description}
                    </Typography>
                    
                    <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                      Features
                    </Typography>
                    <List>
                      {game.features.map((feature, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <CheckCircleIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary={feature} />
                        </ListItem>
                      ))}
                    </List>
                  </TabPanel>

                  <TabPanel value={tabValue} index={1}>
                    <Grid container spacing={2}>
                      {game.images.screenshots.map((screenshot, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                          <ScreenshotCard onClick={() => setSelectedScreenshot(index)}>
                            <CardMedia
                              component="img"
                              height="200"
                              image={screenshot}
                              alt={`Screenshot ${index + 1}`}
                            />
                          </ScreenshotCard>
                        </Grid>
                      ))}
                    </Grid>
                  </TabPanel>

                  <TabPanel value={tabValue} index={2}>
                    {game.systemRequirements && (
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="h6" gutterBottom>
                            Minimum Requirements
                          </Typography>
                          <List>
                            <ListItem>
                              <ListItemText
                                primary="OS"
                                secondary={game.systemRequirements.minimum.os}
                              />
                            </ListItem>
                            <ListItem>
                              <ListItemText
                                primary="Processor"
                                secondary={game.systemRequirements.minimum.processor}
                              />
                            </ListItem>
                            <ListItem>
                              <ListItemText
                                primary="Memory"
                                secondary={game.systemRequirements.minimum.memory}
                              />
                            </ListItem>
                            <ListItem>
                              <ListItemText
                                primary="Graphics"
                                secondary={game.systemRequirements.minimum.graphics}
                              />
                            </ListItem>
                            <ListItem>
                              <ListItemText
                                primary="Storage"
                                secondary={game.systemRequirements.minimum.storage}
                              />
                            </ListItem>
                          </List>
                        </Grid>
                        {game.systemRequirements.recommended && (
                          <Grid item xs={12} md={6}>
                            <Typography variant="h6" gutterBottom>
                              Recommended Requirements
                            </Typography>
                            <List>
                              <ListItem>
                                <ListItemText
                                  primary="OS"
                                  secondary={game.systemRequirements.recommended.os}
                                />
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Processor"
                                  secondary={game.systemRequirements.recommended.processor}
                                />
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Memory"
                                  secondary={game.systemRequirements.recommended.memory}
                                />
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Graphics"
                                  secondary={game.systemRequirements.recommended.graphics}
                                />
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Storage"
                                  secondary={game.systemRequirements.recommended.storage}
                                />
                              </ListItem>
                            </List>
                          </Grid>
                        )}
                      </Grid>
                    )}
                  </TabPanel>

                  <TabPanel value={tabValue} index={3}>
                    <Typography variant="h6" gutterBottom>
                      User Reviews
                    </Typography>
                    <Alert severity="info">
                      Reviews feature coming soon! Users will be able to rate and review games.
                    </Alert>
                  </TabPanel>
                </GameCard>
              </motion.div>
            </Grid>

            {/* Sidebar */}
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <GameCard sx={{ mb: 3 }}>
                  <CardContent>
                    {/* Price and Purchase */}
                    <PriceBox>
                      {game.pricing.isFree ? (
                        <Typography variant="h4" fontWeight="bold">
                          FREE
                        </Typography>
                      ) : (
                        <>
                          {game.pricing.discount && game.pricing.discount > 0 ? (
                            <Box>
                              <Typography variant="body2" sx={{ textDecoration: 'line-through', opacity: 0.7 }}>
                                ${game.pricing.price}
                              </Typography>
                              <Typography variant="h4" fontWeight="bold">
                                ${(game.pricing.price * (1 - game.pricing.discount / 100)).toFixed(2)}
                              </Typography>
                              <Chip
                                label={`-${game.pricing.discount}%`}
                                color="error"
                                size="small"
                                sx={{ mt: 1 }}
                              />
                            </Box>
                          ) : (
                            <Typography variant="h4" fontWeight="bold">
                              ${game.pricing.price}
                            </Typography>
                          )}
                        </>
                      )}
                    </PriceBox>

                    <Button
                      variant="contained"
                      size="large"
                      fullWidth
                      startIcon={game.pricing.isFree ? <DownloadIcon /> : <ShoppingCartIcon />}
                      onClick={game.pricing.isFree ? handleDownload : handlePurchase}
                      sx={{ mb: 2 }}
                    >
                      {game.pricing.isFree ? 'Download Now' : 'Purchase Game'}
                    </Button>

                    <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
                      <Button variant="outlined" startIcon={<FavoriteIcon />} fullWidth>
                        Wishlist
                      </Button>
                      <Button variant="outlined" startIcon={<ShareIcon />} fullWidth>
                        Share
                      </Button>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    {/* Game Info */}
                    <List disablePadding>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Developer"
                          secondary={game.developer.name}
                        />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CalendarTodayIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Release Date"
                          secondary={new Date(game.details.releaseDate).toLocaleDateString()}
                        />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <DevicesIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Platforms"
                          secondary={game.details.platforms.join(', ')}
                        />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CategoryIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Genre"
                          secondary={game.details.genre.join(', ')}
                        />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <LanguageIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Languages"
                          secondary={game.details.languages.join(', ')}
                        />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <StorageIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Size"
                          secondary={game.details.size}
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </GameCard>

                {/* Stats */}
                <GameCard>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Statistics
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="h4" color="primary" fontWeight="bold">
                          {game.stats.downloads.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Downloads
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="h4" color="primary" fontWeight="bold">
                          {game.stats.views.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Views
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="h4" color="primary" fontWeight="bold">
                          {game.stats.likes.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Likes
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="h4" color="primary" fontWeight="bold">
                          {game.stats.reviews.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Reviews
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </GameCard>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </GameDetailContainer>
    </Layout>
  );
};

export default GameDetail;
