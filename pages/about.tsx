import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  useTheme,
  alpha,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  RocketLaunch as RocketLaunchIcon,
  Bolt as BoltIcon,
  Refresh as ArrowPathIcon,
  Warning as ExclamationTriangleIcon,
  Lightbulb as LightBulbIcon,
  Star as StarIcon,
} from '@mui/icons-material';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

const TimelineContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: '50%',
    top: 0,
    bottom: 0,
    width: '4px',
    background: `linear-gradient(180deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    transform: 'translateX(-50%)',
    [theme.breakpoints.down('md')]: {
      left: '30px',
    },
  },
}));

const TimelineItem = styled(Box)(({ theme }) => ({
  position: 'relative',
  marginBottom: theme.spacing(8),
  '&:nth-of-type(even)': {
    '& .timeline-content': {
      [theme.breakpoints.up('md')]: {
        textAlign: 'right',
        paddingRight: theme.spacing(4),
        paddingLeft: 0,
      },
    },
    '& .timeline-icon': {
      [theme.breakpoints.up('md')]: {
        right: 'calc(50% - 30px)',
        left: 'auto',
      },
    },
  },
}));

const TimelineIcon = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: 'calc(50% - 30px)',
  top: 0,
  width: '60px',
  height: '60px',
  borderRadius: '50%',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 2,
  boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
  [theme.breakpoints.down('md')]: {
    left: '0px',
  },
  '& svg': {
    width: '24px',
    height: '24px',
    color: 'white',
  },
}));

const TimelineContent = styled(Card)(({ theme }) => ({
  width: '45%',
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.9),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 8px 24px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 32px ${alpha(theme.palette.common.black, 0.15)}`,
  },
  [theme.breakpoints.down('md')]: {
    width: 'calc(100% - 80px)',
    marginLeft: '80px',
  },
}));

const TimelineYear = styled(Typography)(({ theme }) => ({
  color: theme.palette.primary.main,
  fontWeight: 700,
  fontSize: '0.875rem',
  marginBottom: theme.spacing(1),
}));

const TimelineTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(2),
  color: theme.palette.text.primary,
}));

// Timeline data
const timelineEvents = [
  {
    year: '2017',
    title: 'Gamestorme Founded',
    description: 'Gamestorme was founded by Joel and Adrian in mid-2017 from their passion in game design and development. After a few weeks of discussion, their first game idea was born: Project Hop. To bring Project Hop to life, they needed talented artists. Thus enters Ercrypto and Jorge, our two beloved artists that have been with us throughout all the ups and downs.',
    icon: RocketLaunchIcon,
  },
  {
    year: '2017',
    title: 'Hurricane Maria',
    description: 'Midway through developing Project Hop however, Puerto Rico got hit by Hurricane Irma and the devastating Hurricane Maria, wiping out all development for Project Hop in the process. Months of no power and no water meant that everyone had to find ways to adapt and survive in their new post Maria lifestyle. Some members ended up leaving for the US, while others chose to stay and rebuild what was lost. This unfortunately put a hold on Gamestorme\'s development.',
    icon: BoltIcon,
  },
  {
    year: '2019',
    title: 'Rebuilding',
    description: 'In late 2019, Joel contacted Adrian in hopes of restarting Gamestorme, this time as a blockchain gaming company. To reflect this, Ali and Confessor (smart contract developer and game developer respectively) were added to the team.',
    icon: ArrowPathIcon,
  },
  {
    year: '2020',
    title: 'Covid-19',
    description: 'Once Covid hit, we found ourselves underfunded and once again had to put development on hold. While the project was not moving, Adrian managed to move his way up from working as an IT specialist to becoming a software engineer, Joel was using his time outside his day job to hone his skills in investing, smart contract development and business management, and the rest of us were all honing their skills in their perspective fields. In August of 2021, Teagan joined Gamestorme with the funds to start development again.',
    icon: ExclamationTriangleIcon,
  },
  {
    year: '2021',
    title: 'A New Plan',
    description: 'Seeing the potential of the team, Teagan became project manager for the company and worked to fulfill Gamestorme\'s mission by introducing PNFT\'s, a mobile specific crypto-wallet, and the NFT marketplace. With the idea of PNFT\'s, we applied for Solana\'s hackathon and were blown away by the support of Solana\'s developer community. We met some extremely talented individuals, one of which ended up joining our team—Proharvester—whom helped us build our website, mint page functionality, and mint our NFT\'s.',
    icon: LightBulbIcon,
  },
  {
    year: '2024',
    title: 'The Journey Begins',
    description: 'It has been an amazing journey filled with ups and downs. The people we\'ve met along the way have been nothing less than a blessing, and we hope to bring as many along with us as possible through this journey started by a couple of gamers. As the Storme has only just begun.',
    icon: StarIcon,
  },
];

const About: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="About"
        highlightedTitle="Gamestorme"
        description="Empowering Game Developers Through Innovative Solutions. Discover how we became a pioneering digital game store with AI and blockchain technology."
      />

      {/* Our Story Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <SectionTitle variant="h3" sx={{ textAlign: 'center', mb: 4 }}>
              Our Story
            </SectionTitle>
            <Typography
              variant="body1"
              paragraph
              sx={{
                mb: 4,
                fontSize: '1.1rem',
                lineHeight: 1.8,
                textAlign: 'center',
                maxWidth: '900px',
                mx: 'auto'
              }}
            >
              Gamestorme was founded by Joel and Adrian in mid-2017 from their home in Aguada, Puerto Rico, with a vision to revolutionize the gaming industry. What started as an outlet for creativity and game design has evolved into a pioneering digital game store that empowers game developers through innovative solutions.
            </Typography>
            <Typography
              variant="body1"
              paragraph
              sx={{
                mb: 6,
                fontSize: '1.1rem',
                lineHeight: 1.8,
                textAlign: 'center',
                maxWidth: '900px',
                mx: 'auto'
              }}
            >
              Today, Gamestorme stands as a unified platform for distribution and marketing, powered by cutting-edge AI and blockchain technology. Our main feature is our AI marketing tool for the developer dashboard, which analyzes game market data from our platform and external sources to help boost game development. As Puerto Rico's first blockchain gaming company, we're not just challenging industry standards—we're setting new ones for the future of game development and distribution.
            </Typography>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Timeline Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.3) }}>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3" sx={{ textAlign: 'center', mb: 6 }}>
              Our Journey
            </SectionTitle>

            <TimelineContainer>
              {timelineEvents.map((event, index) => (
                <TimelineItem key={index}>
                  <TimelineIcon className="timeline-icon">
                    <event.icon />
                  </TimelineIcon>

                  <motion.div
                    variants={itemVariants}
                    style={{
                      display: 'flex',
                      justifyContent: index % 2 === 0 ? 'flex-start' : 'flex-end',
                      width: '100%',
                    }}
                  >
                    <TimelineContent className="timeline-content">
                      <TimelineYear>{event.year}</TimelineYear>
                      <TimelineTitle variant="h5">{event.title}</TimelineTitle>
                      <Typography
                        variant="body1"
                        sx={{
                          lineHeight: 1.7,
                          color: theme.palette.text.secondary
                        }}
                      >
                        {event.description}
                      </Typography>
                    </TimelineContent>
                  </motion.div>
                </TimelineItem>
              ))}
            </TimelineContainer>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default About;
