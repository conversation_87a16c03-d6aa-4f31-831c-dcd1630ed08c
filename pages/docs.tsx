import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArticleIcon from '@mui/icons-material/Article';
import CodeIcon from '@mui/icons-material/Code';
import SchoolIcon from '@mui/icons-material/School';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Link from 'next/link';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const DocCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const CategoryTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  color: theme.palette.primary.main,
}));

const DocTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  marginBottom: theme.spacing(0.5),
}));

const DocDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(2),
  flexGrow: 1,
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: 60,
  height: 60,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& svg': {
    fontSize: 30,
    color: theme.palette.primary.main,
  },
}));

// Sample documentation categories
const docCategories = [
  {
    title: 'Getting Started',
    icon: <SchoolIcon />,
    docs: [
      {
        title: 'Introduction to Gamestorme',
        description: 'Learn about the Gamestorme platform and ecosystem.',
        link: '/docs/introduction',
      },
      {
        title: 'Creating Your Account',
        description: 'Step-by-step guide to setting up your Gamestorme account.',
        link: '/docs/account-setup',
      },
      {
        title: 'Connecting Your Wallet',
        description: 'How to connect your blockchain wallet to Gamestorme.',
        link: '/docs/wallet-connection',
      },
    ],
  },
  {
    title: 'Gameplay Guides',
    icon: <ArticleIcon />,
    docs: [
      {
        title: 'Game Mechanics',
        description: 'Detailed explanations of core gameplay mechanics.',
        link: '/docs/game-mechanics',
      },
      {
        title: 'Character Progression',
        description: 'Understanding character leveling and advancement.',
        link: '/docs/character-progression',
      },
      {
        title: 'In-Game Economy',
        description: 'Guide to the economic systems within our games.',
        link: '/docs/economy',
      },
    ],
  },
  {
    title: 'Developer Resources',
    icon: <CodeIcon />,
    docs: [
      {
        title: 'API Documentation',
        description: 'Technical documentation for integrating with our API.',
        link: '/docs/api',
      },
      {
        title: 'SDK Integration',
        description: 'How to use our SDK in your own projects.',
        link: '/docs/sdk',
      },
      {
        title: 'Smart Contract References',
        description: 'Details about our blockchain smart contracts.',
        link: '/docs/smart-contracts',
      },
    ],
  },
  {
    title: 'FAQs & Troubleshooting',
    icon: <HelpOutlineIcon />,
    docs: [
      {
        title: 'Frequently Asked Questions',
        description: 'Answers to common questions about Gamestorme.',
        link: '/docs/faq',
      },
      {
        title: 'Troubleshooting Guide',
        description: 'Solutions to common issues you might encounter.',
        link: '/docs/troubleshooting',
      },
      {
        title: 'Support Resources',
        description: 'How to get help when you need it.',
        link: '/docs/support',
      },
    ],
  },
];

const Docs: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Documentation &"
        highlightedTitle="Resources"
        description="Comprehensive guides, tutorials, and reference materials to help you get the most out of the Gamestorme platform."
      />

      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={4}>
              {docCategories.map((category, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <motion.div variants={itemVariants}>
                    <DocCard>
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <IconWrapper>
                            {category.icon}
                          </IconWrapper>
                          <CategoryTitle variant="h5" sx={{ ml: 2 }}>
                            {category.title}
                          </CategoryTitle>
                        </Box>
                        
                        <List>
                          {category.docs.map((doc, docIndex) => (
                            <ListItem key={docIndex} sx={{ px: 0, py: 1 }}>
                              <ListItemIcon sx={{ minWidth: 36 }}>
                                <ArrowForwardIcon color="primary" fontSize="small" />
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <DocTitle variant="h6">
                                    {doc.title}
                                  </DocTitle>
                                }
                                secondary={
                                  <DocDescription variant="body2">
                                    {doc.description}
                                  </DocDescription>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                        
                        <Button
                          variant="outlined"
                          color="primary"
                          component={Link}
                          href={`/docs/${category.title.toLowerCase().replace(/\s+/g, '-')}`}
                          endIcon={<ArrowForwardIcon />}
                          sx={{ mt: 2 }}
                        >
                          View All {category.title}
                        </Button>
                      </CardContent>
                    </DocCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Docs;
