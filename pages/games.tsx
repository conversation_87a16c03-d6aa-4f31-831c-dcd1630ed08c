import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  useTheme,
  alpha,
  Rating,
  Tabs,
  Tab,
  InputBase,
  IconButton,
  Divider,
  Paper,
  Menu,
  MenuItem,
  FormControl,
  Select,
  SelectChangeEvent,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Slider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import StarIcon from '@mui/icons-material/Star';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import WhatshotIcon from '@mui/icons-material/Whatshot';
import Link from 'next/link';
import Image from 'next/image';

// Styled components
const StoreContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(4, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const FeaturedGameCard = styled(Card)(({ theme }) => ({
  position: 'relative',
  height: 500,
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.2)}`,
  '&:hover .featured-overlay': {
    background: `linear-gradient(to top, ${alpha(theme.palette.common.black, 0.9)} 0%, ${alpha(theme.palette.common.black, 0.6)} 40%, transparent 100%)`,
  },
}));

const FeaturedOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  padding: theme.spacing(4),
  background: `linear-gradient(to top, ${alpha(theme.palette.common.black, 0.8)} 0%, ${alpha(theme.palette.common.black, 0.5)} 40%, transparent 100%)`,
  transition: 'background 0.3s ease',
}));

const GameCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
    '& .game-image': {
      transform: 'scale(1.05)',
    },
  },
}));

const GameImage = styled(CardMedia)(({ theme }) => ({
  height: 220,
  transition: 'transform 0.5s ease',
}));

const GameTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const GameDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(2),
  flexGrow: 1,
}));

const GameChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0, 0.5, 0.5, 0),
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.main,
  fontWeight: 500,
}));

const SearchBar = styled(Paper)(({ theme }) => ({
  padding: '2px 4px',
  display: 'flex',
  alignItems: 'center',
  width: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  flex: 1,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  '& .MuiTabs-indicator': {
    backgroundColor: theme.palette.primary.main,
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontSize: '1rem',
  minWidth: 'auto',
  padding: theme.spacing(1, 2),
  '&.Mui-selected': {
    color: theme.palette.primary.main,
  },
}));

const PriceTag = styled(Box)(({ theme }) => ({
  display: 'inline-block',
  padding: theme.spacing(0.5, 1.5),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  fontWeight: 700,
}));

const DiscountTag = styled(Box)(({ theme }) => ({
  display: 'inline-block',
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.secondary.main,
  color: theme.palette.secondary.contrastText,
  fontWeight: 700,
  marginRight: theme.spacing(1),
}));

const OldPrice = styled(Typography)(({ theme }) => ({
  textDecoration: 'line-through',
  color: alpha(theme.palette.text.primary, 0.6),
  marginRight: theme.spacing(1),
}));

// Gamestorme's actual game collection
const games = [
  {
    id: 1,
    title: 'Agueybana',
    description: 'An epic adventure inspired by Puerto Rican history and mythology, featuring the legendary Taíno cacique.',
    image: '/assets/images/games/agueybana.jpg',
    rating: 4.8,
    categories: ['Adventure', 'Historical', 'Mythology'],
    platforms: ['PC', 'Mobile'],
    price: 29.99,
    discount: 20,
    releaseDate: '2023-05-15',
    publisher: 'Gamestorme',
    featured: true,
  },
  {
    id: 2,
    title: 'Driadan',
    description: 'A mystical RPG journey through ancient realms filled with magic and wonder.',
    image: '/assets/images/games/driadan.jpg',
    rating: 4.6,
    categories: ['RPG', 'Fantasy', 'Magic'],
    platforms: ['PC', 'Console'],
    price: 24.99,
    discount: 15,
    releaseDate: '2023-03-10',
    publisher: 'Gamestorme',
    featured: false,
  },
  {
    id: 3,
    title: 'Guaramania',
    description: 'Experience the vibrant culture and music of Puerto Rico in this rhythm-based adventure game.',
    image: '/assets/images/games/guaramania.png',
    rating: 4.7,
    categories: ['Rhythm', 'Cultural', 'Music'],
    platforms: ['PC', 'Mobile', 'Console'],
    price: 19.99,
    discount: 10,
    releaseDate: '2023-01-20',
    publisher: 'Gamestorme',
    featured: true,
  },
  {
    id: 4,
    title: 'Hajimari',
    description: 'A beautiful beginning - explore new worlds and discover your destiny in this captivating adventure.',
    image: '/assets/images/games/hajimari.jpg',
    rating: 4.5,
    categories: ['Adventure', 'Exploration', 'Story'],
    platforms: ['PC', 'Console'],
    price: 34.99,
    discount: 0,
    releaseDate: '2023-06-01',
    publisher: 'Gamestorme',
    featured: false,
  },
  {
    id: 5,
    title: 'Skybound',
    description: 'Soar through the skies in this thrilling aerial adventure with breathtaking views and challenging gameplay.',
    image: '/assets/images/games/skybound-game.jpg',
    rating: 4.9,
    categories: ['Flying', 'Adventure', 'Action'],
    platforms: ['PC', 'Mobile'],
    price: 39.99,
    discount: 25,
    releaseDate: '2022-11-15',
    publisher: 'Gamestorme',
    featured: true,
  },
  {
    id: 6,
    title: 'Tygran',
    description: 'Command mighty forces in this strategic warfare game set in a world of ancient powers.',
    image: '/assets/images/games/tygran.jpg',
    rating: 4.7,
    categories: ['Strategy', 'Warfare', 'Tactical'],
    platforms: ['PC', 'Console'],
    price: 44.99,
    discount: 20,
    releaseDate: '2023-04-05',
    publisher: 'Gamestorme',
    featured: false,
  },
  {
    id: 7,
    title: 'Z-Tea',
    description: 'A relaxing simulation game where you manage your own tea garden and create the perfect blends.',
    image: '/assets/images/games/z-tea.png',
    rating: 4.4,
    categories: ['Simulation', 'Relaxing', 'Management'],
    platforms: ['PC', 'Mobile'],
    price: 14.99,
    discount: 0,
    releaseDate: '2023-02-28',
    publisher: 'Gamestorme',
    featured: false,
  },
];

// Get featured games
const featuredGames = games.filter(game => game.featured);

// All available categories
const allCategories = Array.from(new Set(games.flatMap(game => game.categories)));

// All available platforms
const allPlatforms = Array.from(new Set(games.flatMap(game => game.platforms)));

const Games: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('featured');
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<number[]>([0, 60]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle sort change
  const handleSortChange = (event: SelectChangeEvent) => {
    setSortBy(event.target.value);
  };

  // Handle category toggle
  const handleCategoryToggle = (category: string) => {
    const currentIndex = selectedCategories.indexOf(category);
    const newSelectedCategories = [...selectedCategories];

    if (currentIndex === -1) {
      newSelectedCategories.push(category);
    } else {
      newSelectedCategories.splice(currentIndex, 1);
    }

    setSelectedCategories(newSelectedCategories);
  };

  // Handle platform toggle
  const handlePlatformToggle = (platform: string) => {
    const currentIndex = selectedPlatforms.indexOf(platform);
    const newSelectedPlatforms = [...selectedPlatforms];

    if (currentIndex === -1) {
      newSelectedPlatforms.push(platform);
    } else {
      newSelectedPlatforms.splice(currentIndex, 1);
    }

    setSelectedPlatforms(newSelectedPlatforms);
  };

  // Handle price range change
  const handlePriceRangeChange = (event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as number[]);
  };

  // Filter and sort games
  const filteredGames = games.filter(game => {
    // Filter by tab
    if (tabValue === 1 && !game.featured) return false;
    if (tabValue === 2 && game.discount === 0) return false;
    if (tabValue === 3 && new Date(game.releaseDate) < new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) return false;

    // Filter by search
    if (searchQuery && !game.title.toLowerCase().includes(searchQuery.toLowerCase())) return false;

    // Filter by categories
    if (selectedCategories.length > 0 && !game.categories.some(cat => selectedCategories.includes(cat))) return false;

    // Filter by platforms
    if (selectedPlatforms.length > 0 && !game.platforms.some(plat => selectedPlatforms.includes(plat))) return false;

    // Filter by price
    const actualPrice = game.discount > 0 ? game.price * (1 - game.discount / 100) : game.price;
    if (actualPrice < priceRange[0] || actualPrice > priceRange[1]) return false;

    return true;
  }).sort((a, b) => {
    // Sort games
    switch (sortBy) {
      case 'price-low':
        const priceA = a.discount > 0 ? a.price * (1 - a.discount / 100) : a.price;
        const priceB = b.discount > 0 ? b.price * (1 - b.discount / 100) : b.price;
        return priceA - priceB;
      case 'price-high':
        const priceAHigh = a.discount > 0 ? a.price * (1 - a.discount / 100) : a.price;
        const priceBHigh = b.discount > 0 ? b.price * (1 - b.discount / 100) : b.price;
        return priceBHigh - priceAHigh;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime();
      default:
        return b.featured ? 1 : -1;
    }
  });

  return (
    <Layout>
      <PageHeader
        title="Storme"
        highlightedTitle="Store"
        description="Discover and play the best games from our collection. From indie gems to blockbuster titles, find your next gaming adventure here."
      />

      {/* Featured Games Carousel */}
      <StoreContainer>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Featured & Recommended
            </Typography>
            <Grid container spacing={4}>
              {featuredGames.slice(0, 1).map((game) => (
                <Grid item xs={12} key={`featured-${game.id}`}>
                  <FeaturedGameCard>
                    <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
                      <Image
                        src={game.image}
                        alt={game.title}
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                    </Box>
                    <FeaturedOverlay className="featured-overlay">
                      <Typography variant="h3" fontWeight="bold" color="white" gutterBottom>
                        {game.title}
                      </Typography>
                      <Typography variant="h6" color="white" sx={{ mb: 2, maxWidth: '60%' }}>
                        {game.description}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        {game.discount > 0 && (
                          <DiscountTag>-{game.discount}%</DiscountTag>
                        )}
                        {game.discount > 0 ? (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <OldPrice variant="h6">${game.price.toFixed(2)}</OldPrice>
                            <PriceTag>
                              <Typography variant="h6">
                                ${(game.price * (1 - game.discount / 100)).toFixed(2)}
                              </Typography>
                            </PriceTag>
                          </Box>
                        ) : (
                          <PriceTag>
                            <Typography variant="h6">${game.price.toFixed(2)}</Typography>
                          </PriceTag>
                        )}
                      </Box>
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button
                          variant="contained"
                          color="primary"
                          size="large"
                          startIcon={<ShoppingCartIcon />}
                        >
                          Add to Cart
                        </Button>
                        <Link href={`/games/${game.id}`} passHref style={{ textDecoration: 'none' }}>
                          <Button
                            variant="outlined"
                            color="primary"
                            size="large"
                          >
                            View Details
                          </Button>
                        </Link>
                      </Box>
                    </FeaturedOverlay>
                  </FeaturedGameCard>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </StoreContainer>

      {/* Game Store Section */}
      <StoreContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Search and Filter Bar */}
            <Box sx={{ mb: 4 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <SearchBar>
                    <IconButton sx={{ p: '10px' }} aria-label="search">
                      <SearchIcon />
                    </IconButton>
                    <StyledInputBase
                      placeholder="Search games..."
                      inputProps={{ 'aria-label': 'search games' }}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </SearchBar>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<FilterListIcon />}
                    onClick={() => setFilterDrawerOpen(true)}
                    fullWidth
                  >
                    Filters
                  </Button>
                </Grid>
                <Grid item xs={6} md={3}>
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      value={sortBy}
                      onChange={handleSortChange}
                      displayEmpty
                      startAdornment={<SortIcon sx={{ mr: 1 }} />}
                      sx={{ borderRadius: theme.shape.borderRadius * 2 }}
                    >
                      <MenuItem value="featured">Featured</MenuItem>
                      <MenuItem value="price-low">Price: Low to High</MenuItem>
                      <MenuItem value="price-high">Price: High to Low</MenuItem>
                      <MenuItem value="rating">Top Rated</MenuItem>
                      <MenuItem value="newest">Newest</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>

            {/* Category Tabs */}
            <StyledTabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="game categories"
            >
              <StyledTab icon={<FilterListIcon />} label="All Games" />
              <StyledTab icon={<WhatshotIcon />} label="Featured" />
              <StyledTab icon={<LocalOfferIcon />} label="On Sale" />
              <StyledTab icon={<NewReleasesIcon />} label="New Releases" />
            </StyledTabs>

            {/* Games Grid */}
            <Grid container spacing={4}>
              {filteredGames.map((game) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={game.id}>
                  <motion.div variants={itemVariants} style={{ height: '100%' }}>
                    <GameCard>
                      <Box sx={{ position: 'relative' }}>
                        <GameImage
                          className="game-image"
                          image={game.image}
                          title={game.title}
                        />
                        {game.discount > 0 && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 10,
                              right: 10,
                              bgcolor: 'secondary.main',
                              color: 'white',
                              fontWeight: 'bold',
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                            }}
                          >
                            -{game.discount}%
                          </Box>
                        )}
                        <IconButton
                          sx={{
                            position: 'absolute',
                            top: 10,
                            left: 10,
                            bgcolor: alpha(theme.palette.background.paper, 0.8),
                            '&:hover': { bgcolor: alpha(theme.palette.background.paper, 0.9) },
                          }}
                        >
                          <FavoriteBorderIcon />
                        </IconButton>
                      </Box>
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <GameTitle variant="h6">{game.title}</GameTitle>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <StarIcon sx={{ color: theme.palette.secondary.main, mr: 0.5, fontSize: '1rem' }} />
                            <Typography variant="body2" fontWeight="bold">
                              {game.rating}
                            </Typography>
                          </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {game.publisher}
                        </Typography>
                        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap' }}>
                          {game.categories.slice(0, 2).map((category) => (
                            <GameChip key={category} label={category} size="small" />
                          ))}
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          {game.discount > 0 ? (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <OldPrice variant="body2">${game.price.toFixed(2)}</OldPrice>
                              <Typography variant="h6" fontWeight="bold" color="primary">
                                ${(game.price * (1 - game.discount / 100)).toFixed(2)}
                              </Typography>
                            </Box>
                          ) : (
                            <Typography variant="h6" fontWeight="bold" color="primary">
                              ${game.price.toFixed(2)}
                            </Typography>
                          )}
                          <IconButton color="primary" size="small">
                            <ShoppingCartIcon />
                          </IconButton>
                        </Box>
                        <Link href={`/games/${game.id}`} passHref style={{ textDecoration: 'none', width: '100%' }}>
                          <Button
                            variant="contained"
                            color="primary"
                            fullWidth
                            size="small"
                          >
                            View Game
                          </Button>
                        </Link>
                      </CardContent>
                    </GameCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </StoreContainer>

      {/* Filter Drawer */}
      <Drawer
        anchor="right"
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        PaperProps={{
          sx: {
            width: 300,
            p: 3,
          },
        }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Filters
          </Typography>
          <Divider />
        </Box>

        {/* Price Range Filter */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Price Range
          </Typography>
          <Box sx={{ px: 1 }}>
            <Slider
              value={priceRange}
              onChange={handlePriceRangeChange}
              valueLabelDisplay="auto"
              min={0}
              max={60}
              step={5}
            />
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">${priceRange[0]}</Typography>
              <Typography variant="body2">${priceRange[1]}</Typography>
            </Box>
          </Box>
        </Box>

        {/* Categories Filter */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Categories
          </Typography>
          <List dense>
            {allCategories.map((category) => (
              <ListItem key={category} disablePadding>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <Checkbox
                    edge="start"
                    checked={selectedCategories.indexOf(category) !== -1}
                    onChange={() => handleCategoryToggle(category)}
                  />
                </ListItemIcon>
                <ListItemText primary={category} />
              </ListItem>
            ))}
          </List>
        </Box>

        {/* Platforms Filter */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Platforms
          </Typography>
          <List dense>
            {allPlatforms.map((platform) => (
              <ListItem key={platform} disablePadding>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <Checkbox
                    edge="start"
                    checked={selectedPlatforms.indexOf(platform) !== -1}
                    onChange={() => handlePlatformToggle(platform)}
                  />
                </ListItemIcon>
                <ListItemText primary={platform} />
              </ListItem>
            ))}
          </List>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={() => {
              setSelectedCategories([]);
              setSelectedPlatforms([]);
              setPriceRange([0, 60]);
            }}
          >
            Clear All
          </Button>
          <Button
            variant="contained"
            onClick={() => setFilterDrawerOpen(false)}
          >
            Apply Filters
          </Button>
        </Box>
      </Drawer>
    </Layout>
  );
};

export default Games;
