import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  useTheme,
  alpha,
  Rating,
  Tabs,
  Tab,
  InputBase,
  IconButton,
  Divider,
  Paper,
  Menu,
  MenuItem,
  FormControl,
  Select,
  SelectChangeEvent,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Slider,
  Skeleton,
  Alert,
  CircularProgress,
  Pagination,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import StarIcon from '@mui/icons-material/Star';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import FavoriteIcon from '@mui/icons-material/Favorite';
import DownloadIcon from '@mui/icons-material/Download';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import WhatshotIcon from '@mui/icons-material/Whatshot';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Game } from '../types/database';

const StoreContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const SearchBar = styled(Paper)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(1, 2),
  marginBottom: theme.spacing(3),
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
}));

const GameCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
    '& .game-image': {
      transform: 'scale(1.05)',
    },
  },
}));

const GameImage = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: 200,
  overflow: 'hidden',
  borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`,
  '& img': {
    transition: 'transform 0.3s ease',
  },
}));

const PriceTag = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`,
  color: 'white',
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(1),
  fontSize: '0.875rem',
  fontWeight: 'bold',
}));

const DiscountBadge = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  left: theme.spacing(1),
  background: theme.palette.error.main,
  color: 'white',
  fontWeight: 'bold',
}));

const FeaturedBadge = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  left: theme.spacing(1),
  background: `linear-gradient(45deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`,
  color: 'white',
  fontWeight: 'bold',
}));

const FilterDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 300,
    padding: theme.spacing(2),
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
    backdropFilter: 'blur(20px)',
  },
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`games-tabpanel-${index}`}
      aria-labelledby={`games-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const Games: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('newest');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Filters
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<number[]>([0, 100]);
  const [showFreeOnly, setShowFreeOnly] = useState(false);

  useEffect(() => {
    fetchGames();
  }, [page, sortBy, selectedGenres, selectedPlatforms, showFreeOnly, searchTerm]);

  const fetchGames = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        status: 'approved',
      });

      if (searchTerm) params.append('search', searchTerm);
      if (selectedGenres.length > 0) params.append('genre', selectedGenres[0]);
      if (selectedPlatforms.length > 0) params.append('platform', selectedPlatforms[0]);

      const response = await fetch(`/api/games?${params}`);
      const result = await response.json();

      if (result.success) {
        setGames(result.data);
        setTotalPages(result.pagination.totalPages);
      } else {
        setError(result.error || 'Failed to load games');
      }
    } catch (err) {
      setError('Failed to load games');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(1);
    
    // Apply different filters based on tab
    switch (newValue) {
      case 0: // All Games
        setSelectedGenres([]);
        break;
      case 1: // Featured
        // Will be handled by API call with featured=true
        break;
      case 2: // New Releases
        setSortBy('newest');
        break;
      case 3: // Popular
        setSortBy('popular');
        break;
    }
  };

  const handleGameClick = (gameId: string) => {
    router.push(`/games/${gameId}`);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderGameCard = (game: Game) => (
    <Grid item xs={12} sm={6} md={4} lg={3} key={game.id}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        whileHover={{ scale: 1.02 }}
      >
        <GameCard onClick={() => handleGameClick(game.id)}>
          <GameImage>
            <Image
              src={game.images.thumbnail}
              alt={game.title}
              fill
              style={{ objectFit: 'cover' }}
              className="game-image"
            />
            {game.featured && (
              <FeaturedBadge
                label="Featured"
                size="small"
                icon={<WhatshotIcon />}
              />
            )}
            {game.pricing.discount && game.pricing.discount > 0 && (
              <DiscountBadge
                label={`-${game.pricing.discount}%`}
                size="small"
              />
            )}
            <PriceTag>
              {game.pricing.isFree ? 'FREE' : `$${game.pricing.price}`}
            </PriceTag>
          </GameImage>
          
          <CardContent sx={{ flexGrow: 1, p: 2 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom noWrap>
              {game.title}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1, height: 40, overflow: 'hidden' }}>
              {game.description}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Rating value={game.details.rating} readOnly size="small" precision={0.1} />
              <Typography variant="caption" sx={{ ml: 1 }}>
                ({game.stats.reviews})
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 0.5, mb: 2, flexWrap: 'wrap' }}>
              {game.details.genre.slice(0, 2).map((genre) => (
                <Chip
                  key={genre}
                  label={genre}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem' }}
                />
              ))}
            </Box>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                size="small"
                fullWidth
                startIcon={game.pricing.isFree ? <DownloadIcon /> : <ShoppingCartIcon />}
              >
                {game.pricing.isFree ? 'Download' : 'Buy Now'}
              </Button>
              <IconButton size="small" color="primary">
                <FavoriteIcon />
              </IconButton>
            </Box>
          </CardContent>
        </GameCard>
      </motion.div>
    </Grid>
  );

  const renderLoadingSkeleton = () => (
    <Grid container spacing={3}>
      {Array.from({ length: 12 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
          <Card>
            <Skeleton variant="rectangular" height={200} />
            <CardContent>
              <Skeleton variant="text" height={32} />
              <Skeleton variant="text" height={20} width="80%" />
              <Skeleton variant="text" height={20} width="60%" />
              <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                <Skeleton variant="rectangular" height={32} width="70%" />
                <Skeleton variant="circular" width={32} height={32} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  return (
    <Layout>
      <PageHeader
        title="Storme"
        highlightedTitle="Store"
        description="Discover and play the best games from our collection. From indie gems to blockbuster titles, find your next gaming adventure here."
      />

      <StoreContainer>
        <Container maxWidth="lg">
          {/* Search and Filters */}
          <SearchBar elevation={0}>
            <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <InputBase
              placeholder="Search games..."
              value={searchTerm}
              onChange={handleSearch}
              sx={{ flex: 1 }}
            />
            <IconButton onClick={() => setFilterDrawerOpen(true)}>
              <FilterListIcon />
            </IconButton>
            <IconButton onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
              {viewMode === 'grid' ? <ViewListIcon /> : <GridViewIcon />}
            </IconButton>
          </SearchBar>

          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="All Games" />
              <Tab label="Featured" />
              <Tab label="New Releases" />
              <Tab label="Popular" />
            </Tabs>
          </Box>

          {/* Games Grid */}
          {loading ? (
            renderLoadingSkeleton()
          ) : error ? (
            <Alert severity="error" sx={{ mb: 4 }}>
              {error}
            </Alert>
          ) : games.length === 0 ? (
            <Alert severity="info" sx={{ mb: 4 }}>
              No games found. Try adjusting your search or filters.
            </Alert>
          ) : (
            <>
              <Grid container spacing={3}>
                {games.map(renderGameCard)}
              </Grid>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                    size="large"
                  />
                </Box>
              )}
            </>
          )}
        </Container>
      </StoreContainer>

      {/* Filter Drawer */}
      <FilterDrawer
        anchor="right"
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
      >
        <Typography variant="h6" gutterBottom>
          Filters
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        {/* Add filter controls here */}
        <Typography variant="subtitle2" gutterBottom>
          Price Range
        </Typography>
        <Slider
          value={priceRange}
          onChange={(_, newValue) => setPriceRange(newValue as number[])}
          valueLabelDisplay="auto"
          min={0}
          max={100}
          sx={{ mb: 3 }}
        />
        
        <Button
          variant="outlined"
          fullWidth
          onClick={() => setFilterDrawerOpen(false)}
        >
          Apply Filters
        </Button>
      </FilterDrawer>
    </Layout>
  );
};

export default Games;
