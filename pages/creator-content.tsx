import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Tab,
  Tabs,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import CampaignIcon from '@mui/icons-material/Campaign';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PeopleIcon from '@mui/icons-material/People';
import StarIcon from '@mui/icons-material/Star';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DownloadIcon from '@mui/icons-material/Download';
import ShareIcon from '@mui/icons-material/Share';
import AddIcon from '@mui/icons-material/Add';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import VisibilityIcon from '@mui/icons-material/Visibility';
import Link from 'next/link';

const CreatorContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const ContentCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const CTACard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: 'white',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  textAlign: 'center',
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 64,
  height: 64,
  borderRadius: theme.spacing(1),
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  marginBottom: theme.spacing(2),
}));

const creatorPrograms = [
  {
    title: 'Content Creator Partnership',
    description: 'Join our exclusive creator program and showcase games to your audience while earning revenue.',
    icon: <VideoLibraryIcon />,
    benefits: [
      'Early access to new games',
      'Revenue sharing on referrals',
      'Exclusive creator tools',
      'Marketing support',
    ],
    requirements: [
      '10K+ followers on any platform',
      'Gaming-focused content',
      'Regular content creation',
      'Professional presentation',
    ],
  },
  {
    title: 'Influencer Marketing',
    description: 'Partner with game developers to create authentic promotional content for their games.',
    icon: <CampaignIcon />,
    benefits: [
      'Paid sponsorship opportunities',
      'Free game access',
      'Custom promotional codes',
      'Performance bonuses',
    ],
    requirements: [
      '5K+ engaged followers',
      'Gaming niche content',
      'High engagement rates',
      'Content quality standards',
    ],
  },
  {
    title: 'Streaming Partnership',
    description: 'Stream games live and earn through our integrated monetization system.',
    icon: <PlayArrowIcon />,
    benefits: [
      'Stream integration tools',
      'Viewer engagement features',
      'Revenue from game sales',
      'Priority game access',
    ],
    requirements: [
      'Regular streaming schedule',
      'Gaming content focus',
      'Interactive audience',
      'Technical setup quality',
    ],
  },
];

const sampleContent = [
  {
    id: 1,
    title: 'Agueybana: Epic Adventure Gameplay',
    creator: 'GameMaster Pro',
    views: '125K',
    likes: '8.2K',
    type: 'Video',
    thumbnail: '/game1.jpg',
    duration: '15:32',
  },
  {
    id: 2,
    title: 'Top 10 Indie Games This Month',
    creator: 'IndieSpotlight',
    views: '89K',
    likes: '5.7K',
    type: 'Article',
    thumbnail: '/game2.jpg',
    readTime: '8 min read',
  },
  {
    id: 3,
    title: 'Guaramania Live Stream Highlights',
    creator: 'RhythmGamer',
    views: '67K',
    likes: '4.1K',
    type: 'Stream',
    thumbnail: '/game3.jpg',
    duration: '2:45:12',
  },
];

const CreatorContent: React.FC = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [applyDialogOpen, setApplyDialogOpen] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<string>('');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  const handleApply = () => {
    // Handle application submission
    console.log('Applying for:', selectedProgram);
    setApplyDialogOpen(false);
  };

  return (
    <Layout>
      <PageHeader
        title="Creator"
        highlightedTitle="Content"
        description="Join our creator community and monetize your gaming content while helping developers reach new audiences."
      />

      <CreatorContainer>
        <Container maxWidth="lg">
          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
            <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
              <Tab label="Creator Programs" />
              <Tab label="Featured Content" />
              <Tab label="Resources" />
              <Tab label="Apply Now" />
            </Tabs>
          </Box>

          {/* Creator Programs Tab */}
          {activeTab === 0 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Grid container spacing={6} alignItems="center" sx={{ mb: 8 }}>
                <Grid item xs={12} md={6}>
                  <motion.div variants={itemVariants}>
                    <Typography variant="h3" fontWeight="bold" gutterBottom>
                      Monetize Your Gaming Content
                    </Typography>
                    <Typography variant="body1" paragraph sx={{ mb: 3, fontSize: '1.1rem' }}>
                      Join thousands of creators who are earning revenue by showcasing amazing games 
                      to their audiences. Our creator programs offer multiple ways to monetize your 
                      passion for gaming.
                    </Typography>
                    <Typography variant="body1" paragraph sx={{ mb: 4, fontSize: '1.1rem' }}>
                      From video content to live streaming, we provide the tools and partnerships 
                      you need to turn your gaming content into a sustainable income stream.
                    </Typography>
                    <Button
                      variant="contained"
                      size="large"
                      endIcon={<ArrowForwardIcon />}
                      onClick={() => setActiveTab(3)}
                    >
                      Join Creator Program
                    </Button>
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={6}>
                  <motion.div variants={itemVariants}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 4,
                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                        borderRadius: 2,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      }}
                    >
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        Creator Benefits
                      </Typography>
                      <List>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <MonetizationOnIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="Earn up to 15% revenue share on game sales" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <TrendingUpIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="Access to trending games before public release" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <PeopleIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="Direct collaboration with game developers" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <StarIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="Exclusive creator tools and analytics" />
                        </ListItem>
                      </List>
                    </Paper>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Programs Grid */}
              <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom sx={{ mb: 6 }}>
                Creator Programs
              </Typography>
              <Grid container spacing={4} sx={{ mb: 8 }}>
                {creatorPrograms.map((program, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <motion.div variants={itemVariants} style={{ height: '100%' }}>
                      <FeatureCard>
                        <CardContent sx={{ p: 3 }}>
                          <IconBox>{program.icon}</IconBox>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {program.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" paragraph>
                            {program.description}
                          </Typography>
                          
                          <Typography variant="subtitle2" fontWeight="bold" gutterBottom sx={{ mt: 3 }}>
                            Benefits:
                          </Typography>
                          <List dense>
                            {program.benefits.map((benefit, i) => (
                              <ListItem key={i} disablePadding sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 32 }}>
                                  <CheckCircleOutlineIcon color="primary" sx={{ fontSize: 20 }} />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={benefit} 
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>

                          <Typography variant="subtitle2" fontWeight="bold" gutterBottom sx={{ mt: 2 }}>
                            Requirements:
                          </Typography>
                          <List dense>
                            {program.requirements.map((req, i) => (
                              <ListItem key={i} disablePadding sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 32 }}>
                                  <CheckCircleOutlineIcon color="secondary" sx={{ fontSize: 20 }} />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={req} 
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>

                          <Button
                            fullWidth
                            variant="outlined"
                            sx={{ mt: 3 }}
                            onClick={() => {
                              setSelectedProgram(program.title);
                              setApplyDialogOpen(true);
                            }}
                          >
                            Apply Now
                          </Button>
                        </CardContent>
                      </FeatureCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Featured Content Tab */}
          {activeTab === 1 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Featured Creator Content
              </Typography>
              <Grid container spacing={4}>
                {sampleContent.map((content, index) => (
                  <Grid item xs={12} sm={6} md={4} key={content.id}>
                    <motion.div variants={itemVariants}>
                      <ContentCard>
                        <Box sx={{ position: 'relative' }}>
                          <Avatar
                            src={content.thumbnail}
                            sx={{ width: '100%', height: 200, borderRadius: '16px 16px 0 0' }}
                            variant="rounded"
                          />
                          <Chip
                            label={content.type}
                            size="small"
                            sx={{ position: 'absolute', top: 8, left: 8 }}
                          />
                          {content.duration && (
                            <Chip
                              label={content.duration}
                              size="small"
                              sx={{ position: 'absolute', bottom: 8, right: 8, bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                            />
                          )}
                          {content.readTime && (
                            <Chip
                              label={content.readTime}
                              size="small"
                              sx={{ position: 'absolute', bottom: 8, right: 8, bgcolor: 'rgba(0,0,0,0.7)', color: 'white' }}
                            />
                          )}
                        </Box>
                        <CardContent>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {content.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            by {content.creator}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <VisibilityIcon sx={{ fontSize: 16 }} />
                              <Typography variant="caption">{content.views}</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <StarIcon sx={{ fontSize: 16 }} />
                              <Typography variant="caption">{content.likes}</Typography>
                            </Box>
                            <Box sx={{ ml: 'auto' }}>
                              <ShareIcon sx={{ fontSize: 16, cursor: 'pointer' }} />
                            </Box>
                          </Box>
                        </CardContent>
                      </ContentCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Resources Tab */}
          {activeTab === 2 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Creator Resources
              </Typography>
              <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                  <FeatureCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Content Creation Tools
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Access our suite of tools designed specifically for gaming content creators.
                      </Typography>
                      <List>
                        <ListItem disablePadding>
                          <ListItemText primary="Game capture software integration" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemText primary="Automated highlight generation" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemText primary="Custom overlay templates" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemText primary="Analytics and performance tracking" />
                        </ListItem>
                      </List>
                      <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                        Download Tools
                      </Button>
                    </CardContent>
                  </FeatureCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FeatureCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Marketing Guidelines
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Best practices and guidelines for promoting games effectively.
                      </Typography>
                      <List>
                        <ListItem disablePadding>
                          <ListItemText primary="Content creation best practices" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemText primary="Audience engagement strategies" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemText primary="Platform-specific optimization" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemText primary="Revenue maximization tips" />
                        </ListItem>
                      </List>
                      <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                        View Guidelines
                      </Button>
                    </CardContent>
                  </FeatureCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Apply Now Tab */}
          {activeTab === 3 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <CTACard>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  Ready to Join Our Creator Community?
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                  Start monetizing your gaming content today and connect with amazing game developers.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: '#ffffff',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.9),
                      },
                    }}
                    onClick={() => setApplyDialogOpen(true)}
                  >
                    Apply Now
                  </Button>
                  <Link href="/contact" passHref style={{ textDecoration: 'none' }}>
                    <Button
                      variant="outlined"
                      size="large"
                      sx={{
                        borderColor: '#ffffff',
                        color: '#ffffff',
                        '&:hover': {
                          borderColor: '#ffffff',
                          bgcolor: alpha('#ffffff', 0.1),
                        },
                      }}
                    >
                      Contact Us
                    </Button>
                  </Link>
                </Box>
              </CTACard>
            </motion.div>
          )}
        </Container>
      </CreatorContainer>

      {/* Application Dialog */}
      <Dialog open={applyDialogOpen} onClose={() => setApplyDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Apply for Creator Program</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Program</InputLabel>
                <Select
                  value={selectedProgram}
                  onChange={(e) => setSelectedProgram(e.target.value)}
                  label="Program"
                >
                  {creatorPrograms.map((program) => (
                    <MenuItem key={program.title} value={program.title}>
                      {program.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Full Name" variant="outlined" required />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Email" variant="outlined" type="email" required />
            </Grid>
            <Grid item xs={12}>
              <TextField fullWidth label="Platform/Channel URL" variant="outlined" required />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Follower Count" variant="outlined" type="number" required />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Content Type" variant="outlined" required />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Why do you want to join our creator program?"
                variant="outlined"
                multiline
                rows={4}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApplyDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleApply}>
            Submit Application
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default CreatorContent;
