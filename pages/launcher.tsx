import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Avatar,
  useTheme,
  alpha,
  Chip,
  IconButton,
  Divider,
  Alert,
  Tab,
  Tabs,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import GameLauncher from '../components/GameLauncher/GameLauncher';

// Icons
import GamesIcon from '@mui/icons-material/Games';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import LoginIcon from '@mui/icons-material/Login';
import AppRegistrationIcon from '@mui/icons-material/AppRegistration';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import UpdateIcon from '@mui/icons-material/Update';

// Styled components
const LauncherContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  flexDirection: 'column',
}));

const LauncherHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  background: alpha(theme.palette.background.paper, 0.1),
  backdropFilter: 'blur(10px)',
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
}));

const QuickActionCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  background: alpha(theme.palette.background.paper, 0.9),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const DesktopLauncher: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const [isDesktop, setIsDesktop] = useState(false);
  const [appVersion, setAppVersion] = useState('2.0.0');
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    // Check if running in desktop app
    if (typeof window !== 'undefined' && (window as any).electronAPI) {
      setIsDesktop(true);
      
      // Get app version
      (window as any).electronAPI.getVersion().then((version: string) => {
        setAppVersion(version);
      });
    }
  }, []);

  const quickActions = [
    {
      title: 'Login',
      description: 'Access your Gamestorme account',
      icon: <LoginIcon />,
      color: theme.palette.primary.main,
      action: () => router.push('/auth/login'),
    },
    {
      title: 'Sign Up',
      description: 'Create a new account',
      icon: <AppRegistrationIcon />,
      color: theme.palette.secondary.main,
      action: () => router.push('/auth/signup'),
    },
    {
      title: 'Developer Dashboard',
      description: 'Manage your games and analytics',
      icon: <DashboardIcon />,
      color: theme.palette.success.main,
      action: () => router.push('/developer/dashboard'),
    },
    {
      title: 'Gamer Dashboard',
      description: 'Discover and play games',
      icon: <GamesIcon />,
      color: theme.palette.info.main,
      action: () => router.push('/gamer/dashboard'),
    },
  ];

  const handleUpdateCheck = async () => {
    if (isDesktop && (window as any).electronAPI) {
      const result = await (window as any).electronAPI.checkForUpdates();
      
      if (result.updateAvailable) {
        await (window as any).electronAPI.showMessageBox({
          type: 'info',
          title: 'Update Available',
          message: 'A new version of Gamestorme is available!',
          detail: 'Would you like to download and install the update?',
          buttons: ['Update Now', 'Later'],
        });
      } else {
        await (window as any).electronAPI.showMessageBox({
          type: 'info',
          title: 'Up to Date',
          message: 'You are running the latest version of Gamestorme!',
          detail: `Version ${appVersion}`,
        });
      }
    }
  };

  return (
    <LauncherContainer>
      {/* Header */}
      <LauncherHeader>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              src="/assets/images/logo.png"
              sx={{ width: 48, height: 48, mr: 2 }}
            />
            <Box>
              <Typography variant="h6" fontWeight="bold" color="white">
                Gamestorme Launcher
              </Typography>
              <Typography variant="caption" color="white" sx={{ opacity: 0.8 }}>
                {isDesktop ? `Desktop v${appVersion}` : 'Web Version'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {isDesktop && (
              <IconButton
                onClick={handleUpdateCheck}
                sx={{ color: 'white' }}
                title="Check for Updates"
              >
                <UpdateIcon />
              </IconButton>
            )}
            <IconButton
              onClick={() => router.push('/')}
              sx={{ color: 'white' }}
              title="Go to Website"
            >
              <ExitToAppIcon />
            </IconButton>
          </Box>
        </Box>
      </LauncherHeader>

      {/* Navigation Tabs */}
      {isDesktop && (
        <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'rgba(0,0,0,0.1)' }}>
          <Container maxWidth="lg">
            <Tabs
              value={activeTab}
              onChange={(e, newValue) => setActiveTab(newValue)}
              sx={{
                '& .MuiTab-root': {
                  color: 'rgba(255,255,255,0.7)',
                  '&.Mui-selected': { color: 'white' }
                }
              }}
            >
              <Tab label="🚀 Quick Launch" />
              <Tab label="🎮 Game Engine" />
            </Tabs>
          </Container>
        </Box>
      )}

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ flexGrow: 1, py: 4 }}>
        {/* Quick Launch Tab */}
        {(!isDesktop || activeTab === 0) && (
          <>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Welcome Section */}
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" fontWeight="bold" color="white" gutterBottom>
              Welcome to Gamestorme
            </Typography>
            <Typography variant="h6" color="white" sx={{ opacity: 0.9, mb: 3 }}>
              Empowering Game Developers Through Innovative Solutions
            </Typography>
            <Chip
              label={isDesktop ? "Desktop Application" : "Web Application"}
              color="secondary"
              sx={{ fontSize: '1rem', py: 1, px: 2 }}
            />
          </Box>

          {/* Quick Actions */}
          <Typography variant="h4" fontWeight="bold" color="white" sx={{ mb: 4, textAlign: 'center' }}>
            Quick Actions
          </Typography>
          
          <Grid container spacing={3} sx={{ mb: 6 }}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <QuickActionCard onClick={action.action}>
                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          bgcolor: alpha(action.color, 0.1),
                          color: action.color,
                          width: 64,
                          height: 64,
                          mx: 'auto',
                          mb: 2,
                        }}
                      >
                        {action.icon}
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {action.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {action.description}
                      </Typography>
                    </CardContent>
                  </QuickActionCard>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Desktop Features */}
          {isDesktop && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card sx={{ 
                background: alpha(theme.palette.background.paper, 0.9),
                backdropFilter: 'blur(10px)',
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <CloudDownloadIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
                    Desktop Features
                  </Typography>
                  <Divider sx={{ my: 2 }} />
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Typography variant="h6" fontWeight="medium" gutterBottom>
                        🚀 Enhanced Performance
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Native desktop performance with optimized resource usage and faster loading times.
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Typography variant="h6" fontWeight="medium" gutterBottom>
                        🔔 Desktop Notifications
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Get real-time notifications for game updates, messages, and important events.
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Typography variant="h6" fontWeight="medium" gutterBottom>
                        ⚡ Offline Capabilities
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Access your dashboard and manage games even when offline.
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Footer */}
          <Box sx={{ textAlign: 'center', mt: 6 }}>
            <Typography variant="body2" color="white" sx={{ opacity: 0.7 }}>
              © 2024 Gamestorme. All rights reserved. | Version {appVersion}
            </Typography>
          </Box>
        </motion.div>
        )}

        {/* Game Engine Tab */}
        {isDesktop && activeTab === 1 && (
          <Box sx={{ mt: -4 }}>
            <GameLauncher />
          </Box>
        )}
      </Container>
    </LauncherContainer>
  );
};

export default DesktopLauncher;
