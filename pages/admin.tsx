import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  TextField,
  Avatar,
  useTheme,
  alpha,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  AppBar,
  Toolbar,
  Drawer,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import EnhancedDashboard from '../components/shared/EnhancedDashboard';
import EnhancedStatsCard from '../components/shared/EnhancedStatsCard';
import GlassCard from '../components/shared/GlassCard';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import GamesIcon from '@mui/icons-material/Games';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import NotificationsIcon from '@mui/icons-material/Notifications';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import PaymentIcon from '@mui/icons-material/Payment';
import RefreshIcon from '@mui/icons-material/Refresh';
import DownloadIcon from '@mui/icons-material/Download';
import ChatIcon from '@mui/icons-material/Chat';
import SecurityIcon from '@mui/icons-material/Security';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CampaignIcon from '@mui/icons-material/Campaign';
import DescriptionIcon from '@mui/icons-material/Description';

// Styled components
const AdminContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  backgroundColor: theme.palette.background.default,
}));

const Sidebar = styled(Box)(({ theme }) => ({
  width: 280,
  backgroundColor: theme.palette.background.paper,
  borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  display: 'flex',
  flexDirection: 'column',
  position: 'fixed',
  height: '100vh',
  zIndex: 1200,
  boxShadow: `4px 0 20px ${alpha(theme.palette.common.black, 0.05)}`,
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  marginLeft: 280,
  padding: theme.spacing(3),
  backgroundColor: alpha(theme.palette.background.default, 0.5),
  minHeight: '100vh',
}));

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.7)})`,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.08)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.12)}`,
  },
}));

// Sample admin data
const adminStats = [
  { title: 'Total Users', value: '12,847', change: '+8.2%', icon: <PeopleIcon />, color: '#6366f1' },
  { title: 'Active Games', value: '3,421', change: '+12.5%', icon: <GamesIcon />, color: '#10b981' },
  { title: 'Revenue', value: '$847K', change: '+15.3%', icon: <AttachMoneyIcon />, color: '#f59e0b' },
  { title: 'Support Tickets', value: '127', change: '-5.2%', icon: <ChatIcon />, color: '#ef4444' },
];

const AdminDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [activeSection, setActiveSection] = useState('dashboard');
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);

  // Check authentication
  useEffect(() => {
    const adminAuth = localStorage.getItem('adminAuth');
    if (adminAuth === 'authenticated') {
      setIsAuthenticated(true);
    }
  }, []);

  const handleLogin = () => {
    if (loginForm.email === '<EMAIL>' && loginForm.password === 'Password1Thanks!') {
      localStorage.setItem('adminAuth', 'authenticated');
      setIsAuthenticated(true);
    } else {
      alert('Invalid credentials');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminAuth');
    setIsAuthenticated(false);
    setLogoutDialogOpen(false);
    router.push('/');
  };

  // Login screen
  if (!isAuthenticated) {
    return (
      <Box sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
      }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card sx={{ p: 4, borderRadius: 3, minWidth: 400 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Gamestorme Admin
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Employee Portal Access
              </Typography>
            </Box>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={loginForm.email}
              onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Password"
              type="password"
              value={loginForm.password}
              onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
              sx={{ mb: 3 }}
              onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
            />
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleLogin}
              sx={{ borderRadius: 2 }}
            >
              Login to Admin Portal
            </Button>
          </Card>
        </motion.div>
      </Box>
    );
  }

  // Sidebar items configuration
  const sidebarItems = [
    { id: 'dashboard', label: 'Executive Dashboard', icon: <DashboardIcon /> },
    { id: 'analytics', label: 'Business Intelligence', icon: <AnalyticsIcon /> },
    { id: 'users', label: 'User Management', icon: <PeopleIcon />, badge: '12.8K' },
    { id: 'games', label: 'Content Approval', icon: <GamesIcon />, badge: '3', color: theme.palette.warning.main },
    { id: 'payments', label: 'Financial Operations', icon: <PaymentIcon /> },
    { id: 'revenue', label: 'Revenue Analytics', icon: <AttachMoneyIcon /> },
    { id: 'marketing', label: 'Marketing Hub', icon: <CampaignIcon /> },
    { id: 'support', label: 'Customer Success', icon: <ChatIcon />, badge: '127', color: theme.palette.error.main },
    { id: 'employees', label: 'HR Management', icon: <PeopleIcon /> },
    { id: 'reports', label: 'Executive Reports', icon: <DescriptionIcon /> },
    { id: 'security', label: 'Security Center', icon: <SecurityIcon /> },
    { id: 'settings', label: 'System Administration', icon: <SettingsIcon /> },
  ];

  const headerActions = (
    <>
      <Button
        variant="outlined"
        startIcon={<DownloadIcon />}
        sx={{ mr: 2 }}
      >
        Export Data
      </Button>
      <Button
        variant="contained"
        startIcon={<RefreshIcon />}
        color="primary"
      >
        Refresh
      </Button>
    </>
  );

  return (
    <EnhancedDashboard
      title="Admin Dashboard"
      subtitle="Welcome back, Joel! Here's what's happening on Gamestorme today."
      userInitial="J"
      notificationCount={5}
      sidebarItems={sidebarItems}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      headerActions={headerActions}
    >

      {/* Dashboard Content */}
      {activeSection === 'dashboard' && (
        <>
          {/* Executive Summary */}
          <GlassCard sx={{ mb: 4, p: 4 }} hoverEffect="glow">
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Executive Summary
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Platform performance overview for today. All systems operational and performing within expected parameters.
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" fontWeight="bold" color="success.main">
                    99.9%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    System Uptime
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" fontWeight="bold" color="primary.main">
                    2.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    API Requests Today
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" fontWeight="bold" color="warning.main">
                    $847K
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Monthly Revenue
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </GlassCard>

          {/* Enhanced Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Total Users"
                value="12,847"
                change="+8.2%"
                changeType="positive"
                icon={<PeopleIcon />}
                color="#6366f1"
                animationDelay={0}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Active Games"
                value="3,421"
                change="+12.5%"
                changeType="positive"
                icon={<GamesIcon />}
                color="#10b981"
                animationDelay={0.1}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Revenue"
                value="$847K"
                change="+15.3%"
                changeType="positive"
                icon={<AttachMoneyIcon />}
                color="#f59e0b"
                animationDelay={0.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Support Tickets"
                value="127"
                change="-5.2%"
                changeType="positive"
                icon={<ChatIcon />}
                color="#ef4444"
                subtitle="Resolved faster"
                animationDelay={0.3}
              />
            </Grid>
          </Grid>

          {/* Recent Activities */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={8}>
              <GlassCard hoverEffect="lift">
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Recent Platform Activities
                  </Typography>
                  <List>
                    {[
                      { action: 'New game submitted', user: 'IndieDev Studios', time: '5 minutes ago', status: 'pending' },
                      { action: 'Payment processed', user: 'GameMaker Pro', time: '12 minutes ago', status: 'success' },
                      { action: 'Support ticket resolved', user: 'PixelCraft Games', time: '1 hour ago', status: 'success' },
                      { action: 'User account created', user: 'NewDeveloper123', time: '2 hours ago', status: 'info' },
                      { action: 'Security scan completed', user: 'System', time: '3 hours ago', status: 'success' },
                    ].map((activity, index) => (
                      <ListItem key={index} sx={{
                        borderRadius: 2,
                        mb: 1,
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.05),
                        }
                      }}>
                        <ListItemIcon>
                          <Avatar sx={{
                            bgcolor: activity.status === 'pending' ? alpha(theme.palette.warning.main, 0.2) :
                                     activity.status === 'success' ? alpha(theme.palette.success.main, 0.2) :
                                     alpha(theme.palette.info.main, 0.2),
                            color: activity.status === 'pending' ? theme.palette.warning.main :
                                   activity.status === 'success' ? theme.palette.success.main :
                                   theme.palette.info.main,
                            width: 40,
                            height: 40,
                            border: `2px solid ${activity.status === 'pending' ? alpha(theme.palette.warning.main, 0.3) :
                                                  activity.status === 'success' ? alpha(theme.palette.success.main, 0.3) :
                                                  alpha(theme.palette.info.main, 0.3)}`
                          }}>
                            {activity.status === 'pending' ? <AccessTimeIcon fontSize="small" /> :
                             activity.status === 'success' ? <CheckCircleIcon fontSize="small" /> :
                             <PeopleIcon fontSize="small" />}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle2" fontWeight={600}>
                              {activity.action}
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              {activity.user} • {activity.time}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </GlassCard>
            </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ borderRadius: 3, height: '100%' }}>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Employee Timesheet
                      </Typography>
                      <Box sx={{ textAlign: 'center', mt: 3 }}>
                        <Typography variant="h3" fontWeight="bold" color="primary">
                          7:42:15
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Hours worked today
                        </Typography>
                        <Button variant="contained" color="success" sx={{ mt: 2, borderRadius: 2 }}>
                          Clock Out
                        </Button>
                      </Box>
                      <Divider sx={{ my: 3 }} />
                      <Typography variant="body2" color="text.secondary">
                        <strong>This Week:</strong> 38.5 hours<br />
                        <strong>This Month:</strong> 162 hours<br />
                        <strong>Overtime:</strong> 2.5 hours
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
        </>
      )}

          {/* User Management */}
          {activeSection === 'users' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h5" fontWeight="bold">
                  User Management
                </Typography>
                <Button variant="outlined" startIcon={<DownloadIcon />}>
                  Export Users
                </Button>
              </Box>
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>User</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell>Games</TableCell>
                          <TableCell>Revenue</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {[
                          { name: 'IndieDev Studios', email: '<EMAIL>', type: 'Developer', games: 5, revenue: '$12,450', status: 'Active' },
                          { name: 'GameMaker Pro', email: '<EMAIL>', type: 'Developer', games: 12, revenue: '$45,200', status: 'Active' },
                          { name: 'CasualGamer123', email: '<EMAIL>', type: 'Gamer', games: 0, revenue: '$0', status: 'Active' },
                        ].map((user, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Avatar sx={{ mr: 2, bgcolor: theme.palette.primary.main }}>
                                  {user.name.charAt(0)}
                                </Avatar>
                                <Box>
                                  <Typography variant="subtitle2" fontWeight="bold">
                                    {user.name}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {user.email}
                                  </Typography>
                                </Box>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Chip label={user.type} size="small" color={user.type === 'Developer' ? 'primary' : 'secondary'} />
                            </TableCell>
                            <TableCell>{user.games}</TableCell>
                            <TableCell>{user.revenue}</TableCell>
                            <TableCell>
                              <Chip label={user.status} size="small" color="success" />
                            </TableCell>
                            <TableCell>
                              <IconButton size="small">
                                <VisibilityIcon />
                              </IconButton>
                              <IconButton size="small">
                                <EditIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Game Approval */}
          {activeSection === 'games' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 4 }}>
                Game Approval Queue
              </Typography>
              <Grid container spacing={3}>
                {[
                  { title: 'Cosmic Adventure', developer: 'IndieDev Studios', submitted: '2024-01-15', status: 'Pending Review' },
                  { title: 'Puzzle Master', developer: 'BrainGames Inc', submitted: '2024-01-14', status: 'Under Review' },
                  { title: 'Racing Thunder', developer: 'SpeedDev', submitted: '2024-01-13', status: 'Approved' },
                ].map((game, index) => (
                  <Grid item xs={12} md={6} lg={4} key={index}>
                    <Card sx={{ borderRadius: 3 }}>
                      <Box sx={{ height: 200, bgcolor: alpha(theme.palette.primary.main, 0.1), display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <GamesIcon sx={{ fontSize: 60, color: theme.palette.primary.main, opacity: 0.7 }} />
                      </Box>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {game.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          by {game.developer}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Submitted: {game.submitted}
                        </Typography>
                        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Chip
                            label={game.status}
                            size="small"
                            color={game.status === 'Approved' ? 'success' : game.status === 'Under Review' ? 'warning' : 'default'}
                          />
                          <Box>
                            <IconButton size="small" color="success">
                              <CheckCircleIcon />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <CancelIcon />
                            </IconButton>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Payment Management */}
          {activeSection === 'payments' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 4 }}>
                Payment Management
              </Typography>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center' }}>
                    <Typography variant="h4" fontWeight="bold" color="success.main">
                      $847,230
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center' }}>
                    <Typography variant="h4" fontWeight="bold" color="warning.main">
                      $23,450
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Payouts
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center' }}>
                    <Typography variant="h4" fontWeight="bold" color="error.main">
                      $1,250
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Disputed Payments
                    </Typography>
                  </Card>
                </Grid>
              </Grid>
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Recent Transactions
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Transaction ID</TableCell>
                          <TableCell>Developer</TableCell>
                          <TableCell>Amount</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {[
                          { id: 'TXN-001', developer: 'IndieDev Studios', amount: '$2,450', type: 'Payout', status: 'Completed' },
                          { id: 'TXN-002', developer: 'GameMaker Pro', amount: '$5,200', type: 'Payout', status: 'Pending' },
                          { id: 'TXN-003', developer: 'PixelCraft', amount: '$1,800', type: 'Refund', status: 'Processing' },
                        ].map((transaction, index) => (
                          <TableRow key={index}>
                            <TableCell>{transaction.id}</TableCell>
                            <TableCell>{transaction.developer}</TableCell>
                            <TableCell>{transaction.amount}</TableCell>
                            <TableCell>
                              <Chip label={transaction.type} size="small" variant="outlined" />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={transaction.status}
                                size="small"
                                color={transaction.status === 'Completed' ? 'success' : transaction.status === 'Pending' ? 'warning' : 'info'}
                              />
                            </TableCell>
                            <TableCell>
                              <Button size="small" variant="outlined">
                                Process
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Business Intelligence Analytics */}
          {activeSection === 'analytics' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Business Intelligence Dashboard
              </Typography>

              {/* KPI Overview */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Monthly Recurring Revenue', value: '$127K', change: '+23%', icon: <AttachMoneyIcon />, color: '#10b981' },
                  { title: 'Customer Acquisition Cost', value: '$45', change: '-12%', icon: <PeopleIcon />, color: '#3b82f6' },
                  { title: 'Lifetime Value', value: '$890', change: '+18%', icon: <AnalyticsIcon />, color: '#8b5cf6' },
                  { title: 'Churn Rate', value: '2.3%', change: '-0.8%', icon: <RefreshIcon />, color: '#ef4444' },
                ].map((kpi, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(kpi.color, 0.1)}` }}>
                      <Avatar sx={{ bgcolor: alpha(kpi.color, 0.1), color: kpi.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {kpi.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" sx={{ color: kpi.color }}>
                        {kpi.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {kpi.title}
                      </Typography>
                      <Chip
                        label={kpi.change}
                        size="small"
                        color={kpi.change.startsWith('+') ? 'success' : 'error'}
                        sx={{ mt: 1 }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Advanced Analytics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Revenue Growth Trajectory
                    </Typography>
                    <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: alpha('#10b981', 0.05), borderRadius: 2 }}>
                      <Typography variant="h6" color="text.secondary">
                        📈 Interactive Revenue Chart
                      </Typography>
                    </Box>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Market Insights
                    </Typography>
                    <List>
                      {[
                        { metric: 'Market Share', value: '12.4%', trend: 'up' },
                        { metric: 'Competitor Analysis', value: 'Leading', trend: 'up' },
                        { metric: 'User Satisfaction', value: '94.2%', trend: 'up' },
                        { metric: 'Platform Stability', value: '99.9%', trend: 'stable' },
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={item.metric}
                            secondary={item.value}
                          />
                          <Chip
                            label={item.trend}
                            size="small"
                            color={item.trend === 'up' ? 'success' : item.trend === 'down' ? 'error' : 'default'}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>

              {/* Detailed Analytics Table */}
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h6" fontWeight="bold">
                      Platform Performance Metrics
                    </Typography>
                    <Button variant="outlined" startIcon={<DownloadIcon />}>
                      Export Analytics
                    </Button>
                  </Box>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Metric</TableCell>
                          <TableCell align="right">Current Value</TableCell>
                          <TableCell align="right">Previous Period</TableCell>
                          <TableCell align="right">Change</TableCell>
                          <TableCell align="right">Target</TableCell>
                          <TableCell align="right">Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {[
                          { metric: 'Daily Active Users', current: '8,450', previous: '7,200', change: '+17.4%', target: '10,000', status: 'On Track' },
                          { metric: 'Revenue per User', current: '$15.20', previous: '$13.80', change: '+10.1%', target: '$18.00', status: 'Good' },
                          { metric: 'Game Completion Rate', current: '68%', previous: '65%', change: '+3%', target: '75%', status: 'Improving' },
                          { metric: 'Support Response Time', current: '2.4h', previous: '3.1h', change: '-22.6%', target: '2h', status: 'Near Target' },
                        ].map((row, index) => (
                          <TableRow key={index}>
                            <TableCell>{row.metric}</TableCell>
                            <TableCell align="right">{row.current}</TableCell>
                            <TableCell align="right">{row.previous}</TableCell>
                            <TableCell align="right">
                              <Chip
                                label={row.change}
                                size="small"
                                color={row.change.startsWith('+') ? 'success' : 'error'}
                              />
                            </TableCell>
                            <TableCell align="right">{row.target}</TableCell>
                            <TableCell align="right">
                              <Chip
                                label={row.status}
                                size="small"
                                color={row.status === 'On Track' ? 'success' : row.status === 'Good' ? 'info' : 'warning'}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Revenue Analytics */}
          {activeSection === 'revenue' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Revenue Analytics & Forecasting
              </Typography>

              {/* Revenue Overview */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={3}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', background: 'linear-gradient(135deg, #10b981, #059669)' }}>
                    <Typography variant="h3" fontWeight="bold" color="white">
                      $2.4M
                    </Typography>
                    <Typography variant="body1" color="white" sx={{ opacity: 0.9 }}>
                      Annual Revenue
                    </Typography>
                    <Typography variant="body2" color="white" sx={{ opacity: 0.8 }}>
                      +34% YoY Growth
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', background: 'linear-gradient(135deg, #3b82f6, #2563eb)' }}>
                    <Typography variant="h3" fontWeight="bold" color="white">
                      $847K
                    </Typography>
                    <Typography variant="body1" color="white" sx={{ opacity: 0.9 }}>
                      Platform Revenue
                    </Typography>
                    <Typography variant="body2" color="white" sx={{ opacity: 0.8 }}>
                      +28% This Quarter
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)' }}>
                    <Typography variant="h3" fontWeight="bold" color="white">
                      $1.6M
                    </Typography>
                    <Typography variant="body1" color="white" sx={{ opacity: 0.9 }}>
                      Developer Payouts
                    </Typography>
                    <Typography variant="body2" color="white" sx={{ opacity: 0.8 }}>
                      67% Revenue Share
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', background: 'linear-gradient(135deg, #f59e0b, #d97706)' }}>
                    <Typography variant="h3" fontWeight="bold" color="white">
                      $3.2M
                    </Typography>
                    <Typography variant="body1" color="white" sx={{ opacity: 0.9 }}>
                      Projected 2024
                    </Typography>
                    <Typography variant="body2" color="white" sx={{ opacity: 0.8 }}>
                      Conservative Estimate
                    </Typography>
                  </Card>
                </Grid>
              </Grid>

              {/* Revenue Breakdown */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Revenue Streams Analysis
                    </Typography>
                    <Box sx={{ height: 350, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: alpha('#3b82f6', 0.05), borderRadius: 2 }}>
                      <Typography variant="h6" color="text.secondary">
                        💰 Revenue Breakdown Chart
                      </Typography>
                    </Box>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Revenue Sources
                    </Typography>
                    <List>
                      {[
                        { source: 'Game Sales', percentage: 45, amount: '$1.08M' },
                        { source: 'Platform Fees', percentage: 25, amount: '$600K' },
                        { source: 'Premium Features', percentage: 20, amount: '$480K' },
                        { source: 'Advertising', percentage: 10, amount: '$240K' },
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0, flexDirection: 'column', alignItems: 'flex-start' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', mb: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {item.source}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {item.amount}
                            </Typography>
                          </Box>
                          <Box sx={{ width: '100%', bgcolor: alpha('#3b82f6', 0.1), borderRadius: 1, height: 8 }}>
                            <Box
                              sx={{
                                width: `${item.percentage}%`,
                                bgcolor: '#3b82f6',
                                height: '100%',
                                borderRadius: 1,
                              }}
                            />
                          </Box>
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            {item.percentage}% of total revenue
                          </Typography>
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Customer Success Center */}
          {activeSection === 'support' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Customer Success Center
              </Typography>

              {/* Support Metrics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Active Tickets', value: '127', change: '-15%', icon: <ChatIcon />, color: '#ef4444' },
                  { title: 'Avg Response Time', value: '2.4h', change: '-22%', icon: <AccessTimeIcon />, color: '#10b981' },
                  { title: 'Customer Satisfaction', value: '98.2%', change: '+2.1%', icon: <CheckCircleIcon />, color: '#3b82f6' },
                  { title: 'Resolution Rate', value: '94.7%', change: '+1.8%', icon: <AnalyticsIcon />, color: '#8b5cf6' },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(metric.color, 0.1)}` }}>
                      <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {metric.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" sx={{ color: metric.color }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.change}
                        size="small"
                        color={metric.change.startsWith('+') ? 'success' : 'error'}
                        sx={{ mt: 1 }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Support Dashboard */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Recent Support Tickets
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Ticket ID</TableCell>
                            <TableCell>User</TableCell>
                            <TableCell>Issue Type</TableCell>
                            <TableCell>Priority</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Assigned To</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {[
                            { id: 'TK-2024-001', user: 'IndieDev Studios', type: 'Payment Issue', priority: 'High', status: 'In Progress', assignee: 'Sarah Chen' },
                            { id: 'TK-2024-002', user: 'GameMaker Pro', type: 'Technical Bug', priority: 'Medium', status: 'Open', assignee: 'Mike Johnson' },
                            { id: 'TK-2024-003', user: 'PixelCraft', type: 'Account Access', priority: 'Low', status: 'Resolved', assignee: 'Lisa Wang' },
                          ].map((ticket, index) => (
                            <TableRow key={index}>
                              <TableCell>{ticket.id}</TableCell>
                              <TableCell>{ticket.user}</TableCell>
                              <TableCell>{ticket.type}</TableCell>
                              <TableCell>
                                <Chip
                                  label={ticket.priority}
                                  size="small"
                                  color={ticket.priority === 'High' ? 'error' : ticket.priority === 'Medium' ? 'warning' : 'default'}
                                />
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={ticket.status}
                                  size="small"
                                  color={ticket.status === 'Resolved' ? 'success' : ticket.status === 'In Progress' ? 'info' : 'default'}
                                />
                              </TableCell>
                              <TableCell>{ticket.assignee}</TableCell>
                              <TableCell>
                                <Button size="small" variant="outlined">
                                  View
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Support Team Performance
                    </Typography>
                    <List>
                      {[
                        { name: 'Sarah Chen', tickets: 45, rating: 4.9 },
                        { name: 'Mike Johnson', tickets: 38, rating: 4.8 },
                        { name: 'Lisa Wang', tickets: 42, rating: 4.7 },
                        { name: 'David Kim', tickets: 35, rating: 4.9 },
                      ].map((agent, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <Avatar sx={{ mr: 2, bgcolor: '#3b82f6' }}>
                            {agent.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          <ListItemText
                            primary={agent.name}
                            secondary={`${agent.tickets} tickets • ${agent.rating}★ rating`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>

              {/* Knowledge Base Management */}
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Knowledge Base Analytics
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Most Viewed Articles
                      </Typography>
                      <List>
                        {[
                          'How to upload your first game',
                          'Payment and revenue sharing explained',
                          'Troubleshooting common upload issues',
                          'Marketing your game effectively',
                          'Understanding analytics dashboard',
                        ].map((article, index) => (
                          <ListItem key={index} sx={{ px: 0 }}>
                            <ListItemText
                              primary={article}
                              secondary={`${Math.floor(Math.random() * 1000) + 500} views this month`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Support Channels Performance
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        {[
                          { channel: 'Live Chat', satisfaction: 98, volume: 45 },
                          { channel: 'Email Support', satisfaction: 94, volume: 35 },
                          { channel: 'Phone Support', satisfaction: 96, volume: 15 },
                          { channel: 'Community Forum', satisfaction: 89, volume: 25 },
                        ].map((channel, index) => (
                          <Box key={index} sx={{ mb: 2 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="body2" fontWeight="medium">
                                {channel.channel}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {channel.satisfaction}% satisfaction
                              </Typography>
                            </Box>
                            <Box sx={{ width: '100%', bgcolor: alpha('#3b82f6', 0.1), borderRadius: 1, height: 8 }}>
                              <Box
                                sx={{
                                  width: `${channel.volume}%`,
                                  bgcolor: '#3b82f6',
                                  height: '100%',
                                  borderRadius: 1,
                                }}
                              />
                            </Box>
                          </Box>
                        ))}
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Marketing Hub */}
          {activeSection === 'marketing' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Marketing Hub & Campaign Management
              </Typography>

              {/* Marketing Metrics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Active Campaigns', value: '12', change: '+3', icon: <CampaignIcon />, color: '#f59e0b' },
                  { title: 'Conversion Rate', value: '3.8%', change: '+0.5%', icon: <AnalyticsIcon />, color: '#10b981' },
                  { title: 'Marketing ROI', value: '340%', change: '+45%', icon: <AttachMoneyIcon />, color: '#3b82f6' },
                  { title: 'Reach', value: '2.4M', change: '+18%', icon: <PeopleIcon />, color: '#8b5cf6' },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(metric.color, 0.1)}` }}>
                      <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {metric.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" sx={{ color: metric.color }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.change}
                        size="small"
                        color="success"
                        sx={{ mt: 1 }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Campaign Management */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Active Marketing Campaigns
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Campaign</TableCell>
                            <TableCell>Type</TableCell>
                            <TableCell>Budget</TableCell>
                            <TableCell>Spent</TableCell>
                            <TableCell>Performance</TableCell>
                            <TableCell>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {[
                            { name: 'Holiday Game Fest', type: 'Social Media', budget: '$15K', spent: '$12.3K', performance: '125%', status: 'Active' },
                            { name: 'Indie Developer Spotlight', type: 'Content Marketing', budget: '$8K', spent: '$6.2K', performance: '98%', status: 'Active' },
                            { name: 'Mobile Gaming Push', type: 'Paid Ads', budget: '$25K', spent: '$18.7K', performance: '142%', status: 'Active' },
                          ].map((campaign, index) => (
                            <TableRow key={index}>
                              <TableCell>{campaign.name}</TableCell>
                              <TableCell>{campaign.type}</TableCell>
                              <TableCell>{campaign.budget}</TableCell>
                              <TableCell>{campaign.spent}</TableCell>
                              <TableCell>
                                <Chip
                                  label={campaign.performance}
                                  size="small"
                                  color={parseInt(campaign.performance) > 100 ? 'success' : 'warning'}
                                />
                              </TableCell>
                              <TableCell>
                                <Chip label={campaign.status} size="small" color="success" />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Marketing Channels
                    </Typography>
                    <List>
                      {[
                        { channel: 'Social Media', performance: 85, cost: '$12K' },
                        { channel: 'Google Ads', performance: 92, cost: '$18K' },
                        { channel: 'Content Marketing', performance: 78, cost: '$8K' },
                        { channel: 'Influencer Partnerships', performance: 95, cost: '$15K' },
                      ].map((channel, index) => (
                        <ListItem key={index} sx={{ px: 0, flexDirection: 'column', alignItems: 'flex-start' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', mb: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {channel.channel}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {channel.cost}
                            </Typography>
                          </Box>
                          <Box sx={{ width: '100%', bgcolor: alpha('#f59e0b', 0.1), borderRadius: 1, height: 8 }}>
                            <Box
                              sx={{
                                width: `${channel.performance}%`,
                                bgcolor: '#f59e0b',
                                height: '100%',
                                borderRadius: 1,
                              }}
                            />
                          </Box>
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            {channel.performance}% performance score
                          </Typography>
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* HR Management */}
          {activeSection === 'employees' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Human Resources Management
              </Typography>

              {/* HR Metrics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Total Employees', value: '47', change: '+3', icon: <PeopleIcon />, color: '#3b82f6' },
                  { title: 'Avg Productivity', value: '94%', change: '+2%', icon: <AnalyticsIcon />, color: '#10b981' },
                  { title: 'Employee Satisfaction', value: '4.8/5', change: '+0.2', icon: <CheckCircleIcon />, color: '#f59e0b' },
                  { title: 'Retention Rate', value: '96%', change: '+1%', icon: <SecurityIcon />, color: '#8b5cf6' },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(metric.color, 0.1)}` }}>
                      <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {metric.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" sx={{ color: metric.color }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.change}
                        size="small"
                        color="success"
                        sx={{ mt: 1 }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Employee Management */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Employee Directory
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Employee</TableCell>
                            <TableCell>Department</TableCell>
                            <TableCell>Position</TableCell>
                            <TableCell>Hours This Week</TableCell>
                            <TableCell>Performance</TableCell>
                            <TableCell>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {[
                            { name: 'Joel Rodriguez', dept: 'Executive', position: 'CEO & Founder', hours: '45.5', performance: '98%', status: 'Active' },
                            { name: 'Sarah Chen', dept: 'Customer Success', position: 'Support Manager', hours: '40.0', performance: '96%', status: 'Active' },
                            { name: 'Mike Johnson', dept: 'Engineering', position: 'Lead Developer', hours: '42.5', performance: '94%', status: 'Active' },
                            { name: 'Lisa Wang', dept: 'Marketing', position: 'Marketing Director', hours: '38.5', performance: '97%', status: 'Active' },
                          ].map((employee, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar sx={{ mr: 2, bgcolor: '#3b82f6' }}>
                                    {employee.name.split(' ').map(n => n[0]).join('')}
                                  </Avatar>
                                  <Typography variant="subtitle2" fontWeight="bold">
                                    {employee.name}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>{employee.dept}</TableCell>
                              <TableCell>{employee.position}</TableCell>
                              <TableCell>{employee.hours}h</TableCell>
                              <TableCell>
                                <Chip label={employee.performance} size="small" color="success" />
                              </TableCell>
                              <TableCell>
                                <Chip label={employee.status} size="small" color="success" />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Department Overview
                    </Typography>
                    <List>
                      {[
                        { dept: 'Engineering', count: 15, budget: '$180K' },
                        { dept: 'Customer Success', count: 8, budget: '$95K' },
                        { dept: 'Marketing', count: 12, budget: '$140K' },
                        { dept: 'Operations', count: 6, budget: '$85K' },
                        { dept: 'Executive', count: 6, budget: '$250K' },
                      ].map((dept, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={dept.dept}
                            secondary={`${dept.count} employees • ${dept.budget} budget`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Executive Reports */}
          {activeSection === 'reports' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Executive Reports & Business Intelligence
              </Typography>

              {/* Report Categories */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Financial Reports', icon: <AttachMoneyIcon />, color: '#10b981', count: 12 },
                  { title: 'User Analytics', icon: <AnalyticsIcon />, color: '#3b82f6', count: 8 },
                  { title: 'Performance Reports', icon: <DescriptionIcon />, color: '#f59e0b', count: 15 },
                  { title: 'Compliance Reports', icon: <SecurityIcon />, color: '#ef4444', count: 6 },
                ].map((category, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(category.color, 0.1)}`, cursor: 'pointer' }}>
                      <Avatar sx={{ bgcolor: alpha(category.color, 0.1), color: category.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {category.icon}
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {category.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {category.count} available reports
                      </Typography>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Recent Reports */}
              <Card sx={{ borderRadius: 3, mb: 4 }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h6" fontWeight="bold">
                      Recent Executive Reports
                    </Typography>
                    <Button variant="contained" startIcon={<DownloadIcon />}>
                      Generate New Report
                    </Button>
                  </Box>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Report Name</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell>Generated</TableCell>
                          <TableCell>Period</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {[
                          { name: 'Q4 2023 Financial Summary', type: 'Financial', generated: '2024-01-15', period: 'Q4 2023', status: 'Ready' },
                          { name: 'User Growth Analysis', type: 'Analytics', generated: '2024-01-14', period: 'December 2023', status: 'Ready' },
                          { name: 'Platform Performance Review', type: 'Performance', generated: '2024-01-13', period: 'Annual 2023', status: 'Ready' },
                          { name: 'Security Compliance Audit', type: 'Compliance', generated: '2024-01-12', period: 'Q4 2023', status: 'Processing' },
                        ].map((report, index) => (
                          <TableRow key={index}>
                            <TableCell>{report.name}</TableCell>
                            <TableCell>
                              <Chip label={report.type} size="small" variant="outlined" />
                            </TableCell>
                            <TableCell>{report.generated}</TableCell>
                            <TableCell>{report.period}</TableCell>
                            <TableCell>
                              <Chip
                                label={report.status}
                                size="small"
                                color={report.status === 'Ready' ? 'success' : 'warning'}
                              />
                            </TableCell>
                            <TableCell>
                              <Button size="small" variant="outlined" startIcon={<DownloadIcon />}>
                                Download
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>

              {/* Key Insights */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Key Business Insights
                    </Typography>
                    <List>
                      {[
                        { insight: 'Revenue growth accelerated 34% YoY', impact: 'High', trend: 'positive' },
                        { insight: 'User acquisition cost decreased 12%', impact: 'Medium', trend: 'positive' },
                        { insight: 'Platform stability improved to 99.9%', impact: 'High', trend: 'positive' },
                        { insight: 'Support response time reduced by 22%', impact: 'Medium', trend: 'positive' },
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={item.insight}
                            secondary={`${item.impact} impact`}
                          />
                          <Chip
                            label={item.trend}
                            size="small"
                            color="success"
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Action Items & Recommendations
                    </Typography>
                    <List>
                      {[
                        { action: 'Expand marketing budget for Q1 2024', priority: 'High' },
                        { action: 'Hire 3 additional support agents', priority: 'Medium' },
                        { action: 'Implement advanced analytics features', priority: 'High' },
                        { action: 'Review and optimize server infrastructure', priority: 'Low' },
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText primary={item.action} />
                          <Chip
                            label={item.priority}
                            size="small"
                            color={item.priority === 'High' ? 'error' : item.priority === 'Medium' ? 'warning' : 'default'}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Security Center */}
          {activeSection === 'security' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Security Center & Compliance
              </Typography>

              {/* Security Metrics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Security Score', value: '98/100', change: '+2', icon: <SecurityIcon />, color: '#10b981' },
                  { title: 'Threats Blocked', value: '1,247', change: '+156', icon: <CancelIcon />, color: '#ef4444' },
                  { title: 'Uptime', value: '99.9%', change: '+0.1%', icon: <CheckCircleIcon />, color: '#3b82f6' },
                  { title: 'Compliance Score', value: '100%', change: '0%', icon: <DescriptionIcon />, color: '#8b5cf6' },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(metric.color, 0.1)}` }}>
                      <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {metric.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" sx={{ color: metric.color }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.change}
                        size="small"
                        color={metric.change.startsWith('+') ? 'success' : 'default'}
                        sx={{ mt: 1 }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Security Dashboard */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Security Events & Monitoring
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Event Type</TableCell>
                            <TableCell>Severity</TableCell>
                            <TableCell>Source</TableCell>
                            <TableCell>Time</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {[
                            { type: 'Failed Login Attempt', severity: 'Medium', source: '*************', time: '2 min ago', status: 'Blocked' },
                            { type: 'Suspicious API Call', severity: 'High', source: 'External', time: '15 min ago', status: 'Investigating' },
                            { type: 'DDoS Attempt', severity: 'High', source: 'Multiple IPs', time: '1 hour ago', status: 'Mitigated' },
                            { type: 'Unauthorized Access', severity: 'Critical', source: 'Unknown', time: '3 hours ago', status: 'Resolved' },
                          ].map((event, index) => (
                            <TableRow key={index}>
                              <TableCell>{event.type}</TableCell>
                              <TableCell>
                                <Chip
                                  label={event.severity}
                                  size="small"
                                  color={event.severity === 'Critical' ? 'error' : event.severity === 'High' ? 'warning' : 'default'}
                                />
                              </TableCell>
                              <TableCell>{event.source}</TableCell>
                              <TableCell>{event.time}</TableCell>
                              <TableCell>
                                <Chip
                                  label={event.status}
                                  size="small"
                                  color={event.status === 'Resolved' || event.status === 'Blocked' || event.status === 'Mitigated' ? 'success' : 'warning'}
                                />
                              </TableCell>
                              <TableCell>
                                <Button size="small" variant="outlined">
                                  Details
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Security Compliance
                    </Typography>
                    <List>
                      {[
                        { standard: 'GDPR Compliance', status: 'Compliant', score: 100 },
                        { standard: 'SOC 2 Type II', status: 'Compliant', score: 98 },
                        { standard: 'ISO 27001', status: 'Compliant', score: 96 },
                        { standard: 'PCI DSS', status: 'Compliant', score: 100 },
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0, flexDirection: 'column', alignItems: 'flex-start' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', mb: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {item.standard}
                            </Typography>
                            <Chip label={item.status} size="small" color="success" />
                          </Box>
                          <Box sx={{ width: '100%', bgcolor: alpha('#10b981', 0.1), borderRadius: 1, height: 8 }}>
                            <Box
                              sx={{
                                width: `${item.score}%`,
                                bgcolor: '#10b981',
                                height: '100%',
                                borderRadius: 1,
                              }}
                            />
                          </Box>
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            {item.score}% compliance score
                          </Typography>
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* System Administration */}
          {activeSection === 'settings' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                System Administration
              </Typography>

              {/* System Health */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Server Health', value: '99.9%', status: 'Excellent', icon: <CheckCircleIcon />, color: '#10b981' },
                  { title: 'Database Performance', value: '98.5%', status: 'Good', icon: <AnalyticsIcon />, color: '#3b82f6' },
                  { title: 'API Response Time', value: '45ms', status: 'Optimal', icon: <AccessTimeIcon />, color: '#f59e0b' },
                  { title: 'Storage Usage', value: '67%', status: 'Normal', icon: <DescriptionIcon />, color: '#8b5cf6' },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(metric.color, 0.1)}` }}>
                      <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {metric.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" sx={{ color: metric.color }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.status}
                        size="small"
                        color="success"
                        sx={{ mt: 1 }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* System Configuration */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Platform Configuration
                    </Typography>
                    <List>
                      {[
                        { setting: 'Maintenance Mode', value: 'Disabled', action: 'Toggle' },
                        { setting: 'User Registration', value: 'Enabled', action: 'Configure' },
                        { setting: 'Payment Processing', value: 'Live', action: 'Manage' },
                        { setting: 'Email Notifications', value: 'Enabled', action: 'Settings' },
                        { setting: 'API Rate Limiting', value: '1000/hour', action: 'Adjust' },
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={item.setting}
                            secondary={item.value}
                          />
                          <Button size="small" variant="outlined">
                            {item.action}
                          </Button>
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      System Logs & Monitoring
                    </Typography>
                    <Box sx={{ height: 300, bgcolor: alpha('#000', 0.05), borderRadius: 2, p: 2, fontFamily: 'monospace', fontSize: '0.8rem', overflow: 'auto' }}>
                      <Typography component="div" sx={{ color: '#10b981', mb: 1 }}>
                        [2024-01-15 14:30:15] INFO: System health check completed successfully
                      </Typography>
                      <Typography component="div" sx={{ color: '#3b82f6', mb: 1 }}>
                        [2024-01-15 14:29:45] INFO: Database backup completed
                      </Typography>
                      <Typography component="div" sx={{ color: '#f59e0b', mb: 1 }}>
                        [2024-01-15 14:28:30] WARN: High CPU usage detected on server-02
                      </Typography>
                      <Typography component="div" sx={{ color: '#10b981', mb: 1 }}>
                        [2024-01-15 14:27:15] INFO: User authentication successful
                      </Typography>
                      <Typography component="div" sx={{ color: '#ef4444', mb: 1 }}>
                        [2024-01-15 14:26:00] ERROR: Failed login attempt from *************
                      </Typography>
                      <Typography component="div" sx={{ color: '#10b981', mb: 1 }}>
                        [2024-01-15 14:25:30] INFO: Payment processed successfully
                      </Typography>
                    </Box>
                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      <Button size="small" variant="outlined" startIcon={<RefreshIcon />}>
                        Refresh
                      </Button>
                      <Button size="small" variant="outlined" startIcon={<DownloadIcon />}>
                        Export Logs
                      </Button>
                    </Box>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}
      {/* Logout Dialog */}
      <Dialog open={logoutDialogOpen} onClose={() => setLogoutDialogOpen(false)}>
        <DialogTitle>Confirm Logout</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to logout from the admin portal?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLogoutDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleLogout} color="error" variant="contained">
            Logout
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedDashboard>
  );
};

export default AdminDashboard;
