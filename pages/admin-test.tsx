import React from 'react';
import { Box, Typography, Container, Card, CardContent } from '@mui/material';

const AdminTest: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Card>
        <CardContent>
          <Typography variant="h4" gutterBottom>
            🎉 Admin Test Page
          </Typography>
          <Typography variant="body1" paragraph>
            This is a simple test page to verify that the admin route is working.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            If you can see this page, the routing is working correctly!
          </Typography>
          <Box sx={{ mt: 3, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
            <Typography variant="body2" color="success.dark">
              ✅ Admin route is accessible
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default AdminTest;
