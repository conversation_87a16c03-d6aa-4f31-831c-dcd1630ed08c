import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  useTheme,
  alpha,
  Paper,
  IconButton,
  Avatar,
  Button,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion, AnimatePresence } from 'framer-motion';
import SendIcon from '@mui/icons-material/Send';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import CampaignIcon from '@mui/icons-material/Campaign';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import BarChartIcon from '@mui/icons-material/BarChart';
import { toast } from 'react-toastify';

// Clean, modern styled components
const InfoSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(20px)',
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  backgroundColor: alpha(theme.palette.background.paper, 0.9),
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'all 0.3s ease',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.2)}`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const ChatContainer = styled(Box)(({ theme }) => ({
  maxWidth: '900px',
  margin: '0 auto',
  padding: theme.spacing(4),
  backgroundColor: alpha(theme.palette.background.paper, 0.95),
  borderRadius: '24px',
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  [theme.breakpoints.down('md')]: {
    margin: theme.spacing(2),
    padding: theme.spacing(3),
  },
}));

const MessageBubble = styled(Paper)<{ isUser?: boolean }>(({ theme, isUser }) => ({
  padding: theme.spacing(2, 3),
  borderRadius: '18px',
  maxWidth: '75%',
  backgroundColor: isUser
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
    : alpha(theme.palette.background.paper, 0.9),
  color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: isUser ? 'none' : `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  transition: 'all 0.2s ease',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
  },
}));

const InputField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '24px',
    backgroundColor: alpha(theme.palette.background.default, 0.8),
    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
    transition: 'all 0.2s ease',
    '& fieldset': {
      border: 'none',
    },
    '&:hover': {
      border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
      boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.1)}`,
    },
    '&.Mui-focused': {
      border: `1px solid ${theme.palette.primary.main}`,
      boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.2)}`,
    },
  },
}));

// Interfaces
interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}





// AI response generator
const generateAIResponse = (userMessage: string): string => {
  const message = userMessage.toLowerCase();

  if (message.includes('strategy') || message.includes('marketing plan')) {
    return `🎯 **Marketing Strategy Recommendation**

Based on your game's characteristics, here's a comprehensive marketing strategy:

**Pre-Launch Phase (3-6 months):**
• Build anticipation with dev blogs and behind-the-scenes content
• Create a Discord community for early supporters
• Partner with gaming influencers for early access content
• Submit to gaming festivals and competitions

**Launch Phase:**
• Coordinate press releases with major gaming outlets
• Execute influencer campaigns across Twitch/YouTube
• Run targeted social media ads on Facebook, Twitter, and Reddit
• Implement email marketing to your wishlist subscribers

**Post-Launch:**
• Monitor and respond to community feedback
• Create post-launch content updates to maintain engagement
• Analyze performance metrics and optimize campaigns

**Key Metrics to Track:**
📊 Wishlist conversion rate, social engagement, influencer ROI, community growth

Would you like me to dive deeper into any specific aspect?`;
  }

  if (message.includes('content') || message.includes('social media') || message.includes('posts')) {
    return `📱 **Social Media Content Strategy**

Here are engaging post ideas for your game launch:

**Twitter/X Posts:**
🚀 "The adventure begins NOW! Download [Game Name] and discover a world where every choice matters. #IndieGame #Gaming"

🎮 "Behind the scenes: Our art team spent 6 months perfecting the lighting system. Here's a before/after comparison! 🧵"

⚡ "LAUNCH WEEK: First 1000 players get exclusive in-game items! Who's ready to join the adventure?"

**Instagram Content:**
📸 Character spotlight posts with lore snippets
🎨 Time-lapse videos of art creation process
📱 Stories with polls asking followers about game preferences

**TikTok Ideas:**
🎵 Game soundtrack with gameplay highlights
😂 "POV: You're a game developer at 3 AM fixing bugs"
🔥 Quick gameplay showcases with trending audio

**Content Calendar:**
• Daily: Community engagement and responses
• 3x/week: Gameplay highlights and tips
• Weekly: Developer insights and updates

Need help with specific platform strategies?`;
  }

  if (message.includes('monetization') || message.includes('pricing') || message.includes('revenue')) {
    return `💰 **Monetization Strategy Analysis**

Based on current market trends and your game type:

**Recommended Primary Model:**
🎯 Premium Purchase ($15-25) + Optional DLC

**Pricing Breakdown:**
• Base Game: $19.99 (sweet spot for indie games)
• Season Pass: $14.99 (3 DLC packs)
• Individual DLC: $5.99 each

**Alternative Revenue Streams:**
✅ Cosmetic items (character skins, weapon designs)
✅ Soundtrack and art book bundles
✅ Merchandise (if community grows large enough)

**Avoid These Models:**
❌ Pay-to-win mechanics (damages reputation)
❌ Aggressive microtransactions in premium games
❌ Loot boxes (regulatory issues)

**Revenue Projections:**
📈 Base game sales: 70%
📈 DLC content: 20%
📈 Cosmetics/extras: 10%

**Launch Strategy:**
• 15% early bird discount for first week
• Bundle deals with soundtrack/art book
• Loyalty rewards for community members

Want me to analyze specific monetization models for your genre?`;
  }

  if (message.includes('analytics') || message.includes('performance') || message.includes('metrics')) {
    return `📊 **Marketing Analytics & Performance Insights**

Here's how to track and optimize your marketing performance:

**Key Performance Indicators (KPIs):**
🎯 Conversion Metrics:
• Wishlist-to-purchase rate: Target 15-25%
• Social media click-through rate: Target 2-5%
• Influencer campaign ROI: Target 3:1 minimum

📱 Social Media Metrics:
• Engagement rate: Target 3-6%
• Follower growth rate: Target 5-10% monthly
• Share/repost rate: Quality indicator

🎮 Game-Specific Metrics:
• Player retention (Day 1, 7, 30)
• Average session length
• User-generated content volume

**Analytics Tools Recommended:**
• Google Analytics 4 for website traffic
• Steam Analytics for wishlist data
• Social media native analytics
• UTM parameters for campaign tracking

**Optimization Strategies:**
🔄 A/B test ad creatives and copy
🔄 Monitor competitor performance
🔄 Adjust targeting based on conversion data
🔄 Optimize posting times for engagement

**Monthly Reporting:**
Create dashboards tracking ROI, engagement trends, and conversion funnels.

Need help setting up specific tracking systems?`;
  }

  // Default response for general queries
  return `🤖 **Stormie AI Marketing Assistant**

I'm here to help you with all aspects of game marketing! I can assist with:

🎯 **Marketing Strategy** - Comprehensive launch and growth plans
📱 **Content Creation** - Social media posts, ad copy, and campaigns
💰 **Monetization** - Pricing strategies and revenue optimization
📊 **Analytics** - Performance tracking and optimization

**Popular Questions:**
• "How should I price my indie game?"
• "Create a social media strategy for my RPG"
• "What marketing channels work best for mobile games?"
• "How do I measure marketing campaign success?"

Try asking me something specific about your game marketing needs! I'm trained on the latest gaming industry trends and best practices.

What would you like to know about marketing your game? 🎮`;
};



const MarketingAI: React.FC = () => {
  const theme = useTheme();
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: `🎮 **Welcome to Stormie AI Marketing Assistant**

I'm your dedicated AI marketing expert powered by advanced machine learning technology! I continuously learn from the Gamestorme platform and user interactions to provide increasingly intelligent insights. Here's how I can help:

**🧠 MACHINE LEARNING POWERED INSIGHTS**
• Learn from every interaction to improve recommendations
• Adapt strategies based on real platform data and user behavior
• Continuously evolve marketing approaches for better results

**💰 SALES & REVENUE OPTIMIZATION**
• Increase sales with data-driven pricing strategies
• Optimize store pages for maximum conversion rates
• Design viral marketing campaigns that generate buzz

**📊 ADVANCED ANALYTICS & INSIGHTS**
• Track and analyze player acquisition costs
• Monitor competitor performance and market trends
• Optimize marketing spend with real-time ROI analysis

**🎯 TARGETED MARKETING CAMPAIGNS**
• Identify and reach your ideal gaming audience
• Create platform-specific campaigns
• Generate high-converting ad copy and visuals

Ready to transform your game marketing? Ask me anything about boosting your sales, analytics, or marketing strategy!`,
      isUser: false,
      timestamp: new Date(),
    }
  ]);

  // Handle message submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: generateAIResponse(userMessage.text),
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500 + Math.random() * 1000);
  };

  return (
    <Layout>
      <PageHeader
        title="AI Marketing"
        highlightedTitle="Platform"
        description="Empowering Game Developers Through Innovative Solutions"
      />

      {/* AI Information Section */}
      <InfoSection>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography variant="h3" fontWeight="bold" sx={{ mb: 3, color: 'primary.main' }}>
                🚀 Gamestorme AI Marketing Platform
              </Typography>
              <Typography variant="h5" sx={{ mb: 3, fontWeight: 600, color: 'text.primary' }}>
                Empowering Game Developers Through Innovative Solutions
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto', fontSize: '1.1rem', lineHeight: 1.7 }}>
                Our main feature is our machine learning AI marketing tool for the developer dashboard. This AI learns from platform interactions and continuously improves its analysis of game market data
                from our platform and external sources to help boost your game development with data-driven insights
                and automated marketing solutions.
              </Typography>
            </Box>

            <Grid container spacing={4} sx={{ mb: 6 }}>
              <Grid item xs={12} md={6}>
                <FeatureCard>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, color: 'primary.main' }}>
                      📊 Unified Platform for Distribution and Marketing
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Streamline your game's journey from development to market success with our integrated platform
                      that combines distribution channels with powerful marketing automation.
                    </Typography>
                  </Box>
                </FeatureCard>
              </Grid>
              <Grid item xs={12} md={6}>
                <FeatureCard>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, color: 'primary.main' }}>
                      🤖 AI-Powered Marketing Insights and Automation
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Get real-time market analysis, competitor insights, and automated campaign optimization
                      powered by our advanced AI that learns from gaming industry trends.
                    </Typography>
                  </Box>
                </FeatureCard>
              </Grid>
            </Grid>

            <Typography variant="h4" fontWeight="bold" sx={{ mb: 4, textAlign: 'center', color: 'primary.main' }}>
              🎯 AI Marketing Tool Features
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <FeatureCard>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <TrendingUpIcon sx={{ color: 'primary.main', mr: 2, fontSize: 32 }} />
                    <Typography variant="h6" fontWeight="600">Help Boost Ideas for Sales</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Get AI-powered suggestions to increase your game sales and revenue.
                  </Typography>
                </FeatureCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FeatureCard>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <MonetizationOnIcon sx={{ color: 'primary.main', mr: 2, fontSize: 32 }} />
                    <Typography variant="h6" fontWeight="600">Adaptive Pricing Strategies</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Optimize your pricing with AI-driven market analysis and competitor insights.
                  </Typography>
                </FeatureCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FeatureCard>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AnalyticsIcon sx={{ color: 'primary.main', mr: 2, fontSize: 32 }} />
                    <Typography variant="h6" fontWeight="600">ASO Enhancement</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Improve your app store optimization with AI-powered keyword and content suggestions.
                  </Typography>
                </FeatureCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FeatureCard>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CampaignIcon sx={{ color: 'primary.main', mr: 2, fontSize: 32 }} />
                    <Typography variant="h6" fontWeight="600">Social Media Integration</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Automate and optimize your social media campaigns across all platforms.
                  </Typography>
                </FeatureCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FeatureCard>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AutoAwesomeIcon sx={{ color: 'primary.main', mr: 2, fontSize: 32 }} />
                    <Typography variant="h6" fontWeight="600">Content Recommendations</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Generate engaging content ideas and marketing materials with AI assistance.
                  </Typography>
                </FeatureCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FeatureCard>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <BarChartIcon sx={{ color: 'primary.main', mr: 2, fontSize: 32 }} />
                    <Typography variant="h6" fontWeight="600">Predictive Analytics</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Forecast market trends and player behavior with advanced AI analytics.
                  </Typography>
                </FeatureCard>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </InfoSection>

      {/* AI Chat Demo Section */}
      <Box sx={{ py: 8, backgroundColor: alpha(theme.palette.background.default, 0.5) }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 2, textAlign: 'center', color: 'primary.main' }}>
              🤖 Try Stormie AI Assistant
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 6, textAlign: 'center', maxWidth: 600, mx: 'auto' }}>
              Experience our AI marketing assistant designed specifically for game developers.
              Get instant insights and strategies to boost your game's success.
            </Typography>

            <ChatContainer>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, pb: 2, borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <SmartToyIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">Stormie AI Marketing Assistant</Typography>
                  <Typography variant="body2" color="text.secondary">
                    🎮 Game Marketing Expert • 📊 Real-time Analytics • 🚀 Sales Optimization
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ mb: 4, minHeight: '300px' }}>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    style={{
                      display: 'flex',
                      justifyContent: message.isUser ? 'flex-end' : 'flex-start',
                      marginBottom: '16px',
                    }}
                  >
                    {!message.isUser && (
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 32, height: 32 }}>
                        <SmartToyIcon sx={{ fontSize: 18 }} />
                      </Avatar>
                    )}
                    <MessageBubble isUser={message.isUser}>
                      <Typography
                        variant="body1"
                        sx={{
                          whiteSpace: 'pre-wrap',
                          lineHeight: 1.6,
                          '& strong': { fontWeight: 'bold' },
                        }}
                      >
                        {message.text}
                      </Typography>
                    </MessageBubble>
                    {message.isUser && (
                      <Avatar sx={{ bgcolor: 'secondary.main', ml: 2, width: 32, height: 32 }}>
                        <PersonIcon sx={{ fontSize: 18 }} />
                      </Avatar>
                    )}
                  </motion.div>
                ))}

                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}
                  >
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 32, height: 32 }}>
                      <SmartToyIcon sx={{ fontSize: 18 }} />
                    </Avatar>
                    <Paper sx={{ p: 2, borderRadius: '16px', backgroundColor: alpha(theme.palette.background.paper, 0.9) }}>
                      <Typography variant="body2" color="text.secondary">
                        Stormie is analyzing your request...
                      </Typography>
                    </Paper>
                  </motion.div>
                )}
              </Box>

              <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', gap: 2, alignItems: 'flex-end' }}>
                <InputField
                  fullWidth
                  multiline
                  maxRows={3}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Ask Stormie about game marketing strategies, pricing optimization, or sales analytics..."
                  variant="outlined"
                  disabled={isTyping}
                />
                <IconButton
                  type="submit"
                  disabled={!inputValue.trim() || isTyping}
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    width: 48,
                    height: 48,
                    '&:hover': { bgcolor: 'primary.dark' },
                    '&:disabled': { bgcolor: 'action.disabled' },
                  }}
                >
                  <SendIcon />
                </IconButton>
              </Box>

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 2, textAlign: 'center' }}>
                💡 <strong>Stormie AI</strong> specializes in game marketing for developers and businesses.
                Get data-driven insights to boost sales and optimize campaigns.
              </Typography>
            </ChatContainer>
          </motion.div>
        </Container>
      </Box>
    </Layout>
  );
};

export default MarketingAI;
