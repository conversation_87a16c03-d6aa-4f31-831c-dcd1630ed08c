import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import PeopleIcon from '@mui/icons-material/People';
import BusinessIcon from '@mui/icons-material/Business';
import DownloadIcon from '@mui/icons-material/Download';
import EmailIcon from '@mui/icons-material/Email';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AssessmentIcon from '@mui/icons-material/Assessment';
import SecurityIcon from '@mui/icons-material/Security';
import PublicIcon from '@mui/icons-material/Public';
import Link from 'next/link';

const InvestorContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const CTACard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
  color: 'white',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  textAlign: 'center',
}));

// Sample financial data
const revenueData = [
  { quarter: 'Q1 2023', revenue: 2.1, growth: 45 },
  { quarter: 'Q2 2023', revenue: 3.2, growth: 52 },
  { quarter: 'Q3 2023', revenue: 4.8, growth: 50 },
  { quarter: 'Q4 2023', revenue: 7.2, growth: 50 },
  { quarter: 'Q1 2024', revenue: 10.8, growth: 50 },
  { quarter: 'Q2 2024', revenue: 16.2, growth: 50 },
];

const userGrowthData = [
  { month: 'Jan', users: 125000, developers: 850 },
  { month: 'Feb', users: 148000, developers: 920 },
  { month: 'Mar', users: 175000, developers: 1050 },
  { month: 'Apr', users: 210000, developers: 1200 },
  { month: 'May', users: 252000, developers: 1380 },
  { month: 'Jun', users: 301000, developers: 1580 },
];

const marketShareData = [
  { name: 'Mobile Games', value: 45, color: '#4229BC' },
  { name: 'PC Games', value: 35, color: '#F0BC2B' },
  { name: 'Console Games', value: 15, color: '#7B65ED' },
  { name: 'VR Games', value: 5, color: '#F7D36E' },
];

const financialHighlights = [
  { metric: 'Total Revenue', value: '$52.3M', growth: '+150%', period: 'YoY' },
  { metric: 'Gross Margin', value: '78%', growth: '+5%', period: 'YoY' },
  { metric: 'Active Users', value: '3.2M', growth: '+200%', period: 'YoY' },
  { metric: 'Developer Payouts', value: '$38.1M', growth: '+180%', period: 'YoY' },
];

const upcomingEvents = [
  { date: '2024-02-15', event: 'Q4 2024 Earnings Call', type: 'Earnings' },
  { date: '2024-03-20', event: 'Gaming Industry Conference', type: 'Conference' },
  { date: '2024-04-10', event: 'Annual Shareholder Meeting', type: 'Meeting' },
  { date: '2024-05-15', event: 'Q1 2025 Earnings Call', type: 'Earnings' },
];

const InvestorRelations: React.FC = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Investor"
        highlightedTitle="Relations"
        description="Discover Gamestorme's financial performance, growth metrics, and investment opportunities in the rapidly expanding gaming platform market."
      />

      <InvestorContainer>
        <Container maxWidth="lg">
          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
            <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
              <Tab label="Financial Overview" />
              <Tab label="Performance Metrics" />
              <Tab label="Reports & Filings" />
              <Tab label="Events & Presentations" />
            </Tabs>
          </Box>

          {/* Financial Overview Tab */}
          {activeTab === 0 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Key Metrics */}
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Financial Highlights
              </Typography>
              <Grid container spacing={3} sx={{ mb: 6 }}>
                {financialHighlights.map((highlight, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <motion.div variants={itemVariants}>
                      <MetricCard>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" fontWeight="bold" color="primary.main">
                            {highlight.value}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            {highlight.metric}
                          </Typography>
                          <Chip
                            label={`${highlight.growth} ${highlight.period}`}
                            color="success"
                            size="small"
                            icon={<TrendingUpIcon />}
                          />
                        </CardContent>
                      </MetricCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>

              {/* Revenue Chart */}
              <Grid container spacing={4} sx={{ mb: 6 }}>
                <Grid item xs={12} md={8}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Revenue Growth Trajectory
                        </Typography>
                        <ResponsiveContainer width="100%" height={300}>
                          <LineChart data={revenueData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="quarter" />
                            <YAxis />
                            <Line 
                              type="monotone" 
                              dataKey="revenue" 
                              stroke={theme.palette.primary.main} 
                              strokeWidth={3}
                              dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 6 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={4}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Market Position
                        </Typography>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={marketShareData}
                              cx="50%"
                              cy="50%"
                              outerRadius={80}
                              dataKey="value"
                              label={({ name, value }) => `${name}: ${value}%`}
                            >
                              {marketShareData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Investment Highlights */}
              <motion.div variants={itemVariants}>
                <MetricCard sx={{ mb: 4 }}>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Investment Highlights
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <List>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <TrendingUpIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Rapid Revenue Growth"
                              secondary="150% year-over-year revenue growth with strong unit economics"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <PeopleIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Growing User Base"
                              secondary="3.2M+ active users with 200% growth rate"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <BusinessIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Developer Ecosystem"
                              secondary="1,580+ active developers with strong retention rates"
                            />
                          </ListItem>
                        </List>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <List>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <AttachMoneyIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Strong Margins"
                              secondary="78% gross margin with scalable business model"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <SecurityIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Market Leadership"
                              secondary="Leading position in indie game distribution"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <PublicIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Global Expansion"
                              secondary="Expanding into new markets with localized offerings"
                            />
                          </ListItem>
                        </List>
                      </Grid>
                    </Grid>
                  </CardContent>
                </MetricCard>
              </motion.div>
            </motion.div>
          )}

          {/* Performance Metrics Tab */}
          {activeTab === 1 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Performance Metrics
              </Typography>
              
              {/* User Growth Chart */}
              <motion.div variants={itemVariants}>
                <MetricCard sx={{ mb: 4 }}>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      User & Developer Growth
                    </Typography>
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart data={userGrowthData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Bar dataKey="users" fill={theme.palette.primary.main} name="Users" />
                        <Bar dataKey="developers" fill={theme.palette.secondary.main} name="Developers" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </MetricCard>
              </motion.div>

              {/* Key Performance Indicators */}
              <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Operational Metrics
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemText 
                              primary="Monthly Active Users"
                              secondary="3.2M (+25% MoM)"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText 
                              primary="Average Revenue Per User"
                              secondary="$16.35 (+12% MoM)"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText 
                              primary="Customer Acquisition Cost"
                              secondary="$8.20 (-15% MoM)"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText 
                              primary="Lifetime Value"
                              secondary="$127.50 (+18% MoM)"
                            />
                          </ListItem>
                        </List>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={6}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Developer Metrics
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemText 
                              primary="Active Developers"
                              secondary="1,580 (+22% MoM)"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText 
                              primary="Games Published"
                              secondary="12,450 (+35% MoM)"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText 
                              primary="Developer Retention"
                              secondary="89% (12-month)"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemText 
                              primary="Average Developer Revenue"
                              secondary="$24,150 (+28% MoM)"
                            />
                          </ListItem>
                        </List>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Reports & Filings Tab */}
          {activeTab === 2 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Reports & Filings
              </Typography>
              
              <Grid container spacing={4}>
                <Grid item xs={12} md={8}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Financial Reports
                        </Typography>
                        <TableContainer>
                          <Table>
                            <TableHead>
                              <TableRow>
                                <TableCell>Document</TableCell>
                                <TableCell>Period</TableCell>
                                <TableCell>Date</TableCell>
                                <TableCell>Action</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              <TableRow>
                                <TableCell>Q4 2024 Earnings Report</TableCell>
                                <TableCell>Q4 2024</TableCell>
                                <TableCell>Jan 15, 2025</TableCell>
                                <TableCell>
                                  <Button size="small" startIcon={<DownloadIcon />}>
                                    Download
                                  </Button>
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Annual Report 2024</TableCell>
                                <TableCell>FY 2024</TableCell>
                                <TableCell>Dec 31, 2024</TableCell>
                                <TableCell>
                                  <Button size="small" startIcon={<DownloadIcon />}>
                                    Download
                                  </Button>
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Q3 2024 Earnings Report</TableCell>
                                <TableCell>Q3 2024</TableCell>
                                <TableCell>Oct 15, 2024</TableCell>
                                <TableCell>
                                  <Button size="small" startIcon={<DownloadIcon />}>
                                    Download
                                  </Button>
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Investor Presentation</TableCell>
                                <TableCell>Q3 2024</TableCell>
                                <TableCell>Oct 15, 2024</TableCell>
                                <TableCell>
                                  <Button size="small" startIcon={<DownloadIcon />}>
                                    Download
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={4}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Quick Links
                        </Typography>
                        <List>
                          <ListItem button>
                            <ListItemIcon>
                              <AssessmentIcon />
                            </ListItemIcon>
                            <ListItemText primary="SEC Filings" />
                          </ListItem>
                          <ListItem button>
                            <ListItemIcon>
                              <BusinessIcon />
                            </ListItemIcon>
                            <ListItemText primary="Corporate Governance" />
                          </ListItem>
                          <ListItem button>
                            <ListItemIcon>
                              <EmailIcon />
                            </ListItemIcon>
                            <ListItemText primary="Email Alerts" />
                          </ListItem>
                          <ListItem button>
                            <ListItemIcon>
                              <CalendarTodayIcon />
                            </ListItemIcon>
                            <ListItemText primary="Financial Calendar" />
                          </ListItem>
                        </List>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Events & Presentations Tab */}
          {activeTab === 3 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Upcoming Events
              </Typography>
              
              <Grid container spacing={4}>
                <Grid item xs={12} md={8}>
                  <motion.div variants={itemVariants}>
                    <MetricCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Investor Calendar
                        </Typography>
                        <TableContainer>
                          <Table>
                            <TableHead>
                              <TableRow>
                                <TableCell>Date</TableCell>
                                <TableCell>Event</TableCell>
                                <TableCell>Type</TableCell>
                                <TableCell>Action</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {upcomingEvents.map((event, index) => (
                                <TableRow key={index}>
                                  <TableCell>{new Date(event.date).toLocaleDateString()}</TableCell>
                                  <TableCell>{event.event}</TableCell>
                                  <TableCell>
                                    <Chip 
                                      label={event.type} 
                                      size="small"
                                      color={event.type === 'Earnings' ? 'primary' : 'secondary'}
                                    />
                                  </TableCell>
                                  <TableCell>
                                    <Button size="small">
                                      Add to Calendar
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </CardContent>
                    </MetricCard>
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={4}>
                  <motion.div variants={itemVariants}>
                    <CTACard>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Investor Contact
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 3, opacity: 0.9 }}>
                        For investor inquiries and additional information
                      </Typography>
                      <Button
                        variant="contained"
                        sx={{
                          bgcolor: '#ffffff',
                          color: 'success.main',
                          '&:hover': {
                            bgcolor: alpha('#ffffff', 0.9),
                          },
                        }}
                        startIcon={<EmailIcon />}
                      >
                        Contact IR Team
                      </Button>
                    </CTACard>
                  </motion.div>
                </Grid>
              </Grid>
            </motion.div>
          )}
        </Container>
      </InvestorContainer>
    </Layout>
  );
};

export default InvestorRelations;
