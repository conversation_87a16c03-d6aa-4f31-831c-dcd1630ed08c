import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CodeIcon from '@mui/icons-material/Code';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import SupportIcon from '@mui/icons-material/Support';
import SecurityIcon from '@mui/icons-material/Security';
import Link from 'next/link';

const DevelopersContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const CTACard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: 'white',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  textAlign: 'center',
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 64,
  height: 64,
  borderRadius: theme.spacing(1),
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  marginBottom: theme.spacing(2),
}));

const features = [
  {
    title: 'Easy Game Upload',
    description: 'Upload your games with our streamlined process. Support for multiple platforms and file formats.',
    icon: <CloudUploadIcon />,
    benefits: [
      'Drag & drop file upload',
      'Multi-platform support',
      'Automated testing',
      'Version management',
    ],
  },
  {
    title: 'AI-Powered Marketing',
    description: 'Leverage Stormie AI to create marketing campaigns, analyze market trends, and optimize your game\'s reach.',
    icon: <AnalyticsIcon />,
    benefits: [
      'Market analysis',
      'ASO optimization',
      'Social media content',
      'Pricing strategies',
    ],
  },
  {
    title: 'Revenue Sharing',
    description: 'Fair and transparent revenue sharing model with competitive rates and instant payouts.',
    icon: <MonetizationOnIcon />,
    benefits: [
      'Competitive revenue split',
      'Instant payouts',
      'Detailed analytics',
      'Multiple payment methods',
    ],
  },
  {
    title: 'Developer Support',
    description: 'Get technical support, marketing guidance, and community access to help your games succeed.',
    icon: <SupportIcon />,
    benefits: [
      '24/7 technical support',
      'Marketing consultation',
      'Developer community',
      'Educational resources',
    ],
  },
  {
    title: 'Secure Infrastructure',
    description: 'Built on Firebase with enterprise-grade security, scalability, and reliability.',
    icon: <SecurityIcon />,
    benefits: [
      'Enterprise security',
      'Global CDN',
      'Auto-scaling',
      'Data protection',
    ],
  },
  {
    title: 'Development Tools',
    description: 'Access to SDKs, APIs, and development tools to integrate with our platform.',
    icon: <CodeIcon />,
    benefits: [
      'REST APIs',
      'SDKs for major platforms',
      'Documentation',
      'Code samples',
    ],
  },
];

const Developers: React.FC = () => {
  const theme = useTheme();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Developer"
        highlightedTitle="Program"
        description="Join our developer ecosystem and reach millions of players worldwide. Get the tools, support, and revenue opportunities you need to succeed."
      />

      <DevelopersContainer>
        <Container maxWidth="lg">
          {/* Overview Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={6} alignItems="center" sx={{ mb: 8 }}>
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Typography variant="h3" fontWeight="bold" gutterBottom>
                    Build. Publish. Succeed.
                  </Typography>
                  <Typography variant="body1" paragraph sx={{ mb: 3, fontSize: '1.1rem' }}>
                    Gamestorme provides everything you need to bring your games to market successfully. 
                    From AI-powered marketing tools to global distribution, we're here to help you succeed.
                  </Typography>
                  <Typography variant="body1" paragraph sx={{ mb: 4, fontSize: '1.1rem' }}>
                    Join thousands of developers who trust Gamestorme to distribute their games and 
                    grow their audience worldwide.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Link href="/developer/dashboard" passHref style={{ textDecoration: 'none' }}>
                      <Button
                        variant="contained"
                        size="large"
                        endIcon={<ArrowForwardIcon />}
                      >
                        Get Started
                      </Button>
                    </Link>
                    <Link href="/docs" passHref style={{ textDecoration: 'none' }}>
                      <Button
                        variant="outlined"
                        size="large"
                      >
                        View Documentation
                      </Button>
                    </Link>
                  </Box>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    }}
                  >
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      Why Choose Gamestorme?
                    </Typography>
                    <List>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CheckCircleOutlineIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="Global reach with millions of active users" />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CheckCircleOutlineIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="AI-powered marketing and analytics tools" />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CheckCircleOutlineIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="Competitive revenue sharing (up to 85%)" />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CheckCircleOutlineIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="Enterprise-grade infrastructure" />
                      </ListItem>
                      <ListItem disablePadding>
                        <ListItemIcon>
                          <CheckCircleOutlineIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary="Dedicated developer support" />
                      </ListItem>
                    </List>
                  </Paper>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom sx={{ mb: 6 }}>
              Developer Platform Features
            </Typography>
            <Grid container spacing={4} sx={{ mb: 8 }}>
              {features.map((feature, index) => (
                <Grid item xs={12} md={6} lg={4} key={index}>
                  <motion.div variants={itemVariants} style={{ height: '100%' }}>
                    <FeatureCard>
                      <CardContent sx={{ p: 3 }}>
                        <IconBox>{feature.icon}</IconBox>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {feature.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {feature.description}
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <List disablePadding>
                          {feature.benefits.map((benefit, i) => (
                            <ListItem key={i} disablePadding sx={{ py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                <CheckCircleOutlineIcon color="primary" sx={{ fontSize: 20 }} />
                              </ListItemIcon>
                              <ListItemText 
                                primary={benefit} 
                                primaryTypographyProps={{ variant: 'body2' }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </FeatureCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <CTACard>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Ready to Get Started?
              </Typography>
              <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                Join our developer program today and start reaching millions of players worldwide.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Link href="/signup" passHref style={{ textDecoration: 'none' }}>
                  <Button
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: '#ffffff',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.9),
                      },
                    }}
                    endIcon={<ArrowForwardIcon />}
                  >
                    Sign Up Now
                  </Button>
                </Link>
                <Link href="/contact" passHref style={{ textDecoration: 'none' }}>
                  <Button
                    variant="outlined"
                    size="large"
                    sx={{
                      borderColor: '#ffffff',
                      color: '#ffffff',
                      '&:hover': {
                        borderColor: '#ffffff',
                        bgcolor: alpha('#ffffff', 0.1),
                      },
                    }}
                  >
                    Contact Sales
                  </Button>
                </Link>
              </Box>
            </CTACard>
          </motion.div>
        </Container>
      </DevelopersContainer>
    </Layout>
  );
};

export default Developers;
