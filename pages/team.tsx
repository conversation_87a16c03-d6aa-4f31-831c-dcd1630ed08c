import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  Avatar,
  useTheme,
  alpha,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const TeamMemberCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  textAlign: 'center',
  padding: theme.spacing(4, 2),
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const LargeAvatar = styled(Avatar)(({ theme }) => ({
  width: 120,
  height: 120,
  margin: '0 auto',
  marginBottom: theme.spacing(2),
  border: `4px solid ${alpha(theme.palette.primary.main, 0.2)}`,
}));

const MemberName = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(0.5),
}));

const MemberRole = styled(Typography)(({ theme }) => ({
  color: theme.palette.primary.main,
  fontWeight: 600,
  marginBottom: theme.spacing(2),
}));

// Sample team data
const teamMembers = [
  {
    name: 'Alex Johnson',
    role: 'CEO & Founder',
    bio: 'Blockchain enthusiast with 10+ years in gaming industry. Previously led development at major gaming studios.',
    avatar: '/team1.jpg',
  },
  {
    name: 'Sarah Chen',
    role: 'CTO',
    bio: 'Expert in blockchain technology and game development. Has built multiple successful gaming platforms.',
    avatar: '/team2.jpg',
  },
  {
    name: 'Michael Rodriguez',
    role: 'Creative Director',
    bio: 'Award-winning game designer with a passion for creating immersive gaming experiences.',
    avatar: '/team3.jpg',
  },
  {
    name: 'Emily Williams',
    role: 'Head of Marketing',
    bio: 'Marketing strategist with experience in both gaming and blockchain industries.',
    avatar: '/team4.jpg',
  },
  {
    name: 'David Kim',
    role: 'Lead Developer',
    bio: 'Full-stack developer with expertise in blockchain integration and game development.',
    avatar: '/team5.jpg',
  },
  {
    name: 'Jessica Taylor',
    role: 'Community Manager',
    bio: 'Building and nurturing our vibrant community of gamers and creators.',
    avatar: '/team6.jpg',
  },
  {
    name: 'Robert Chen',
    role: 'Blockchain Architect',
    bio: 'Designs and implements the blockchain infrastructure that powers our ecosystem.',
    avatar: '/team7.jpg',
  },
  {
    name: 'Olivia Martinez',
    role: 'UX/UI Designer',
    bio: 'Creates intuitive and engaging user experiences across our platform and games.',
    avatar: '/team8.jpg',
  },
];

const Team: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Our"
        highlightedTitle="Team"
        description="Meet the passionate individuals behind Gamestorme who are dedicated to revolutionizing the gaming industry."
      />

      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={4}>
              {teamMembers.map((member, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div variants={itemVariants}>
                    <TeamMemberCard>
                      <LargeAvatar src={member.avatar} alt={member.name} />
                      <MemberName variant="h5">{member.name}</MemberName>
                      <MemberRole variant="subtitle1">{member.role}</MemberRole>
                      <Divider sx={{ mb: 2, width: '50%', mx: 'auto' }} />
                      <Typography variant="body2" color="text.secondary">
                        {member.bio}
                      </Typography>
                    </TeamMemberCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Team;
