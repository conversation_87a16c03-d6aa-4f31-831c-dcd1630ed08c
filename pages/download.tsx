import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Download as DownloadIcon,
  Check as CheckIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Update as UpdateIcon,
  Devices as DevicesIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import Head from 'next/head';
import Layout from '../components/layout/Layout';

// Styled components
const PageTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 800,
  marginBottom: theme.spacing(2),
  background: `linear-gradient(90deg, ${theme.palette.text.primary} 0%, ${theme.palette.secondary.main} 100%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  textShadow: `0 0 40px ${alpha(theme.palette.primary.main, 0.3)}`,
  [theme.breakpoints.down('md')]: {
    fontSize: '2.5rem',
  },
}));

const FeatureCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const DownloadButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(1.5, 4),
  borderRadius: theme.shape.borderRadius * 1.5,
  fontWeight: 600,
  fontSize: '1.1rem',
  textTransform: 'none',
  boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
  '&:hover': {
    boxShadow: `0 10px 25px ${alpha(theme.palette.primary.main, 0.4)}`,
  },
}));

const SystemRequirements = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
}));

const Download: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <Head>
        <title>Download Gamestorme Launcher | Gamestorme</title>
        <meta name="description" content="Download the Gamestorme Launcher for the best gaming experience. Access your games, connect with friends, and enjoy seamless gameplay." />
      </Head>

      <Container maxWidth="lg" sx={{ py: 8 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div variants={itemVariants}>
            <PageTitle variant="h2" align="center">
              Download Gamestorme Launcher
            </PageTitle>
            <Typography 
              variant="h5" 
              align="center" 
              color="text.secondary"
              sx={{ mb: 6, maxWidth: 800, mx: 'auto' }}
            >
              Experience Gamestorme on your desktop with our dedicated launcher application
            </Typography>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 8 }}>
              <DownloadButton
                variant="contained"
                color="primary"
                size="large"
                startIcon={<DownloadIcon />}
                onClick={() => window.open('/download/gamestorme-launcher-setup.exe')}
              >
                Download for Windows
              </DownloadButton>
            </Box>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Typography 
              variant="h4" 
              fontWeight="bold" 
              sx={{ mb: 4 }}
            >
              Features
            </Typography>
          </motion.div>

          <Grid container spacing={4} sx={{ mb: 8 }}>
            {[
              {
                icon: <SpeedIcon fontSize="large" color="primary" />,
                title: 'Fast Performance',
                description: 'Optimized for speed with quick load times and smooth gameplay experience.',
              },
              {
                icon: <SecurityIcon fontSize="large" color="primary" />,
                title: 'Secure Gaming',
                description: 'Enhanced security features to keep your gaming experience safe and protected.',
              },
              {
                icon: <UpdateIcon fontSize="large" color="primary" />,
                title: 'Automatic Updates',
                description: 'Always stay up-to-date with automatic updates for both the launcher and your games.',
              },
              {
                icon: <DevicesIcon fontSize="large" color="primary" />,
                title: 'Cross-Platform',
                description: 'Seamlessly sync your progress across devices with our cloud save feature.',
              },
            ].map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <motion.div variants={itemVariants}>
                  <FeatureCard>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                      <Box sx={{ mb: 2 }}>
                        {feature.icon}
                      </Box>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                        {feature.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {feature.description}
                      </Typography>
                    </Box>
                  </FeatureCard>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          <motion.div variants={itemVariants}>
            <Typography 
              variant="h4" 
              fontWeight="bold" 
              sx={{ mb: 4 }}
            >
              System Requirements
            </Typography>
          </motion.div>

          <motion.div variants={itemVariants}>
            <SystemRequirements sx={{ mb: 8 }}>
              <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                    Minimum Requirements
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Operating System" 
                        secondary="Windows 10 (64-bit)" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Processor" 
                        secondary="Intel Core i3 or AMD equivalent" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Memory" 
                        secondary="4 GB RAM" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Storage" 
                        secondary="500 MB available space" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Internet" 
                        secondary="Broadband Internet connection" 
                      />
                    </ListItem>
                  </List>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                    Recommended Requirements
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Operating System" 
                        secondary="Windows 10/11 (64-bit)" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Processor" 
                        secondary="Intel Core i5 or AMD equivalent" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Memory" 
                        secondary="8 GB RAM" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Storage" 
                        secondary="1 GB available space (SSD recommended)" 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Internet" 
                        secondary="High-speed Internet connection" 
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </SystemRequirements>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Typography 
              variant="h4" 
              fontWeight="bold" 
              sx={{ mb: 4 }}
            >
              Coming Soon
            </Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>
              We're working on versions for macOS and Linux. Stay tuned for updates!
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>
              <Button variant="outlined" disabled>
                Download for macOS (Coming Soon)
              </Button>
              <Button variant="outlined" disabled>
                Download for Linux (Coming Soon)
              </Button>
            </Box>
          </motion.div>
        </motion.div>
      </Container>
    </Layout>
  );
};

export default Download;
