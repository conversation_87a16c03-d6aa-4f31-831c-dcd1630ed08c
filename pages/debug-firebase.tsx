import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper, Button, Alert } from '@mui/material';
import { auth, firestore } from '../lib/firebase';
import { signInWithEmailAndPassword } from 'firebase/auth';

const DebugFirebase: React.FC = () => {
  const [status, setStatus] = useState<any>({});
  const [testResult, setTestResult] = useState<string>('');

  useEffect(() => {
    // Check Firebase configuration
    const config = {
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Set' : 'Missing',
    };
    
    setStatus({
      config,
      authApp: auth.app.name,
      firestoreApp: firestore.app.name,
    });
  }, []);

  const testFirebaseAuth = async () => {
    try {
      setTestResult('Testing Firebase Auth...');
      
      // Try to sign in with a test email (this will fail but should show network connectivity)
      await signInWithEmailAndPassword(auth, '<EMAIL>', 'password');
      
    } catch (error: any) {
      console.log('Firebase Auth Test Error:', error);
      
      if (error.code === 'auth/network-request-failed') {
        setTestResult('❌ NETWORK ERROR: Cannot connect to Firebase servers. Check your internet connection and firewall settings.');
      } else if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-email') {
        setTestResult('✅ NETWORK OK: Firebase servers are reachable (authentication failed as expected with test credentials)');
      } else {
        setTestResult(`⚠️ OTHER ERROR: ${error.code} - ${error.message}`);
      }
    }
  };

  const testAPI = async () => {
    try {
      setTestResult('Testing API route...');
      const response = await fetch('/api/test-firebase');
      const data = await response.json();
      
      if (data.success) {
        setTestResult('✅ API TEST: Firebase connection successful via API route');
      } else {
        setTestResult(`❌ API TEST: ${data.error}`);
      }
    } catch (error: any) {
      setTestResult(`❌ API TEST ERROR: ${error.message}`);
    }
  };

  return (
    <Box sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🔥 Firebase Debug Page
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Firebase Configuration Status
        </Typography>
        <pre style={{ background: '#f5f5f5', padding: '16px', borderRadius: '4px' }}>
          {JSON.stringify(status, null, 2)}
        </pre>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Connection Tests
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button variant="contained" onClick={testFirebaseAuth}>
            Test Firebase Auth
          </Button>
          <Button variant="contained" onClick={testAPI}>
            Test API Route
          </Button>
        </Box>
        
        {testResult && (
          <Alert severity={testResult.includes('✅') ? 'success' : testResult.includes('⚠️') ? 'warning' : 'error'}>
            {testResult}
          </Alert>
        )}
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Troubleshooting Steps
        </Typography>
        <Typography variant="body2" component="div">
          <ol>
            <li><strong>Check Internet Connection:</strong> Make sure you can access other websites</li>
            <li><strong>Check Firewall:</strong> Ensure your firewall isn't blocking Firebase domains</li>
            <li><strong>Check Environment Variables:</strong> Verify all NEXT_PUBLIC_FIREBASE_* variables are set</li>
            <li><strong>Check Firebase Project:</strong> Ensure the project exists and is active</li>
            <li><strong>Check Security Middleware:</strong> Our security system might be blocking requests</li>
            <li><strong>Check Browser Console:</strong> Look for additional error messages</li>
          </ol>
        </Typography>
      </Paper>
    </Box>
  );
};

export default DebugFirebase;
