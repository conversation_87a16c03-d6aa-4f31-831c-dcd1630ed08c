import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  Chip,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Tab,
  Tabs,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Skeleton,
  CircularProgress,
  Badge,
  Tooltip,
  Switch,
  FormControlLabel,
  Drawer,
  AppBar,
  Toolbar,
  useMediaQuery,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/router';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart, Pie, Cell, AreaChart, Area } from 'recharts';
import Layout from '../../components/layout/Layout';

// Firebase imports
import { firestore, auth } from '../../lib/firebase';
import { collection, query, where, orderBy, onSnapshot, addDoc, updateDoc, doc, getDocs, limit } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import GamesIcon from '@mui/icons-material/Games';
import BarChartIcon from '@mui/icons-material/BarChart';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import PeopleIcon from '@mui/icons-material/People';
import HelpIcon from '@mui/icons-material/Help';
import SettingsIcon from '@mui/icons-material/Settings';
import NotificationsIcon from '@mui/icons-material/Notifications';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import AddIcon from '@mui/icons-material/Add';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import ChatIcon from '@mui/icons-material/Chat';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import LogoutIcon from '@mui/icons-material/Logout';
import SendIcon from '@mui/icons-material/Send';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import DownloadIcon from '@mui/icons-material/Download';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import StarIcon from '@mui/icons-material/Star';
import PublishIcon from '@mui/icons-material/Publish';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LaunchIcon from '@mui/icons-material/Launch';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import CampaignIcon from '@mui/icons-material/Campaign';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import PendingIcon from '@mui/icons-material/Pending';

// Types
import { Game, NewsArticle } from '../../types/database';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
}));

const Sidebar = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  },
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  marginLeft: 280,
  [theme.breakpoints.down('md')]: {
    marginLeft: 0,
  },
}));

const StatsCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const GameCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 10px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

// Interface for real-time analytics
interface DeveloperAnalytics {
  totalRevenue: number;
  totalDownloads: number;
  totalViews: number;
  totalLikes: number;
  averageRating: number;
  conversionRate: number;
  revenueGrowth: number;
  downloadGrowth: number;
  monthlyData: Array<{
    month: string;
    revenue: number;
    downloads: number;
    views: number;
  }>;
  topGames: Array<{
    id: string;
    title: string;
    revenue: number;
    downloads: number;
  }>;
}

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: Date;
  updatedAt: Date;
  developerId: string;
  adminResponse?: string;
}

interface CommunityMetrics {
  totalFollowers: number;
  engagementRate: number;
  communityGrowth: number;
  recentActivity: Array<{
    type: 'review' | 'download' | 'like' | 'share';
    gameTitle: string;
    timestamp: Date;
    user: string;
  }>;
}

const DeveloperDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [user, loading, error] = useAuthState(auth);

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [games, setGames] = useState<Game[]>([]);
  const [analytics, setAnalytics] = useState<DeveloperAnalytics | null>(null);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [communityMetrics, setCommunityMetrics] = useState<CommunityMetrics | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [notifications, setNotifications] = useState<any[]>([]);

  // Dialog states
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [supportDialogOpen, setSupportDialogOpen] = useState(false);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);

  // Real-time data fetching
  useEffect(() => {
    if (!user) return;

    const unsubscribes: (() => void)[] = [];

    // Fetch developer's games
    const gamesQuery = query(
      collection(firestore, 'games'),
      where('developer.uid', '==', user.uid),
      orderBy('createdAt', 'desc')
    );

    const unsubscribeGames = onSnapshot(gamesQuery, (snapshot) => {
      const gamesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        approvedAt: doc.data().approvedAt?.toDate(),
      })) as Game[];
      
      setGames(gamesData);
      calculateAnalytics(gamesData);
    });
    unsubscribes.push(unsubscribeGames);

    // Fetch support tickets
    const ticketsQuery = query(
      collection(firestore, 'supportTickets'),
      where('developerId', '==', user.uid),
      orderBy('createdAt', 'desc'),
      limit(10)
    );

    const unsubscribeTickets = onSnapshot(ticketsQuery, (snapshot) => {
      const ticketsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      })) as SupportTicket[];
      
      setSupportTickets(ticketsData);
    });
    unsubscribes.push(unsubscribeTickets);

    // Fetch notifications
    const notificationsQuery = query(
      collection(firestore, 'notifications'),
      where('userId', '==', user.uid),
      where('read', '==', false),
      orderBy('createdAt', 'desc'),
      limit(5)
    );

    const unsubscribeNotifications = onSnapshot(notificationsQuery, (snapshot) => {
      const notificationsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
      }));
      
      setNotifications(notificationsData);
    });
    unsubscribes.push(unsubscribeNotifications);

    setLoadingData(false);

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [user]);

  // Calculate analytics from games data
  const calculateAnalytics = (gamesData: Game[]) => {
    if (gamesData.length === 0) {
      setAnalytics({
        totalRevenue: 0,
        totalDownloads: 0,
        totalViews: 0,
        totalLikes: 0,
        averageRating: 0,
        conversionRate: 0,
        revenueGrowth: 0,
        downloadGrowth: 0,
        monthlyData: [],
        topGames: [],
      });
      return;
    }

    const totalRevenue = gamesData.reduce((sum, game) => {
      return sum + (game.stats.downloads * (game.pricing.isFree ? 0 : game.pricing.price));
    }, 0);

    const totalDownloads = gamesData.reduce((sum, game) => sum + game.stats.downloads, 0);
    const totalViews = gamesData.reduce((sum, game) => sum + game.stats.views, 0);
    const totalLikes = gamesData.reduce((sum, game) => sum + game.stats.likes, 0);
    const averageRating = gamesData.reduce((sum, game) => sum + game.details.rating, 0) / gamesData.length;
    const conversionRate = totalViews > 0 ? (totalDownloads / totalViews) * 100 : 0;

    // Generate mock monthly data for the last 6 months
    const monthlyData = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      const monthName = date.toLocaleDateString('en-US', { month: 'short' });
      
      return {
        month: monthName,
        revenue: Math.floor(totalRevenue * (0.1 + Math.random() * 0.2)),
        downloads: Math.floor(totalDownloads * (0.1 + Math.random() * 0.2)),
        views: Math.floor(totalViews * (0.1 + Math.random() * 0.2)),
      };
    });

    // Top performing games
    const topGames = gamesData
      .sort((a, b) => (b.stats.downloads * (b.pricing.isFree ? 0 : b.pricing.price)) - 
                      (a.stats.downloads * (a.pricing.isFree ? 0 : a.pricing.price)))
      .slice(0, 5)
      .map(game => ({
        id: game.id,
        title: game.title,
        revenue: game.stats.downloads * (game.pricing.isFree ? 0 : game.pricing.price),
        downloads: game.stats.downloads,
      }));

    setAnalytics({
      totalRevenue,
      totalDownloads,
      totalViews,
      totalLikes,
      averageRating,
      conversionRate,
      revenueGrowth: Math.random() * 50 + 10, // Mock growth data
      downloadGrowth: Math.random() * 30 + 5,
      monthlyData,
      topGames,
    });

    // Generate community metrics
    setCommunityMetrics({
      totalFollowers: Math.floor(totalDownloads * 0.3),
      engagementRate: Math.random() * 15 + 5,
      communityGrowth: Math.random() * 25 + 5,
      recentActivity: Array.from({ length: 10 }, (_, i) => ({
        type: ['review', 'download', 'like', 'share'][Math.floor(Math.random() * 4)] as any,
        gameTitle: gamesData[Math.floor(Math.random() * gamesData.length)]?.title || 'Unknown Game',
        timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        user: `User${Math.floor(Math.random() * 1000)}`,
      })),
    });
  };

  // Handle game upload
  const handleGameUpload = async (gameData: any) => {
    try {
      await addDoc(collection(firestore, 'games'), {
        ...gameData,
        developer: {
          name: user?.displayName || 'Developer',
          email: user?.email || '',
          uid: user?.uid || '',
        },
        status: 'pending',
        featured: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        stats: {
          downloads: 0,
          views: 0,
          likes: 0,
          reviews: 0,
        },
      });
      setUploadDialogOpen(false);
    } catch (error) {
      console.error('Error uploading game:', error);
    }
  };

  // Handle support ticket creation
  const handleSupportTicket = async (ticketData: any) => {
    try {
      await addDoc(collection(firestore, 'supportTickets'), {
        ...ticketData,
        developerId: user?.uid,
        status: 'open',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      setSupportDialogOpen(false);
    } catch (error) {
      console.error('Error creating support ticket:', error);
    }
  };

  if (loading || loadingData) {
    return (
      <Layout>
        <DashboardContainer>
          <Container maxWidth="lg" sx={{ py: 4 }}>
            <Grid container spacing={3}>
              {Array.from({ length: 8 }).map((_, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 2 }} />
                </Grid>
              ))}
            </Grid>
          </Container>
        </DashboardContainer>
      </Layout>
    );
  }

  if (!user) {
    router.push('/login');
    return null;
  }

  return (
    <Layout>
      <DashboardContainer>
        {/* Mobile Header */}
        {isMobile && (
          <AppBar position="static" elevation={0} sx={{ bgcolor: 'transparent' }}>
            <Toolbar>
              <IconButton onClick={() => setSidebarOpen(true)} sx={{ mr: 2 }}>
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Developer Dashboard
              </Typography>
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </Toolbar>
          </AppBar>
        )}

        {/* Sidebar */}
        <Sidebar
          variant={isMobile ? 'temporary' : 'permanent'}
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        >
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                {user.displayName?.charAt(0) || 'D'}
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="bold">
                  {user.displayName || 'Developer'}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Developer Account
                </Typography>
              </Box>
            </Box>

            <List>
              {[
                { label: 'Overview', icon: <DashboardIcon />, index: 0 },
                { label: 'My Games', icon: <GamesIcon />, index: 1 },
                { label: 'Analytics', icon: <BarChartIcon />, index: 2 },
                { label: 'Financials', icon: <AttachMoneyIcon />, index: 3 },
                { label: 'Community', icon: <PeopleIcon />, index: 4 },
                { label: 'Marketing AI', icon: <SmartToyIcon />, index: 5 },
                { label: 'Support', icon: <HelpIcon />, index: 6 },
                { label: 'Settings', icon: <SettingsIcon />, index: 7 },
              ].map((item) => (
                <ListItem
                  key={item.index}
                  button
                  selected={activeTab === item.index}
                  onClick={() => setActiveTab(item.index)}
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    '&.Mui-selected': {
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.15),
                      },
                    },
                  }}
                >
                  <ListItemIcon sx={{ color: activeTab === item.index ? 'primary.main' : 'inherit' }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.label} />
                  {item.index === 6 && supportTickets.filter(t => t.status === 'open').length > 0 && (
                    <Badge badgeContent={supportTickets.filter(t => t.status === 'open').length} color="error" />
                  )}
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            <Button
              fullWidth
              variant="contained"
              startIcon={<CloudUploadIcon />}
              onClick={() => setUploadDialogOpen(true)}
              sx={{ mb: 2 }}
            >
              Upload Game
            </Button>

            <Button
              fullWidth
              variant="outlined"
              startIcon={<LogoutIcon />}
              onClick={() => {
                auth.signOut();
                router.push('/');
              }}
            >
              Logout
            </Button>
          </Box>
        </Sidebar>

        {/* Main Content */}
        <MainContent>
          <Container maxWidth="xl">
            {/* Tab Content will be added in the next part */}
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {['Overview', 'My Games', 'Analytics', 'Financials', 'Community', 'Marketing AI', 'Support', 'Settings'][activeTab]}
            </Typography>
            
            {/* Tab Content */}
            <Box sx={{ mt: 3 }}>
              {/* Overview Tab */}
              {activeTab === 0 && analytics && (
                <Grid container spacing={3}>
                  {/* Overview Stats */}
                  <Grid item xs={12} sm={6} md={3}>
                    <StatsCard>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" fontWeight="bold" color="primary.main">
                              ${analytics.totalRevenue.toLocaleString()}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Total Revenue
                            </Typography>
                          </Box>
                          <AttachMoneyIcon sx={{ fontSize: 40, color: 'primary.main', opacity: 0.7 }} />
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                          <Typography variant="caption" color="success.main">
                            +{analytics.revenueGrowth.toFixed(1)}% this month
                          </Typography>
                        </Box>
                      </CardContent>
                    </StatsCard>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <StatsCard>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" fontWeight="bold" color="secondary.main">
                              {analytics.totalDownloads.toLocaleString()}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Total Downloads
                            </Typography>
                          </Box>
                          <DownloadIcon sx={{ fontSize: 40, color: 'secondary.main', opacity: 0.7 }} />
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                          <Typography variant="caption" color="success.main">
                            +{analytics.downloadGrowth.toFixed(1)}% this month
                          </Typography>
                        </Box>
                      </CardContent>
                    </StatsCard>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <StatsCard>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" fontWeight="bold" color="info.main">
                              {analytics.totalViews.toLocaleString()}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Total Views
                            </Typography>
                          </Box>
                          <VisibilityIcon sx={{ fontSize: 40, color: 'info.main', opacity: 0.7 }} />
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            {analytics.conversionRate.toFixed(1)}% conversion rate
                          </Typography>
                        </Box>
                      </CardContent>
                    </StatsCard>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <StatsCard>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" fontWeight="bold" color="warning.main">
                              {analytics.averageRating.toFixed(1)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Average Rating
                            </Typography>
                          </Box>
                          <StarIcon sx={{ fontSize: 40, color: 'warning.main', opacity: 0.7 }} />
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            {analytics.totalLikes.toLocaleString()} total likes
                          </Typography>
                        </Box>
                      </CardContent>
                    </StatsCard>
                  </Grid>

                  {/* Revenue Chart */}
                  <Grid item xs={12} md={8}>
                    <StatsCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Revenue Trends
                        </Typography>
                        <ResponsiveContainer width="100%" height={300}>
                          <AreaChart data={analytics.monthlyData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Area
                              type="monotone"
                              dataKey="revenue"
                              stroke={theme.palette.primary.main}
                              fill={alpha(theme.palette.primary.main, 0.3)}
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </StatsCard>
                  </Grid>

                  {/* Top Games */}
                  <Grid item xs={12} md={4}>
                    <StatsCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Top Performing Games
                        </Typography>
                        <List>
                          {analytics.topGames.map((game, index) => (
                            <ListItem key={game.id} sx={{ px: 0 }}>
                              <ListItemText
                                primary={game.title}
                                secondary={`$${game.revenue.toLocaleString()} • ${game.downloads.toLocaleString()} downloads`}
                              />
                              <Chip
                                label={`#${index + 1}`}
                                size="small"
                                color={index === 0 ? 'primary' : 'default'}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </StatsCard>
                  </Grid>

                  {/* Recent Activity */}
                  <Grid item xs={12}>
                    <StatsCard>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Recent Activity
                        </Typography>
                        <TableContainer>
                          <Table>
                            <TableHead>
                              <TableRow>
                                <TableCell>Game</TableCell>
                                <TableCell>Status</TableCell>
                                <TableCell>Downloads</TableCell>
                                <TableCell>Revenue</TableCell>
                                <TableCell>Rating</TableCell>
                                <TableCell>Actions</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {games.slice(0, 5).map((game) => (
                                <TableRow key={game.id}>
                                  <TableCell>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <Avatar
                                        src={game.images.thumbnail}
                                        sx={{ mr: 2, width: 40, height: 40 }}
                                      />
                                      <Box>
                                        <Typography variant="subtitle2" fontWeight="bold">
                                          {game.title}
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                          {game.details.genre.join(', ')}
                                        </Typography>
                                      </Box>
                                    </Box>
                                  </TableCell>
                                  <TableCell>
                                    <Chip
                                      label={game.status}
                                      color={
                                        game.status === 'approved' ? 'success' :
                                        game.status === 'pending' ? 'warning' : 'error'
                                      }
                                      size="small"
                                      icon={
                                        game.status === 'approved' ? <CheckCircleIcon /> :
                                        game.status === 'pending' ? <PendingIcon /> : <ErrorIcon />
                                      }
                                    />
                                  </TableCell>
                                  <TableCell>{game.stats.downloads.toLocaleString()}</TableCell>
                                  <TableCell>
                                    ${(game.stats.downloads * (game.pricing.isFree ? 0 : game.pricing.price)).toLocaleString()}
                                  </TableCell>
                                  <TableCell>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <StarIcon sx={{ fontSize: 16, color: 'warning.main', mr: 0.5 }} />
                                      {game.details.rating.toFixed(1)}
                                    </Box>
                                  </TableCell>
                                  <TableCell>
                                    <IconButton size="small" onClick={() => router.push(`/games/${game.id}`)}>
                                      <LaunchIcon />
                                    </IconButton>
                                    <IconButton size="small">
                                      <EditIcon />
                                    </IconButton>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </CardContent>
                    </StatsCard>
                  </Grid>
                </Grid>
              )}

              {/* My Games Tab */}
              {activeTab === 1 && (
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                      <Typography variant="h5" fontWeight="bold">
                        My Games ({games.length})
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => setUploadDialogOpen(true)}
                      >
                        Upload New Game
                      </Button>
                    </Box>
                  </Grid>

                  {games.length === 0 ? (
                    <Grid item xs={12}>
                      <StatsCard>
                        <CardContent sx={{ textAlign: 'center', py: 8 }}>
                          <GamesIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                          <Typography variant="h6" color="text.secondary" gutterBottom>
                            No games uploaded yet
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                            Start by uploading your first game to the platform
                          </Typography>
                          <Button
                            variant="contained"
                            startIcon={<CloudUploadIcon />}
                            onClick={() => setUploadDialogOpen(true)}
                          >
                            Upload Your First Game
                          </Button>
                        </CardContent>
                      </StatsCard>
                    </Grid>
                  ) : (
                    games.map((game) => (
                      <Grid item xs={12} sm={6} md={4} key={game.id}>
                        <GameCard>
                          <CardContent>
                            <Box sx={{ position: 'relative', mb: 2 }}>
                              <Avatar
                                src={game.images.thumbnail}
                                sx={{ width: '100%', height: 120, borderRadius: 2 }}
                                variant="rounded"
                              />
                              <Chip
                                label={game.status}
                                color={
                                  game.status === 'approved' ? 'success' :
                                  game.status === 'pending' ? 'warning' : 'error'
                                }
                                size="small"
                                sx={{ position: 'absolute', top: 8, right: 8 }}
                              />
                            </Box>

                            <Typography variant="h6" fontWeight="bold" gutterBottom>
                              {game.title}
                            </Typography>

                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {game.description}
                            </Typography>

                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography variant="h6" color="primary.main">
                                  {game.stats.downloads.toLocaleString()}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Downloads
                                </Typography>
                              </Box>
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography variant="h6" color="secondary.main">
                                  ${(game.stats.downloads * (game.pricing.isFree ? 0 : game.pricing.price)).toLocaleString()}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Revenue
                                </Typography>
                              </Box>
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography variant="h6" color="warning.main">
                                  {game.details.rating.toFixed(1)}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Rating
                                </Typography>
                              </Box>
                            </Box>

                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                size="small"
                                variant="outlined"
                                startIcon={<LaunchIcon />}
                                onClick={() => router.push(`/games/${game.id}`)}
                                fullWidth
                              >
                                View
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                startIcon={<EditIcon />}
                                fullWidth
                              >
                                Edit
                              </Button>
                            </Box>
                          </CardContent>
                        </GameCard>
                      </Grid>
                    ))
                  )}
                </Grid>
              )}
            </Box>
          </Container>
        </MainContent>

        {/* Upload Game Dialog */}
        <Dialog
          open={uploadDialogOpen}
          onClose={() => setUploadDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Upload New Game</DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Upload your game to the Gamestorme platform. All games go through our approval process.
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Game Title"
                  variant="outlined"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  variant="outlined"
                  multiline
                  rows={3}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Genre</InputLabel>
                  <Select label="Genre">
                    <MenuItem value="action">Action</MenuItem>
                    <MenuItem value="adventure">Adventure</MenuItem>
                    <MenuItem value="puzzle">Puzzle</MenuItem>
                    <MenuItem value="strategy">Strategy</MenuItem>
                    <MenuItem value="simulation">Simulation</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Price ($)"
                  variant="outlined"
                  type="number"
                  InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  startIcon={<CloudUploadIcon />}
                  sx={{ height: 100, borderStyle: 'dashed' }}
                >
                  Upload Game Files
                  <input type="file" hidden multiple />
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => handleGameUpload({})}>
              Upload Game
            </Button>
          </DialogActions>
        </Dialog>

        {/* Support Dialog */}
        <Dialog
          open={supportDialogOpen}
          onClose={() => setSupportDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Create Support Ticket</DialogTitle>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Subject"
                  variant="outlined"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Priority</InputLabel>
                  <Select label="Priority">
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="urgent">Urgent</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  variant="outlined"
                  multiline
                  rows={4}
                  required
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSupportDialogOpen(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => handleSupportTicket({})}>
              Create Ticket
            </Button>
          </DialogActions>
        </Dialog>
      </DashboardContainer>
    </Layout>
  );
};

export default DeveloperDashboard;
