import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  Divider,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  IconButton,
  useTheme,
  alpha,
  Badge,
  Chip,
  TextField,
  Drawer,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  AppBar,
  Toolbar,
  useMediaQuery,
  CircularProgress,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Select,
  FormControl,
  InputLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import { <PERSON><PERSON>hart, <PERSON>, <PERSON>A<PERSON>s, YA<PERSON>s, CartesianGrid, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';
import EnhancedDashboard from '../../components/shared/EnhancedDashboard';
import EnhancedStatsCard from '../../components/shared/EnhancedStatsCard';
import GlassCard from '../../components/shared/GlassCard';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import GamesIcon from '@mui/icons-material/Games';
import BarChartIcon from '@mui/icons-material/BarChart';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import PeopleIcon from '@mui/icons-material/People';
import HelpIcon from '@mui/icons-material/Help';
import SettingsIcon from '@mui/icons-material/Settings';
import NotificationsIcon from '@mui/icons-material/Notifications';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import AddIcon from '@mui/icons-material/Add';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import ChatIcon from '@mui/icons-material/Chat';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import LogoutIcon from '@mui/icons-material/Logout';
import SendIcon from '@mui/icons-material/Send';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import DownloadIcon from '@mui/icons-material/Download';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import StarIcon from '@mui/icons-material/Star';
import PublishIcon from '@mui/icons-material/Publish';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LaunchIcon from '@mui/icons-material/Launch';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import CampaignIcon from '@mui/icons-material/Campaign';
import DescriptionIcon from '@mui/icons-material/Description';
import ForumIcon from '@mui/icons-material/Forum';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SecurityIcon from '@mui/icons-material/Security';
import PaymentIcon from '@mui/icons-material/Payment';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import UploadIcon from '@mui/icons-material/Upload';
import CodeIcon from '@mui/icons-material/Code';
import ArticleIcon from '@mui/icons-material/Article';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';

// Styled components
const DashboardContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  backgroundColor: theme.palette.mode === 'dark' ? '#0a0a0f' : '#f5f7fa',
}));

const Sidebar = styled(Box)(({ theme }) => ({
  width: 280,
  backgroundColor: alpha(theme.palette.background.paper, 0.95),
  backdropFilter: 'blur(20px)',
  borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  position: 'fixed',
  height: '100vh',
  overflowY: 'auto',
  zIndex: 1200,
  [theme.breakpoints.down('md')]: {
    transform: 'translateX(-100%)',
    transition: 'transform 0.3s ease',
    '&.open': {
      transform: 'translateX(0)',
    },
  },
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  marginLeft: 280,
  [theme.breakpoints.down('md')]: {
    marginLeft: 0,
    paddingTop: theme.spacing(10),
  },
}));

const SidebarItem = styled(ListItemButton, {
  shouldForwardProp: (prop) => prop !== 'isActive'
})<{ isActive?: boolean }>(({ theme, isActive }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  marginBottom: theme.spacing(1),
  backgroundColor: isActive ? alpha(theme.palette.primary.main, 0.15) : 'transparent',
  color: isActive ? theme.palette.primary.main : theme.palette.text.primary,
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    transform: 'translateX(5px)',
  },
  '& .MuiListItemIcon-root': {
    color: isActive ? theme.palette.primary.main : theme.palette.text.secondary,
  },
}));

const ChatDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 400,
    backgroundColor: alpha(theme.palette.background.paper, 0.95),
    backdropFilter: 'blur(20px)',
    borderLeft: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  },
}));

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'transparent',
    transition: 'all 0.3s ease',
  },
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: `0 12px 28px ${alpha(theme.palette.common.black, 0.12)}`,
  },
}));

const GameCard = styled(Card)(({ theme }) => ({
  display: 'flex',
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
  overflow: 'hidden',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: `0 10px 20px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 56,
  height: 56,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  position: 'relative',
  boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.08)}`,
  border: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  '& svg': {
    fontSize: 24,
    color: theme.palette.primary.main,
  },
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: `0 8px 20px ${alpha(theme.palette.common.black, 0.12)}`,
    border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const StatValue = styled(Typography)(({ theme }) => ({
  fontSize: '2.2rem',
  fontWeight: 700,
  marginBottom: theme.spacing(0.5),
  fontFamily: '"Poppins", sans-serif',
  letterSpacing: '-0.5px',
  lineHeight: 1.2,
  background: `linear-gradient(135deg, ${theme.palette.text.primary} 30%, ${alpha(theme.palette.text.primary, 0.7)} 100%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  transition: 'all 0.3s ease',
}));

const StatLabel = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
  fontWeight: 500,
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
}));

const UploadButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(1.5, 4),
  borderRadius: theme.shape.borderRadius * 3,
  fontWeight: 700,
  textTransform: 'none',
  fontSize: '1rem',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: `0 8px 20px ${alpha(theme.palette.secondary.main, 0.4)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'rgba(255, 255, 255, 0.1)',
    transform: 'translateX(-100%) rotate(45deg)',
    transition: 'transform 0.6s',
  },
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: `0 12px 28px ${alpha(theme.palette.secondary.main, 0.6)}`,
    '&::before': {
      transform: 'translateX(100%) rotate(45deg)',
    },
  },
  '&:active': {
    transform: 'translateY(1px)',
    boxShadow: `0 5px 15px ${alpha(theme.palette.secondary.main, 0.4)}`,
  },
}));

// Sample data
const recentGames = [
  {
    id: 1,
    title: 'Cosmic Odyssey',
    image: '/assets/images/games/cosmic-odyssey.jpg',
    sales: 1245,
    revenue: '$12,450',
    status: 'Live',
    trend: 'up',
    rating: 4.8,
    downloads: 15420,
    category: 'Adventure',
    platform: 'PC, Mobile',
    releaseDate: '2024-01-15',
    description: 'An epic space adventure with stunning visuals and immersive gameplay.',
  },
  {
    id: 2,
    title: 'Neon Racer',
    image: '/assets/images/games/neon-racer.jpg',
    sales: 876,
    revenue: '$8,760',
    status: 'Live',
    trend: 'down',
    rating: 4.2,
    downloads: 8760,
    category: 'Racing',
    platform: 'PC, Console',
    releaseDate: '2023-11-20',
    description: 'High-speed racing in a cyberpunk world with neon-lit tracks.',
  },
  {
    id: 3,
    title: 'Mystic Realms',
    image: '/assets/images/games/mystic-realms.jpg',
    sales: 0,
    revenue: '$0',
    status: 'In Review',
    trend: 'neutral',
    rating: 0,
    downloads: 0,
    category: 'RPG',
    platform: 'PC, Mobile, Console',
    releaseDate: '2024-03-01',
    description: 'A fantasy RPG with magical creatures and epic quests.',
  },
];

// Analytics data
const analyticsData = [
  { month: 'Jan', players: 1200, revenue: 15000, downloads: 2400 },
  { month: 'Feb', players: 1800, revenue: 22000, downloads: 3200 },
  { month: 'Mar', players: 2400, revenue: 28000, downloads: 4100 },
  { month: 'Apr', players: 3200, revenue: 35000, downloads: 5200 },
  { month: 'May', players: 4100, revenue: 42000, downloads: 6800 },
  { month: 'Jun', players: 5200, revenue: 48000, downloads: 7500 },
];

// Financial data
const financialData = [
  { month: 'Jan', revenue: 15000, expenses: 8000, profit: 7000 },
  { month: 'Feb', revenue: 22000, expenses: 9500, profit: 12500 },
  { month: 'Mar', revenue: 28000, expenses: 11000, profit: 17000 },
  { month: 'Apr', revenue: 35000, expenses: 12500, profit: 22500 },
  { month: 'May', revenue: 42000, expenses: 14000, profit: 28000 },
  { month: 'Jun', revenue: 48000, expenses: 15500, profit: 32500 },
];

// Platform distribution data
const platformData = [
  { name: 'PC', value: 45, color: '#8884d8' },
  { name: 'Mobile', value: 35, color: '#82ca9d' },
  { name: 'Console', value: 20, color: '#ffc658' },
];

// Community data
const communityPosts = [
  {
    id: 1,
    title: 'New Update for Cosmic Odyssey',
    content: 'We\'ve added new planets to explore and fixed several bugs.',
    author: 'John Developer',
    date: '2024-01-20',
    likes: 45,
    comments: 12,
  },
  {
    id: 2,
    title: 'Behind the Scenes: Neon Racer Development',
    content: 'Take a look at how we created the stunning neon effects.',
    author: 'John Developer',
    date: '2024-01-18',
    likes: 32,
    comments: 8,
  },
];

// Marketing AI insights
const marketingInsights = [
  {
    title: 'Optimal Release Time',
    description: 'Based on your audience data, Tuesday 2PM EST shows highest engagement.',
    impact: 'High',
    category: 'Timing',
  },
  {
    title: 'Social Media Strategy',
    description: 'Focus on TikTok and Instagram for your target demographic (18-25).',
    impact: 'Medium',
    category: 'Social Media',
  },
  {
    title: 'Pricing Optimization',
    description: 'Consider a 15% price reduction to maximize revenue based on demand elasticity.',
    impact: 'High',
    category: 'Pricing',
  },
];

// Define the tabs for the dashboard
enum DashboardTab {
  Overview = 'overview',
  MyGames = 'my-games',
  Analytics = 'analytics',
  Financials = 'financials',
  Community = 'community',
  MarketingAI = 'marketing-ai',
  Documentation = 'documentation',
  Support = 'support',
  Settings = 'settings'
}

const DeveloperDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { currentUser, userProfile, logout, loading: authLoading } = useAuth();

  const [activeTab, setActiveTab] = useState<DashboardTab>(DashboardTab.Overview);
  const [loading, setLoading] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [chatType, setChatType] = useState<'support' | 'ai'>('ai');
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [chatMessages, setChatMessages] = useState<{ sender: 'user' | 'ai'; text: string; time: string }[]>([
    { sender: 'ai', text: 'Hi there! I\'m Stormy, your AI marketing assistant. How can I help you today?', time: 'now' }
  ]);
  const [messageInput, setMessageInput] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);

  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Authentication check
  useEffect(() => {
    if (!authLoading) {
      if (!currentUser) {
        router.push('/login');
        return;
      }

      if (userProfile && userProfile.userType !== 'developer') {
        // Redirect to appropriate dashboard based on user type
        if (userProfile.userType === 'gamer') {
          router.push('/gamer/dashboard');
        } else {
          router.push('/login');
        }
      }
    }
  }, [currentUser, userProfile, authLoading, router]);

  // Handle user menu
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      setLoading(true);
      setLogoutDialogOpen(false);
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tab: DashboardTab) => {
    setActiveTab(tab);
  };

  // Handle chat
  const handleChatToggle = () => {
    setChatOpen(!chatOpen);
  };

  const handleSendMessage = () => {
    if (!messageInput.trim()) return;

    // Add user message
    setChatMessages([...chatMessages, { sender: 'user', text: messageInput, time: 'now' }]);

    // Simulate AI response
    setTimeout(() => {
      let response = '';

      if (messageInput.toLowerCase().includes('marketing')) {
        response = 'Based on your game\'s performance, I recommend focusing on social media marketing and influencer partnerships to increase visibility.';
      } else if (messageInput.toLowerCase().includes('sales')) {
        response = 'Your sales have increased by 15% this month. Consider running a limited-time discount to boost them further.';
      } else if (messageInput.toLowerCase().includes('user')) {
        response = 'Your user engagement is strong, but retention could be improved. Consider adding more achievements or daily rewards.';
      } else {
        response = 'I\'m here to help with marketing strategies, sales analysis, and user engagement. What specific aspect would you like insights on?';
      }

      setChatMessages(prev => [...prev, { sender: 'ai', text: response, time: 'now' }]);
    }, 1000);

    setMessageInput('');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Don't render if not authenticated
  if (!currentUser || !userProfile) {
    return null;
  }

  // Sidebar items configuration
  const sidebarItems = [
    { id: 'overview', label: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'my-games', label: 'My Games', icon: <GamesIcon />, badge: recentGames.length },
    { id: 'analytics', label: 'Analytics', icon: <BarChartIcon /> },
    { id: 'financials', label: 'Financials', icon: <AttachMoneyIcon /> },
    { id: 'community', label: 'Community', icon: <PeopleIcon />, badge: '12' },
    { id: 'marketing-ai', label: 'Marketing AI', icon: <SmartToyIcon />, color: theme.palette.secondary.main },
    { id: 'documentation', label: 'Documentation', icon: <ArticleIcon /> },
    { id: 'support', label: 'Support', icon: <HelpIcon />, badge: '3', color: theme.palette.error.main },
    { id: 'settings', label: 'Settings', icon: <SettingsIcon /> },
  ];

  const headerActions = (
    <>
      <Button
        variant="contained"
        color="secondary"
        startIcon={<CloudUploadIcon />}
        sx={{ mr: 2 }}
      >
        Upload Game
      </Button>
      <IconButton onClick={() => setChatOpen(true)}>
        <Badge badgeContent={2} color="primary">
          <ChatIcon />
        </Badge>
      </IconButton>
    </>
  );

  return (
    <EnhancedDashboard
      title="Developer Dashboard"
      subtitle="Welcome back, John! Ready to create amazing games?"
      userInitial="JD"
      notificationCount={3}
      sidebarItems={sidebarItems}
      activeSection={activeTab}
      onSectionChange={(section) => setActiveTab(section as DashboardTab)}
      headerActions={headerActions}
    >
      {/* Overview Tab Content */}
      {activeTab === DashboardTab.Overview && (
        <Box>
          {/* Welcome Hero Section */}
          <GlassCard sx={{ mb: 4, p: 4 }} hoverEffect="glow">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  Ready to publish your next game?
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3, fontSize: '1.1rem' }}>
                  Upload your game to the Gamestorme platform and reach millions of players worldwide.
                  Our platform provides powerful tools for distribution, monetization, and community building.
                </Typography>
                <Button
                  variant="contained"
                  color="secondary"
                  size="large"
                  startIcon={<CloudUploadIcon />}
                  sx={{ mr: 2 }}
                >
                  Upload New Game
                </Button>
                <Button
                  variant="outlined"
                  color="primary"
                  size="large"
                  startIcon={<SmartToyIcon />}
                  onClick={() => setActiveTab(DashboardTab.MarketingAI)}
                >
                  Try AI Marketing
                </Button>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <GamesIcon sx={{ fontSize: 120, color: theme.palette.primary.main, opacity: 0.8 }} />
                </Box>
              </Grid>
            </Grid>
          </GlassCard>

          {/* Key Metrics */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Total Players"
                value="5.2K"
                change="+12%"
                changeType="positive"
                icon={<PeopleIcon />}
                color={theme.palette.primary.main}
                animationDelay={0}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Revenue"
                value="$48K"
                change="+18%"
                changeType="positive"
                icon={<AttachMoneyIcon />}
                color={theme.palette.success.main}
                animationDelay={0.1}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Downloads"
                value="7.5K"
                change="+25%"
                changeType="positive"
                icon={<DownloadIcon />}
                color={theme.palette.info.main}
                animationDelay={0.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedStatsCard
                title="Avg Rating"
                value="4.6"
                change="+0.2"
                changeType="positive"
                icon={<StarIcon />}
                color={theme.palette.warning.main}
                subtitle="out of 5.0"
                animationDelay={0.3}
              />
            </Grid>
          </Grid>
            {/* Recent Games Section */}
            <Box sx={{ mb: 6 }}>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3
              }}>
                <Typography
                  variant="h5"
                  fontWeight="bold"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <BarChartIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                  Performance Overview
                </Typography>
                <Box>
                  <Button
                    size="small"
                    variant="outlined"
                    sx={{
                      mr: 1,
                      borderRadius: theme.shape.borderRadius * 1.5,
                      textTransform: 'none',
                    }}
                  >
                    This Week
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    sx={{
                      borderRadius: theme.shape.borderRadius * 1.5,
                      textTransform: 'none',
                    }}
                  >
                    This Month
                  </Button>
                </Box>
              </Box>
              <Grid container spacing={3}>
                {[
                  {
                    icon: <GamesIcon />,
                    value: '3',
                    label: 'Total Games',
                    color: theme.palette.primary.main,
                    trend: '+1 this month',
                    trendUp: true
                  },
                  {
                    icon: <PeopleIcon />,
                    value: '2.4K',
                    label: 'Total Players',
                    color: theme.palette.info.main,
                    trend: '+346 this month',
                    trendUp: true
                  },
                  {
                    icon: <AttachMoneyIcon />,
                    value: '$21.2K',
                    label: 'Total Revenue',
                    color: theme.palette.success.main,
                    trend: '+$2.4K this month',
                    trendUp: true
                  },
                  {
                    icon: <TrendingUpIcon />,
                    value: '+12%',
                    label: 'Monthly Growth',
                    color: theme.palette.secondary.main,
                    trend: '+3% from last month',
                    trendUp: true
                  }
                ].map((stat, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <motion.div
                      variants={itemVariants}
                      whileHover={{
                        scale: 1.03,
                        transition: { duration: 0.2 }
                      }}
                    >
                      <StatsCard sx={{
                        borderTop: `4px solid ${stat.color}`,
                        transition: 'all 0.3s ease',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                      }}>
                        <CardContent sx={{
                          p: 3,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          textAlign: 'center',
                          height: '100%',
                          justifyContent: 'space-between'
                        }}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
                            <IconBox
                              sx={{
                                bgcolor: alpha(stat.color, 0.1),
                                color: stat.color,
                                border: `2px solid ${alpha(stat.color, 0.2)}`,
                                '& svg': {
                                  color: stat.color,
                                }
                              }}
                            >
                              {stat.icon}
                            </IconBox>
                            <StatValue sx={{ color: stat.color, mt: 1, mb: 1 }}>
                              {stat.value}
                            </StatValue>
                            <StatLabel sx={{ mb: 2 }}>
                              {stat.label}
                            </StatLabel>
                          </Box>
                          <Chip
                            size="small"
                            icon={stat.trendUp ? <TrendingUpIcon fontSize="small" /> : <TrendingDownIcon fontSize="small" />}
                            label={stat.trend}
                            sx={{
                              bgcolor: alpha(stat.trendUp ? theme.palette.success.main : theme.palette.error.main, 0.1),
                              color: stat.trendUp ? theme.palette.success.main : theme.palette.error.main,
                              fontWeight: 600,
                              fontSize: '0.75rem',
                              height: '28px',
                              borderRadius: '14px',
                              '& .MuiChip-icon': {
                                fontSize: '0.9rem',
                                marginLeft: '4px',
                              },
                              '& .MuiChip-label': {
                                padding: '0 8px',
                              },
                            }}
                          />
                        </CardContent>
                      </StatsCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Recent Games */}
            <Box sx={{ mb: 6 }}>
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                Your Games
              </Typography>
              <Grid container spacing={3}>
                {recentGames.map((game) => (
                  <Grid item xs={12} key={game.id}>
                    <motion.div variants={itemVariants}>
                      <GameCard>
                        <Box sx={{ width: 120, position: 'relative' }}>
                          <img
                            src={game.image}
                            alt={game.title}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              position: 'absolute',
                              top: 0,
                              left: 0
                            }}
                          />
                        </Box>
                        <Box sx={{ display: 'flex', flexGrow: 1, p: 2 }}>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" fontWeight="bold">
                              {game.title}
                            </Typography>
                            <Chip
                              label={game.status}
                              size="small"
                              color={game.status === 'Live' ? 'success' : 'warning'}
                              sx={{ mt: 1, mb: 2 }}
                            />
                            <Grid container spacing={2}>
                              <Grid item xs={6}>
                                <Typography variant="body2" color="text.secondary">
                                  Sales (30 days)
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Typography variant="subtitle1" fontWeight="bold">
                                    {game.sales}
                                  </Typography>
                                  {game.trend === 'up' && (
                                    <TrendingUpIcon color="success" fontSize="small" sx={{ ml: 1 }} />
                                  )}
                                  {game.trend === 'down' && (
                                    <TrendingDownIcon color="error" fontSize="small" sx={{ ml: 1 }} />
                                  )}
                                </Box>
                              </Grid>
                              <Grid item xs={6}>
                                <Typography variant="body2" color="text.secondary">
                                  Revenue (30 days)
                                </Typography>
                                <Typography variant="subtitle1" fontWeight="bold">
                                  {game.revenue}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Box>
                          <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                            <Button variant="outlined" color="primary" sx={{ mb: 1 }}>
                              View Details
                            </Button>
                            <Button variant="text" color="primary">
                              Edit Game
                            </Button>
                          </Box>
                        </Box>
                      </GameCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Box>
            </Box>
          )}

          {/* My Games Tab */}
          {activeTab === DashboardTab.MyGames && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h4" fontWeight="bold">
                  My Games
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  sx={{ borderRadius: 2, textTransform: 'none', px: 3 }}
                >
                  Upload New Game
                </Button>
              </Box>

              <Grid container spacing={3}>
                {recentGames.map((game) => (
                  <Grid item xs={12} md={6} lg={4} key={game.id}>
                    <Card sx={{
                      borderRadius: 3,
                      overflow: 'hidden',
                      boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.1)}`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.15)}`,
                      },
                    }}>
                      <Box sx={{ position: 'relative', height: 200 }}>
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <GamesIcon sx={{ fontSize: 60, color: 'white', opacity: 0.8 }} />
                        </Box>
                        <Chip
                          label={game.status}
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            bgcolor: game.status === 'Live' ? theme.palette.success.main : theme.palette.warning.main,
                            color: 'white',
                          }}
                        />
                      </Box>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {game.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {game.description}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Sales: {game.sales.toLocaleString()}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Revenue: {game.revenue}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <StarIcon sx={{ color: theme.palette.warning.main, fontSize: 16, mr: 0.5 }} />
                            <Typography variant="body2">{game.rating || 'N/A'}</Typography>
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button variant="outlined" size="small" startIcon={<EditIcon />}>
                            Edit
                          </Button>
                          <Button variant="outlined" size="small" startIcon={<VisibilityIcon />}>
                            View
                          </Button>
                          <Button variant="outlined" size="small" startIcon={<BarChartIcon />}>
                            Analytics
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Analytics Tab */}
          {activeTab === DashboardTab.Analytics && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Analytics Dashboard
              </Typography>

              {/* Key Metrics */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Total Players', value: '5.2K', change: '+12%', icon: <PeopleIcon />, color: theme.palette.primary.main },
                  { title: 'Revenue', value: '$48K', change: '+18%', icon: <AttachMoneyIcon />, color: theme.palette.success.main },
                  { title: 'Downloads', value: '7.5K', change: '+25%', icon: <DownloadIcon />, color: theme.palette.info.main },
                  { title: 'Avg Rating', value: '4.6', change: '+0.2', icon: <StarIcon />, color: theme.palette.warning.main },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center' }}>
                      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                        <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56 }}>
                          {metric.icon}
                        </Avatar>
                      </Box>
                      <Typography variant="h4" fontWeight="bold" color={metric.color}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.change}
                        size="small"
                        icon={<TrendingUpIcon />}
                        sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Charts */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Revenue & Player Growth
                    </Typography>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={analyticsData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Area type="monotone" dataKey="revenue" stackId="1" stroke={theme.palette.primary.main} fill={alpha(theme.palette.primary.main, 0.3)} />
                          <Area type="monotone" dataKey="players" stackId="2" stroke={theme.palette.secondary.main} fill={alpha(theme.palette.secondary.main, 0.3)} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </Box>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Platform Distribution
                    </Typography>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={platformData}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          >
                            {platformData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  </Card>
                </Grid>
              </Grid>

              {/* Detailed Analytics Table */}
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Game Performance Details
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Game</TableCell>
                          <TableCell align="right">Players</TableCell>
                          <TableCell align="right">Revenue</TableCell>
                          <TableCell align="right">Rating</TableCell>
                          <TableCell align="right">Downloads</TableCell>
                          <TableCell align="right">Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {recentGames.map((game) => (
                          <TableRow key={game.id}>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Avatar sx={{ mr: 2, bgcolor: theme.palette.primary.main }}>
                                  <GamesIcon />
                                </Avatar>
                                <Box>
                                  <Typography variant="subtitle2" fontWeight="bold">
                                    {game.title}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {game.category}
                                  </Typography>
                                </Box>
                              </Box>
                            </TableCell>
                            <TableCell align="right">{game.sales.toLocaleString()}</TableCell>
                            <TableCell align="right">{game.revenue}</TableCell>
                            <TableCell align="right">
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                <StarIcon sx={{ color: theme.palette.warning.main, fontSize: 16, mr: 0.5 }} />
                                {game.rating || 'N/A'}
                              </Box>
                            </TableCell>
                            <TableCell align="right">{game.downloads?.toLocaleString() || 'N/A'}</TableCell>
                            <TableCell align="right">
                              <Chip
                                label={game.status}
                                size="small"
                                color={game.status === 'Live' ? 'success' : 'warning'}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Financials Tab */}
          {activeTab === DashboardTab.Financials && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Financial Dashboard
              </Typography>

              {/* Financial Overview Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Total Revenue', value: '$48,000', change: '+18%', icon: <AttachMoneyIcon />, color: theme.palette.success.main },
                  { title: 'Total Expenses', value: '$15,500', change: '+5%', icon: <PaymentIcon />, color: theme.palette.error.main },
                  { title: 'Net Profit', value: '$32,500', change: '+25%', icon: <MonetizationOnIcon />, color: theme.palette.primary.main },
                  { title: 'Profit Margin', value: '67.7%', change: '+3%', icon: <TrendingUpIcon />, color: theme.palette.info.main },
                ].map((metric, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', border: `2px solid ${alpha(metric.color, 0.1)}` }}>
                      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                        <Avatar sx={{ bgcolor: alpha(metric.color, 0.1), color: metric.color, width: 56, height: 56 }}>
                          {metric.icon}
                        </Avatar>
                      </Box>
                      <Typography variant="h4" fontWeight="bold" color={metric.color}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Chip
                        label={metric.change}
                        size="small"
                        icon={<TrendingUpIcon />}
                        sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Financial Charts */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Revenue vs Expenses
                    </Typography>
                    <Box sx={{ height: 350 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={financialData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Bar dataKey="revenue" fill={theme.palette.success.main} name="Revenue" />
                          <Bar dataKey="expenses" fill={theme.palette.error.main} name="Expenses" />
                          <Bar dataKey="profit" fill={theme.palette.primary.main} name="Profit" />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Payment Methods
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      {[
                        { method: 'Credit Card', percentage: 65, amount: '$31,200' },
                        { method: 'PayPal', percentage: 25, amount: '$12,000' },
                        { method: 'Crypto', percentage: 10, amount: '$4,800' },
                      ].map((payment, index) => (
                        <Box key={index} sx={{ mb: 3 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {payment.method}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {payment.amount}
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={payment.percentage}
                            sx={{
                              height: 8,
                              borderRadius: 4,
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              '& .MuiLinearProgress-bar': {
                                borderRadius: 4,
                                bgcolor: theme.palette.primary.main,
                              },
                            }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {payment.percentage}%
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Card>
                </Grid>
              </Grid>

              {/* Financial Breakdown Table */}
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Monthly Financial Breakdown
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Month</TableCell>
                          <TableCell align="right">Revenue</TableCell>
                          <TableCell align="right">Expenses</TableCell>
                          <TableCell align="right">Profit</TableCell>
                          <TableCell align="right">Margin</TableCell>
                          <TableCell align="right">Growth</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {financialData.map((row, index) => {
                          const margin = ((row.profit / row.revenue) * 100).toFixed(1);
                          const growth = index > 0 ? (((row.profit - financialData[index - 1].profit) / financialData[index - 1].profit) * 100).toFixed(1) : '0';
                          return (
                            <TableRow key={row.month}>
                              <TableCell>{row.month}</TableCell>
                              <TableCell align="right" sx={{ color: theme.palette.success.main, fontWeight: 'bold' }}>
                                ${row.revenue.toLocaleString()}
                              </TableCell>
                              <TableCell align="right" sx={{ color: theme.palette.error.main }}>
                                ${row.expenses.toLocaleString()}
                              </TableCell>
                              <TableCell align="right" sx={{ color: theme.palette.primary.main, fontWeight: 'bold' }}>
                                ${row.profit.toLocaleString()}
                              </TableCell>
                              <TableCell align="right">{margin}%</TableCell>
                              <TableCell align="right">
                                <Chip
                                  label={`${growth}%`}
                                  size="small"
                                  icon={parseFloat(growth) >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                                  sx={{
                                    bgcolor: alpha(parseFloat(growth) >= 0 ? theme.palette.success.main : theme.palette.error.main, 0.1),
                                    color: parseFloat(growth) >= 0 ? theme.palette.success.main : theme.palette.error.main,
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Community Tab */}
          {activeTab === DashboardTab.Community && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Community Management
              </Typography>

              {/* Community Stats */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Total Followers', value: '12.5K', icon: <PeopleIcon />, color: theme.palette.primary.main },
                  { title: 'Active Discussions', value: '45', icon: <ForumIcon />, color: theme.palette.info.main },
                  { title: 'Community Posts', value: '128', icon: <DescriptionIcon />, color: theme.palette.secondary.main },
                  { title: 'Engagement Rate', value: '8.2%', icon: <ThumbUpIcon />, color: theme.palette.success.main },
                ].map((stat, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center' }}>
                      <Avatar sx={{ bgcolor: alpha(stat.color, 0.1), color: stat.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        {stat.icon}
                      </Avatar>
                      <Typography variant="h4" fontWeight="bold" color={stat.color}>
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.title}
                      </Typography>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Recent Community Posts */}
              <Card sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Recent Community Posts
                  </Typography>
                  {communityPosts.map((post) => (
                    <Box key={post.id} sx={{ mb: 3, p: 3, border: `1px solid ${alpha(theme.palette.divider, 0.1)}`, borderRadius: 2 }}>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {post.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {post.content}
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          By {post.author} • {post.date}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Chip
                            icon={<ThumbUpIcon />}
                            label={post.likes}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<ForumIcon />}
                            label={post.comments}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Marketing AI Tab */}
          {activeTab === DashboardTab.MarketingAI && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Marketing AI Assistant
              </Typography>

              {/* AI Insights */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {marketingInsights.map((insight, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <Card sx={{
                      p: 3,
                      borderRadius: 3,
                      border: `2px solid ${insight.impact === 'High' ? theme.palette.error.main : theme.palette.warning.main}`,
                      borderColor: alpha(insight.impact === 'High' ? theme.palette.error.main : theme.palette.warning.main, 0.3),
                    }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" fontWeight="bold">
                          {insight.title}
                        </Typography>
                        <Chip
                          label={insight.impact}
                          size="small"
                          color={insight.impact === 'High' ? 'error' : 'warning'}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {insight.description}
                      </Typography>
                      <Chip
                        label={insight.category}
                        size="small"
                        variant="outlined"
                        icon={<CampaignIcon />}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* AI Tools */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Content Generation
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Generate marketing content for your games using AI
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button variant="outlined" size="small">Social Media Posts</Button>
                      <Button variant="outlined" size="small">Press Releases</Button>
                      <Button variant="outlined" size="small">Game Descriptions</Button>
                    </Box>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Market Analysis
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Get insights about your target market and competitors
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button variant="outlined" size="small">Competitor Analysis</Button>
                      <Button variant="outlined" size="small">Trend Analysis</Button>
                      <Button variant="outlined" size="small">Price Optimization</Button>
                    </Box>
                  </Card>
                </Grid>
              </Grid>

              {/* Stormie AI Chat Interface */}
              <Card sx={{ borderRadius: 3, height: 600 }}>
                <CardContent sx={{ p: 0, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  {/* Chat Header */}
                  <Box sx={{
                    p: 3,
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{
                        bgcolor: theme.palette.primary.main,
                        mr: 2,
                        width: 48,
                        height: 48,
                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      }}>
                        <SmartToyIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="bold">
                          Stormie AI Marketing Assistant
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Powered by Gamestorme's marketing intelligence • Online
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {/* Chat Messages */}
                  <Box sx={{
                    flexGrow: 1,
                    p: 3,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                  }}>
                    {/* Welcome Message */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Avatar sx={{
                        bgcolor: theme.palette.primary.main,
                        mr: 2,
                        width: 32,
                        height: 32,
                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      }}>
                        <SmartToyIcon fontSize="small" />
                      </Avatar>
                      <Box sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        p: 2,
                        borderRadius: 2,
                        maxWidth: '80%',
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      }}>
                        <Typography variant="body2">
                          👋 Hi! I'm Stormie, your AI marketing assistant. I can help you with:
                        </Typography>
                        <Box sx={{ mt: 1 }}>
                          <Chip label="📊 Market Analysis" size="small" sx={{ mr: 1, mb: 1 }} />
                          <Chip label="💰 Pricing Strategy" size="small" sx={{ mr: 1, mb: 1 }} />
                          <Chip label="📱 Social Media" size="small" sx={{ mr: 1, mb: 1 }} />
                          <Chip label="🎯 Target Audience" size="small" sx={{ mr: 1, mb: 1 }} />
                          <Chip label="📈 Growth Tactics" size="small" sx={{ mr: 1, mb: 1 }} />
                        </Box>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          What would you like to know about marketing your games?
                        </Typography>
                      </Box>
                    </Box>

                    {/* Sample Conversation */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Box sx={{
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        p: 2,
                        borderRadius: 2,
                        maxWidth: '80%',
                      }}>
                        <Typography variant="body2">
                          How can I improve the marketing for my indie puzzle game?
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Avatar sx={{
                        bgcolor: theme.palette.primary.main,
                        mr: 2,
                        width: 32,
                        height: 32,
                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      }}>
                        <SmartToyIcon fontSize="small" />
                      </Avatar>
                      <Box sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        p: 2,
                        borderRadius: 2,
                        maxWidth: '80%',
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      }}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          Based on your game analytics and current market trends, here are my recommendations:
                        </Typography>
                        <Typography variant="body2" component="div">
                          <strong>🎯 Target Audience:</strong> Focus on casual gamers aged 25-45 who enjoy brain teasers
                          <br /><br />
                          <strong>📱 Platform Strategy:</strong> Mobile-first approach with cross-promotion on social media
                          <br /><br />
                          <strong>💡 Content Ideas:</strong>
                          • Behind-the-scenes puzzle creation videos
                          • Daily puzzle challenges on social media
                          • Collaboration with puzzle influencers
                          <br /><br />
                          <strong>📊 Pricing:</strong> Consider a freemium model with premium puzzle packs at $2.99
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {/* Chat Input */}
                  <Box sx={{
                    p: 3,
                    borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    bgcolor: alpha(theme.palette.background.paper, 0.5),
                  }}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-end' }}>
                      <TextField
                        fullWidth
                        multiline
                        maxRows={3}
                        placeholder="Ask Stormie about marketing strategies, analytics, pricing, or anything else..."
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            bgcolor: theme.palette.background.paper,
                          }
                        }}
                      />
                      <Button
                        variant="contained"
                        color="primary"
                        sx={{
                          minWidth: 56,
                          height: 56,
                          borderRadius: 3,
                          background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                        }}
                      >
                        <SendIcon />
                      </Button>
                    </Box>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      Stormie can make mistakes. Verify important information with your analytics data.
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Documentation Tab */}
          {activeTab === DashboardTab.Documentation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Developer Documentation
              </Typography>

              <Grid container spacing={3}>
                {[
                  {
                    title: 'Getting Started',
                    description: 'Learn how to upload and publish your first game on Gamestorme',
                    icon: <LaunchIcon />,
                    color: theme.palette.primary.main,
                    topics: ['Account Setup', 'Game Upload Process', 'Publishing Guidelines']
                  },
                  {
                    title: 'Monetization Guide',
                    description: 'Maximize your revenue with our monetization strategies',
                    icon: <MonetizationOnIcon />,
                    color: theme.palette.success.main,
                    topics: ['Pricing Strategies', 'In-App Purchases', 'Revenue Sharing']
                  },
                  {
                    title: 'Marketing Tools',
                    description: 'Promote your games effectively using our marketing platform',
                    icon: <CampaignIcon />,
                    color: theme.palette.secondary.main,
                    topics: ['AI Marketing Assistant', 'Social Media Integration', 'Analytics Dashboard']
                  },
                  {
                    title: 'API Documentation',
                    description: 'Integrate with Gamestorme APIs for advanced functionality',
                    icon: <DescriptionIcon />,
                    color: theme.palette.info.main,
                    topics: ['Authentication', 'Game Management API', 'Analytics API']
                  },
                  {
                    title: 'Community Guidelines',
                    description: 'Build and manage your game community effectively',
                    icon: <ForumIcon />,
                    color: theme.palette.warning.main,
                    topics: ['Community Building', 'Moderation Tools', 'Engagement Strategies']
                  },
                  {
                    title: 'Security & Privacy',
                    description: 'Keep your games and user data secure',
                    icon: <SecurityIcon />,
                    color: theme.palette.error.main,
                    topics: ['Data Protection', 'Secure Payments', 'Privacy Compliance']
                  },
                ].map((doc, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Card sx={{
                      p: 3,
                      borderRadius: 3,
                      height: '100%',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.15)}`,
                      },
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar sx={{ bgcolor: alpha(doc.color, 0.1), color: doc.color, mr: 2 }}>
                          {doc.icon}
                        </Avatar>
                        <Typography variant="h6" fontWeight="bold">
                          {doc.title}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        {doc.description}
                      </Typography>
                      <Box sx={{ mb: 3 }}>
                        {doc.topics.map((topic, topicIndex) => (
                          <Chip
                            key={topicIndex}
                            label={topic}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ bgcolor: doc.color, '&:hover': { bgcolor: doc.color } }}
                        startIcon={<LaunchIcon />}
                      >
                        Read Documentation
                      </Button>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Settings Tab */}
          {activeTab === DashboardTab.Settings && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Account Settings
              </Typography>

              <Grid container spacing={3}>
                {/* Profile Settings */}
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main, mr: 2 }}>
                        <PersonIcon />
                      </Avatar>
                      Profile Information
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      <TextField
                        fullWidth
                        label="Company Name"
                        defaultValue="Gamestorme Studios"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Contact Email"
                        defaultValue="<EMAIL>"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Phone Number"
                        defaultValue="+****************"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Website"
                        defaultValue="https://gamestorme.com"
                        sx={{ mb: 3 }}
                      />
                      <Button variant="contained" color="primary">
                        Update Profile
                      </Button>
                    </Box>
                  </Card>
                </Grid>

                {/* Banking Information */}
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main, mr: 2 }}>
                        <PaymentIcon />
                      </Avatar>
                      Banking Information
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      <TextField
                        fullWidth
                        label="Bank Name"
                        defaultValue="Chase Bank"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Account Holder Name"
                        defaultValue="Gamestorme Studios LLC"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Account Number"
                        defaultValue="****1234"
                        type="password"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Routing Number"
                        defaultValue="****5678"
                        type="password"
                        sx={{ mb: 3 }}
                      />
                      <Button variant="contained" color="success">
                        Update Banking
                      </Button>
                    </Box>
                  </Card>
                </Grid>

                {/* Tax Information */}
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main, mr: 2 }}>
                        <DescriptionIcon />
                      </Avatar>
                      Tax Information
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>Country</InputLabel>
                        <Select defaultValue="US" label="Country">
                          <MenuItem value="US">United States</MenuItem>
                          <MenuItem value="CA">Canada</MenuItem>
                          <MenuItem value="UK">United Kingdom</MenuItem>
                          <MenuItem value="DE">Germany</MenuItem>
                          <MenuItem value="FR">France</MenuItem>
                        </Select>
                      </FormControl>
                      <TextField
                        fullWidth
                        label="Tax ID / EIN"
                        defaultValue="12-3456789"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="VAT Number (if applicable)"
                        placeholder="Enter VAT number"
                        sx={{ mb: 2 }}
                      />
                      <FormControl fullWidth sx={{ mb: 3 }}>
                        <InputLabel>Business Type</InputLabel>
                        <Select defaultValue="LLC" label="Business Type">
                          <MenuItem value="LLC">LLC</MenuItem>
                          <MenuItem value="Corporation">Corporation</MenuItem>
                          <MenuItem value="Partnership">Partnership</MenuItem>
                          <MenuItem value="Sole Proprietorship">Sole Proprietorship</MenuItem>
                        </Select>
                      </FormControl>
                      <Button variant="contained" color="warning">
                        Update Tax Info
                      </Button>
                    </Box>
                  </Card>
                </Grid>

                {/* Security Settings */}
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: alpha(theme.palette.error.main, 0.1), color: theme.palette.error.main, mr: 2 }}>
                        <SecurityIcon />
                      </Avatar>
                      Security Settings
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      <TextField
                        fullWidth
                        label="Current Password"
                        type="password"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="New Password"
                        type="password"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Confirm New Password"
                        type="password"
                        sx={{ mb: 3 }}
                      />
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Enable Two-Factor Authentication"
                        sx={{ mb: 2 }}
                      />
                      <FormControlLabel
                        control={<Switch defaultChecked />}
                        label="Email Notifications"
                        sx={{ mb: 3 }}
                      />
                      <Button variant="contained" color="error">
                        Update Security
                      </Button>
                    </Box>
                  </Card>
                </Grid>

                {/* Payout Settings */}
                <Grid item xs={12}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main, mr: 2 }}>
                        <MonetizationOnIcon />
                      </Avatar>
                      Payout Settings
                    </Typography>
                    <Grid container spacing={3} sx={{ mt: 2 }}>
                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Payout Frequency</InputLabel>
                          <Select defaultValue="monthly" label="Payout Frequency">
                            <MenuItem value="weekly">Weekly</MenuItem>
                            <MenuItem value="monthly">Monthly</MenuItem>
                            <MenuItem value="quarterly">Quarterly</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Minimum Payout Amount"
                          defaultValue="$100"
                          InputProps={{
                            startAdornment: <Box sx={{ mr: 1 }}>$</Box>,
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Preferred Currency</InputLabel>
                          <Select defaultValue="USD" label="Preferred Currency">
                            <MenuItem value="USD">USD - US Dollar</MenuItem>
                            <MenuItem value="EUR">EUR - Euro</MenuItem>
                            <MenuItem value="GBP">GBP - British Pound</MenuItem>
                            <MenuItem value="CAD">CAD - Canadian Dollar</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                    <Box sx={{ mt: 3, p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Next Payout:</strong> January 31, 2024 • <strong>Amount:</strong> $2,450.00
                      </Typography>
                    </Box>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

      {/* Support Tab */}
      {activeTab === DashboardTab.Support && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 4 }}>
                Developer Support Center
              </Typography>

              {/* Support Stats */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {[
                  { title: 'Open Tickets', value: '3', icon: <HelpIcon />, color: theme.palette.warning.main },
                  { title: 'Resolved Issues', value: '47', icon: <CheckCircleIcon />, color: theme.palette.success.main },
                  { title: 'Avg Response Time', value: '2.4h', icon: <AccessTimeIcon />, color: theme.palette.info.main },
                  { title: 'Satisfaction Rate', value: '98%', icon: <ThumbUpIcon />, color: theme.palette.primary.main },
                ].map((stat, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card sx={{ p: 3, borderRadius: 3, textAlign: 'center', height: '100%' }}>
                        <Avatar sx={{ bgcolor: alpha(stat.color, 0.1), color: stat.color, width: 56, height: 56, mx: 'auto', mb: 2 }}>
                          {stat.icon}
                        </Avatar>
                        <Typography variant="h4" fontWeight="bold" color={stat.color}>
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {stat.title}
                        </Typography>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>

              {/* Quick Actions */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Submit New Support Ticket
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>Issue Category</InputLabel>
                        <Select defaultValue="" label="Issue Category">
                          <MenuItem value="technical">Technical Issue</MenuItem>
                          <MenuItem value="billing">Billing & Payments</MenuItem>
                          <MenuItem value="game-upload">Game Upload</MenuItem>
                          <MenuItem value="analytics">Analytics & Reports</MenuItem>
                          <MenuItem value="account">Account Management</MenuItem>
                          <MenuItem value="other">Other</MenuItem>
                        </Select>
                      </FormControl>
                      <TextField
                        fullWidth
                        label="Subject"
                        placeholder="Brief description of your issue"
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Description"
                        placeholder="Please provide detailed information about your issue..."
                        sx={{ mb: 3 }}
                      />
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <Button variant="outlined" startIcon={<AttachFileIcon />}>
                          Attach Files
                        </Button>
                        <Button variant="contained" color="primary">
                          Submit Ticket
                        </Button>
                      </Box>
                    </Box>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Quick Help
                    </Typography>
                    <List>
                      {[
                        { title: 'Game Upload Guide', icon: <UploadIcon />, color: theme.palette.primary.main },
                        { title: 'Payment Setup', icon: <PaymentIcon />, color: theme.palette.success.main },
                        { title: 'Analytics Tutorial', icon: <BarChartIcon />, color: theme.palette.info.main },
                        { title: 'API Documentation', icon: <CodeIcon />, color: theme.palette.secondary.main },
                      ].map((item, index) => (
                        <ListItemButton key={index} sx={{ borderRadius: 2, mb: 1 }}>
                          <ListItemIcon>
                            <Avatar sx={{ bgcolor: alpha(item.color, 0.1), color: item.color, width: 32, height: 32 }}>
                              {item.icon}
                            </Avatar>
                          </ListItemIcon>
                          <ListItemText primary={item.title} />
                          <LaunchIcon fontSize="small" color="action" />
                        </ListItemButton>
                      ))}
                    </List>
                  </Card>
                </Grid>
              </Grid>

              {/* Support Tickets */}
              <Card sx={{ borderRadius: 3, mb: 4 }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h6" fontWeight="bold">
                      Your Support Tickets
                    </Typography>
                    <Button variant="outlined" startIcon={<DownloadIcon />}>
                      Export to Excel
                    </Button>
                  </Box>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Ticket ID</TableCell>
                          <TableCell>Subject</TableCell>
                          <TableCell>Category</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Priority</TableCell>
                          <TableCell>Created</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {[
                          { id: 'TK-001', subject: 'Game upload failing', category: 'Technical', status: 'Open', priority: 'High', created: '2024-01-15' },
                          { id: 'TK-002', subject: 'Payment not received', category: 'Billing', status: 'In Progress', priority: 'Medium', created: '2024-01-14' },
                          { id: 'TK-003', subject: 'Analytics data missing', category: 'Analytics', status: 'Resolved', priority: 'Low', created: '2024-01-12' },
                        ].map((ticket) => (
                          <TableRow key={ticket.id}>
                            <TableCell>{ticket.id}</TableCell>
                            <TableCell>{ticket.subject}</TableCell>
                            <TableCell>{ticket.category}</TableCell>
                            <TableCell>
                              <Chip
                                label={ticket.status}
                                size="small"
                                color={ticket.status === 'Open' ? 'error' : ticket.status === 'In Progress' ? 'warning' : 'success'}
                              />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={ticket.priority}
                                size="small"
                                variant="outlined"
                                color={ticket.priority === 'High' ? 'error' : ticket.priority === 'Medium' ? 'warning' : 'default'}
                              />
                            </TableCell>
                            <TableCell>{ticket.created}</TableCell>
                            <TableCell>
                              <Button size="small" variant="outlined">
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>

              {/* Knowledge Base */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Popular Articles
                    </Typography>
                    <List>
                      {[
                        'How to upload your first game',
                        'Setting up payment methods',
                        'Understanding analytics data',
                        'Marketing your game effectively',
                        'Troubleshooting common issues',
                      ].map((article, index) => (
                        <ListItemButton key={index} sx={{ borderRadius: 2 }}>
                          <ListItemIcon>
                            <ArticleIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary={article} />
                        </ListItemButton>
                      ))}
                    </List>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card sx={{ p: 3, borderRadius: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Contact Information
                    </Typography>
                    <Box sx={{ mt: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <EmailIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
                        <Typography><EMAIL></Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <PhoneIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
                        <Typography>+1 (555) 123-GAME</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <ChatIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
                        <Typography>Live Chat (9 AM - 6 PM EST)</Typography>
                      </Box>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="body2" color="text.secondary">
                        Our support team is available Monday through Friday, 9 AM to 6 PM EST.
                        For urgent issues, please mark your ticket as high priority.
                      </Typography>
                    </Box>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          )}

      {/* AI Chat Interface */}
      <ChatDrawer
        anchor="right"
        open={chatOpen}
        onClose={handleChatToggle}
      >
        <Box sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          color: 'white',
        }}>
          <Avatar
            sx={{
              bgcolor: '#ffffff',
              color: theme.palette.primary.main,
              mr: 2,
            }}
          >
            <SmartToyIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Stormy
            </Typography>
            <Typography variant="body2">
              AI Marketing Assistant
            </Typography>
          </Box>
          <IconButton
            onClick={handleChatToggle}
            sx={{
              ml: 'auto',
              color: 'white',
            }}
          >
            <Box component="span" sx={{ fontSize: '1.5rem' }}>×</Box>
          </IconButton>
        </Box>

        <Box sx={{
          height: 'calc(100% - 140px)',
          overflowY: 'auto',
          p: 2,
          display: 'flex',
          flexDirection: 'column',
        }}>
          {chatMessages.map((message, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                mb: 2,
              }}
            >
              {message.sender === 'ai' && (
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    mr: 1,
                    bgcolor: theme.palette.primary.main,
                  }}
                >
                  <SmartToyIcon sx={{ fontSize: '1rem' }} />
                </Avatar>
              )}
              <Paper
                sx={{
                  p: 2,
                  maxWidth: '80%',
                  borderRadius: theme.shape.borderRadius * 2,
                  bgcolor: message.sender === 'user'
                    ? alpha(theme.palette.primary.main, 0.1)
                    : alpha(theme.palette.background.paper, 0.8),
                  color: message.sender === 'user'
                    ? theme.palette.primary.main
                    : theme.palette.text.primary,
                  boxShadow: message.sender === 'user'
                    ? 'none'
                    : `0 2px 10px ${alpha(theme.palette.common.black, 0.05)}`,
                  borderTopLeftRadius: message.sender === 'ai' ? 0 : undefined,
                  borderTopRightRadius: message.sender === 'user' ? 0 : undefined,
                }}
              >
                <Typography variant="body2">
                  {message.text}
                </Typography>
              </Paper>
              {message.sender === 'user' && (
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    ml: 1,
                    bgcolor: theme.palette.primary.main,
                  }}
                >
                  JD
                </Avatar>
              )}
            </Box>
          ))}
        </Box>

        <Box sx={{
          p: 2,
          borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          display: 'flex',
          alignItems: 'center',
        }}>
          <TextField
            fullWidth
            placeholder="Ask Stormy about marketing strategies..."
            variant="outlined"
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSendMessage();
              }
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: theme.shape.borderRadius * 3,
                pr: 0,
              },
            }}
            InputProps={{
              endAdornment: (
                <IconButton
                  color="primary"
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim()}
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    mr: 0.5,
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.2),
                    }
                  }}
                >
                  <SendIcon />
                </IconButton>
              ),
            }}
          />
        </Box>
      </ChatDrawer>

      {/* Logout Dialog */}
      <Dialog 
        open={logoutDialogOpen} 
        onClose={() => setLogoutDialogOpen(false)}
      >
        <DialogTitle>Confirm Logout</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to logout from your developer session?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLogoutDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleLogout} color="error" variant="contained">
            Logout
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedDashboard>
  );
};

export default DeveloperDashboard;
