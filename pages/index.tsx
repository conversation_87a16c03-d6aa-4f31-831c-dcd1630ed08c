import type { NextPage } from 'next';
import { Box } from '@mui/material';
import Head from 'next/head';
import Layout from '../components/layout/Layout';
import Hero from '../components/Home/Hero';
import Features from '../components/Home/Features';
import GamesShowcase from '../components/Home/GamesShowcase';
import CallToAction from '../components/Home/CallToAction';
import Careers from '../components/Home/Careers';

const Home: NextPage = () => {
  return (
    <Layout>
      <Head>
        <title>Gamestorme - Empowering Game Developers Through Innovative Solutions</title>
        <meta name="description" content="Digital game store powered by AI marketing tools and blockchain technology. Boost your game sales with our AI-powered marketing platform designed for developers." />
      </Head>

      <Hero />
      <Features />
      <GamesShowcase />
      <CallToAction />
      <Careers />

      {/* Add space at the bottom for better UX */}
      <Box sx={{ height: { xs: 60, md: 100 } }} />
    </Layout>
  );
};

export default Home;
