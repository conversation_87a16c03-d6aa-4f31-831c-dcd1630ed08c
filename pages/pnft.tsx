import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import Image from 'next/image';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import VerifiedIcon from '@mui/icons-material/Verified';
import Link from 'next/link';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(6),
  maxWidth: '800px',
}));

const NFTCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
    '& .nft-image': {
      transform: 'scale(1.05)',
    },
  },
}));

const NFTImage = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: 300,
  width: '100%',
  overflow: 'hidden',
  '& img': {
    transition: 'transform 0.5s ease',
  },
  '&:hover img': {
    transform: 'scale(1.05)',
  },
}));

const NFTTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const NFTDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(2),
  flexGrow: 1,
}));

const NFTChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0, 0.5, 0.5, 0),
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.main,
  fontWeight: 500,
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const FeatureTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(2),
  color: theme.palette.primary.main,
}));

// Sample NFT data
const nfts = [
  {
    id: 1,
    title: 'Cosmic Guardian',
    description: 'A legendary guardian from the Cosmic Realm with unique abilities and powers.',
    image: '/nft1.jpg',
    price: '0.5 ETH',
    rarity: 'Legendary',
    collection: 'Cosmic Guardians',
  },
  {
    id: 2,
    title: 'Mystic Dragon',
    description: 'A rare dragon with mystical powers that can be used in multiple games across the platform.',
    image: '/nft2.jpg',
    price: '0.3 ETH',
    rarity: 'Rare',
    collection: 'Mythical Creatures',
  },
  {
    id: 3,
    title: 'Cyber Samurai',
    description: 'A futuristic samurai warrior with enhanced cybernetic abilities and unique weapons.',
    image: '/nft3.jpg',
    price: '0.4 ETH',
    rarity: 'Epic',
    collection: 'Cyber Warriors',
  },
];

// Features of pNFTs
const features = [
  {
    title: 'Cross-Game Compatibility',
    description: 'Use your pNFTs across multiple games in the Gamestorme ecosystem, maximizing their utility and value.',
  },
  {
    title: 'True Ownership',
    description: 'Enjoy full ownership of your digital assets with blockchain-backed verification and transferability.',
  },
  {
    title: 'Play-to-Earn Mechanics',
    description: 'Earn rewards by using your pNFTs in games, participating in events, or trading on the marketplace.',
  },
  {
    title: 'Evolving Assets',
    description: 'Watch your pNFTs grow and evolve as you use them in games, unlocking new abilities and appearances.',
  },
];

const PNFT: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Playable"
        highlightedTitle="NFTs"
        description="Discover unique digital assets that you can truly own and use across multiple games in our ecosystem."
      />

      {/* What are pNFTs Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <SectionTitle variant="h3">What are pNFTs?</SectionTitle>
                  <Typography variant="body1" paragraph sx={{ mb: 3 }}>
                    Playable Non-Fungible Tokens (pNFTs) are unique digital assets that exist on the blockchain and can be used across multiple games and applications within the Gamestorme ecosystem.
                  </Typography>
                  <Typography variant="body1" paragraph>
                    Unlike traditional in-game items, pNFTs offer true ownership, allowing you to trade, sell, or use them as you see fit. Each pNFT has unique properties, abilities, and visual characteristics that make them valuable both in-game and in the marketplace.
                  </Typography>
                  <Typography variant="body1" paragraph>
                    With pNFTs, your gaming assets have real-world value and utility that extends beyond a single game, creating a new paradigm for digital ownership in gaming.
                  </Typography>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Box sx={{ position: 'relative', width: '100%', height: 400, borderRadius: '20px', overflow: 'hidden' }}>
                    <Image
                      src="/pnft-concept.jpg"
                      alt="pNFT Concept"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </Box>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Features Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.5) }}>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Key Features</SectionTitle>
            <SectionSubtitle variant="h6">
              Discover the unique benefits that make our pNFTs revolutionary in the gaming space.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {features.map((feature, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div variants={itemVariants}>
                    <FeatureCard>
                      <FeatureTitle variant="h5">{feature.title}</FeatureTitle>
                      <Typography variant="body2" color="text.secondary">
                        {feature.description}
                      </Typography>
                    </FeatureCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Featured pNFTs Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Featured pNFTs</SectionTitle>
            <SectionSubtitle variant="h6">
              Explore our collection of unique playable NFTs that offer special abilities and benefits across our games.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {nfts.map((nft) => (
                <Grid item xs={12} sm={6} md={4} key={nft.id}>
                  <motion.div variants={itemVariants} style={{ height: '100%' }}>
                    <NFTCard>
                      <NFTImage>
                        <Image
                          src={nft.image}
                          alt={nft.title}
                          fill
                          className="nft-image"
                          style={{ objectFit: 'cover' }}
                        />
                      </NFTImage>
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <NFTTitle variant="h5">{nft.title}</NFTTitle>
                          <VerifiedIcon sx={{ color: theme.palette.primary.main }} />
                        </Box>
                        <NFTDescription variant="body2">
                          {nft.description}
                        </NFTDescription>
                        <Box sx={{ mb: 2 }}>
                          <NFTChip label={nft.rarity} size="small" />
                          <NFTChip label={nft.collection} size="small" />
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            Price:
                          </Typography>
                          <Typography variant="body1" fontWeight="bold" color="primary">
                            {nft.price}
                          </Typography>
                        </Box>
                        <Button
                          variant="contained"
                          color="primary"
                          fullWidth
                          component={Link}
                          href={`/marketplace/${nft.id}`}
                          endIcon={<ArrowForwardIcon />}
                        >
                          View Details
                        </Button>
                      </CardContent>
                    </NFTCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default PNFT;
