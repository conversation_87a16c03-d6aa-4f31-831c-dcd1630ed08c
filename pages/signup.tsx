import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  TextField,
  Button,
  Paper,
  Divider,
  InputAdornment,
  IconButton,
  useTheme,
  alpha,
  Link as MuiLink,
  Grid,
  Card,
  CardActionArea,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Alert,
  Avatar,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import { motion, AnimatePresence } from 'framer-motion';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import BusinessIcon from '@mui/icons-material/Business';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import GoogleIcon from '@mui/icons-material/Google';
import FacebookIcon from '@mui/icons-material/Facebook';
import AppleIcon from '@mui/icons-material/Apple';
import CelebrationIcon from '@mui/icons-material/Celebration';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';
import { UserType } from '../contexts/AuthContext';
import Confetti from 'react-confetti';

// Styled components
const SignupContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  minHeight: 'calc(100vh - 200px)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 50% 50%, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 70%)`,
    zIndex: -1,
  },
}));

const SignupPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(6),
  borderRadius: theme.shape.borderRadius * 2,
  maxWidth: 800,
  width: '100%',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    boxShadow: `0 15px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
  position: 'relative',
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '5px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  },
}));

const SocialButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(1.5),
  borderRadius: theme.shape.borderRadius * 1.5,
  border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: alpha(theme.palette.background.paper, 0.8),
    transform: 'translateY(-3px)',
    boxShadow: `0 6px 15px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const OrDivider = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  margin: theme.spacing(3, 0),
}));

const UserTypeCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  border: `2px solid transparent`,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.common.black, 0.15)}`,
  },
}));

const SelectedCard = styled(UserTypeCard)(({ theme }) => ({
  borderColor: theme.palette.primary.main,
  boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.2)}`,
  transform: 'translateY(-8px) scale(1.02)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '30px',
    height: '30px',
    background: theme.palette.primary.main,
    borderBottomLeftRadius: '100%',
    zIndex: 1,
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 70,
  height: 70,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto',
  marginBottom: theme.spacing(2),
  transition: 'all 0.3s ease',
  '& svg': {
    fontSize: 35,
    color: theme.palette.primary.main,
    transition: 'all 0.3s ease',
  },
  '&:hover': {
    transform: 'scale(1.1) rotate(5deg)',
    backgroundColor: alpha(theme.palette.primary.main, 0.2),
    '& svg': {
      transform: 'scale(1.1)',
    },
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    transition: 'all 0.3s ease',
    borderRadius: theme.shape.borderRadius * 1.5,
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: `0 4px 10px ${alpha(theme.palette.primary.main, 0.1)}`,
    },
    '&.Mui-focused': {
      transform: 'translateY(-2px)',
      boxShadow: `0 4px 15px ${alpha(theme.palette.primary.main, 0.15)}`,
    },
  },
}));

const AnimatedButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(1.2, 3),
  transition: 'all 0.3s ease',
  position: 'relative',
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'rgba(255, 255, 255, 0.2)',
    transform: 'translateX(-100%)',
  },
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: `0 6px 15px ${alpha(theme.palette.primary.main, 0.3)}`,
    '&::after': {
      transform: 'translateX(100%)',
      transition: 'transform 0.6s ease',
    },
  },
}));

const DisclaimerBox = styled(Box)(({ theme }) => ({
  maxHeight: '250px',
  overflowY: 'auto',
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  borderRadius: theme.shape.borderRadius * 1.5,
  border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
  backgroundColor: alpha(theme.palette.background.paper, 0.7),
  backdropFilter: 'blur(10px)',
  fontSize: '0.875rem',
  lineHeight: 1.6,
  boxShadow: `inset 0 0 20px ${alpha(theme.palette.common.black, 0.05)}`,
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40px',
    background: `linear-gradient(to top, ${alpha(theme.palette.background.paper, 0.9)}, transparent)`,
    pointerEvents: 'none',
    opacity: 0.7,
    borderBottomLeftRadius: theme.shape.borderRadius * 1.5,
    borderBottomRightRadius: theme.shape.borderRadius * 1.5,
    zIndex: 1,
  },
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: alpha(theme.palette.divider, 0.1),
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: alpha(theme.palette.primary.main, 0.2),
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: alpha(theme.palette.primary.main, 0.4),
  },
  '& strong': {
    color: theme.palette.primary.main,
    fontWeight: 600,
  },
  '& p': {
    marginBottom: theme.spacing(2),
  },
}));

// Steps for the signup process
const steps = ['Choose Account Type', 'Create Account', 'Complete Profile'];

const Signup: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const { signUp, currentUser, userProfile, setIsAuthPage } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [userType, setUserType] = useState<UserType | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showConfetti, setShowConfetti] = useState(false);
  const [signupSuccess, setSignupSuccess] = useState(false);
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [hasAgreedToTerms, setHasAgreedToTerms] = useState(false);
  const disclaimerRef = useRef<HTMLDivElement>(null);
  const windowSize = useRef({ width: typeof window !== 'undefined' ? window.innerWidth : 800, height: typeof window !== 'undefined' ? window.innerHeight : 800 });

  // Set isAuthPage to true when component mounts
  useEffect(() => {
    setIsAuthPage(true);
    return () => {
      setIsAuthPage(false);
    };
  }, [setIsAuthPage]);

  // Update window size for confetti
  useEffect(() => {
    const handleResize = () => {
      windowSize.current = {
        width: window.innerWidth,
        height: window.innerHeight
      };
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // Redirect if already logged in, but only if not on an auth page
  useEffect(() => {
    // Only redirect if user is logged in and we're on the signup page intentionally
    if (currentUser && userProfile) {
      console.log('User already logged in, redirecting based on user type:', userProfile.userType);
      // Check URL query parameters for redirect
      const { redirect } = router.query;
      if (redirect && typeof redirect === 'string') {
        router.push(redirect);
      } else {
        // Default redirect based on user type
        if (userProfile.userType === 'developer') {
          router.push('/developer/dashboard');
        } else if (userProfile.userType === 'gamer') {
          router.push('/gamer/dashboard');
        } else {
          // Fallback for unknown user types
          router.push('/gamer/dashboard');
        }
      }
    }
  }, [currentUser, userProfile, router]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
    exit: {
      opacity: 0,
      y: 20,
      transition: {
        duration: 0.3,
      },
    },
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
        duration: 0.5,
      },
    },
    exit: {
      opacity: 0,
      x: -50,
      transition: {
        duration: 0.3,
      },
    },
  };

  const successVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 20,
      },
    },
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const validateStep = () => {
    let isValid = true;
    const newErrors = { ...errors };

    if (activeStep === 0) {
      if (!userType) {
        isValid = false;
        // We'll handle this with a UI indication
      }
    } else if (activeStep === 1) {
      if (!formData.firstName.trim()) {
        newErrors.firstName = 'First name is required';
        isValid = false;
      } else {
        newErrors.firstName = '';
      }

      if (!formData.lastName.trim()) {
        newErrors.lastName = 'Last name is required';
        isValid = false;
      } else {
        newErrors.lastName = '';
      }

      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
        isValid = false;
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
        isValid = false;
      } else {
        newErrors.email = '';
      }

      if (!formData.password) {
        newErrors.password = 'Password is required';
        isValid = false;
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
        isValid = false;
      } else {
        newErrors.password = '';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
        isValid = false;
      } else {
        newErrors.confirmPassword = '';
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleNext = async () => {
    if (validateStep()) {
      if (activeStep === steps.length - 1) {
        // Submit the form
        setLoading(true);
        setError('');

        try {
          if (!userType) {
            throw new Error('Please select a user type');
          }

          // Create display name from first and last name
          const displayName = `${formData.firstName} ${formData.lastName}`;

          // Sign up with Firebase
          await signUp(formData.email, formData.password, displayName, userType);

          // Show success animation and confetti
          setSignupSuccess(true);
          setShowConfetti(true);

          // Show success message with animation
          toast.success('🎉 Account created successfully!', {
            position: "top-center",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            style: {
              background: `linear-gradient(to right, ${theme.palette.success.light}, ${theme.palette.success.main})`,
              color: 'white',
              borderRadius: '10px',
              boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
            },
          });

          // Stop confetti after 5 seconds
          setTimeout(() => {
            setShowConfetti(false);
          }, 5000);

          // Redirect to the appropriate dashboard after a short delay
          setTimeout(() => {
            if (userType === 'developer') {
              router.push('/developer/dashboard');
            } else {
              router.push('/gamer/dashboard');
            }
          }, 3000);
        } catch (err: any) {
          console.error('Signup error:', err);

          // Handle Firebase auth errors with more specific messages
          let errorMessage = 'Failed to create account. Please try again.';

          if (err.code) {
            switch (err.code) {
              case 'auth/email-already-in-use':
                errorMessage = 'This email is already in use. Please try another email or login.';
                break;
              case 'auth/invalid-email':
                errorMessage = 'The email address is not valid.';
                break;
              case 'auth/weak-password':
                errorMessage = 'The password is too weak. Please use a stronger password.';
                break;
              case 'auth/network-request-failed':
                errorMessage = 'Network error. Please check your internet connection.';
                break;
              default:
                errorMessage = err.message || 'Failed to create account. Please try again.';
            }
          }

          setError(errorMessage);

          // Show error toast with animation
          toast.error(errorMessage, {
            position: "top-center",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            style: {
              background: `linear-gradient(to right, ${theme.palette.error.light}, ${theme.palette.error.main})`,
              color: 'white',
              borderRadius: '10px',
              boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
            },
          });

          // Go back to the account creation step if there's an error
          setActiveStep(1);
        } finally {
          setLoading(false);
        }
      } else {
        // Animate to next step
        setActiveStep((prevStep) => prevStep + 1);
      }
    } else {
      // Shake animation for validation errors
      const form = document.getElementById('signup-form');
      if (form) {
        form.classList.add('shake-animation');
        setTimeout(() => {
          form.classList.remove('shake-animation');
        }, 500);
      }
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Handle disclaimer scroll event
  const handleDisclaimerScroll = () => {
    if (disclaimerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = disclaimerRef.current;
      // Check if user has scrolled to the bottom (with a small threshold)
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isAtBottom && !hasScrolledToBottom) {
        setHasScrolledToBottom(true);

        // Add a visual indicator with animation
        const scrollIndicator = document.getElementById('scroll-indicator');
        if (scrollIndicator) {
          scrollIndicator.classList.add('pulse-animation');
        }
      }
    }
  };

  // Reset scroll state when moving back from the final step
  useEffect(() => {
    if (activeStep !== 2) {
      setHasScrolledToBottom(false);
      setHasAgreedToTerms(false);
    }
  }, [activeStep]);

  // Handle terms agreement checkbox
  const handleTermsAgreement = (event: React.ChangeEvent<HTMLInputElement>) => {
    setHasAgreedToTerms(event.target.checked);
  };

  const renderStepContent = () => {
    // If signup is successful, show success screen
    if (signupSuccess) {
      return (
        <motion.div
          variants={successVariants}
          initial="hidden"
          animate="visible"
        >
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Box sx={{
              display: 'flex',
              justifyContent: 'center',
              mb: 4,
              position: 'relative',
            }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  bgcolor: theme.palette.success.main,
                  boxShadow: `0 10px 30px ${alpha(theme.palette.success.main, 0.3)}`,
                }}
              >
                <CelebrationIcon sx={{ fontSize: 60 }} />
              </Avatar>
              <Box sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                animation: 'pulse 2s infinite',
                borderRadius: '50%',
                backgroundColor: alpha(theme.palette.success.main, 0.2),
                '@keyframes pulse': {
                  '0%': {
                    transform: 'scale(1)',
                    opacity: 0.8,
                  },
                  '70%': {
                    transform: 'scale(1.5)',
                    opacity: 0,
                  },
                  '100%': {
                    transform: 'scale(1.5)',
                    opacity: 0,
                  },
                },
              }} />
            </Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Welcome to Gamestorme!
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              Your account has been created successfully. Redirecting you to your dashboard...
            </Typography>
            <CircularProgress size={30} sx={{ mt: 2 }} />
          </Box>
        </motion.div>
      );
    }

    // Otherwise, show the appropriate step
    switch (activeStep) {
      case 0:
        return (
          <motion.div
            key="step-0"
            variants={stepVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <Box sx={{ mt: 4 }}>
              <Typography
                variant="h5"
                gutterBottom
                sx={{
                  fontWeight: 700,
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 3,
                }}
              >
                Select your account type:
              </Typography>
              <Grid container spacing={4} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {userType === 'developer' ? (
                      <SelectedCard onClick={() => setUserType('developer')}>
                        <CardActionArea sx={{ p: 3, height: '100%' }}>
                          <IconBox>
                            <BusinessIcon />
                          </IconBox>
                          <Typography variant="h5" align="center" fontWeight="bold" gutterBottom>
                            Game Developer
                          </Typography>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Upload and sell your games, access analytics, marketing tools, and developer resources.
                          </Typography>
                        </CardActionArea>
                      </SelectedCard>
                    ) : (
                      <UserTypeCard onClick={() => setUserType('developer')}>
                        <CardActionArea sx={{ p: 3, height: '100%' }}>
                          <IconBox>
                            <BusinessIcon />
                          </IconBox>
                          <Typography variant="h5" align="center" fontWeight="bold" gutterBottom>
                            Game Developer
                          </Typography>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Upload and sell your games, access analytics, marketing tools, and developer resources.
                          </Typography>
                        </CardActionArea>
                      </UserTypeCard>
                    )}
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={6}>
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {userType === 'gamer' ? (
                      <SelectedCard onClick={() => setUserType('gamer')}>
                        <CardActionArea sx={{ p: 3, height: '100%' }}>
                          <IconBox>
                            <SportsEsportsIcon />
                          </IconBox>
                          <Typography variant="h5" align="center" fontWeight="bold" gutterBottom>
                            Gamer
                          </Typography>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Purchase and play games, connect with friends, track your game collection, and more.
                          </Typography>
                        </CardActionArea>
                      </SelectedCard>
                    ) : (
                      <UserTypeCard onClick={() => setUserType('gamer')}>
                        <CardActionArea sx={{ p: 3, height: '100%' }}>
                          <IconBox>
                            <SportsEsportsIcon />
                          </IconBox>
                          <Typography variant="h5" align="center" fontWeight="bold" gutterBottom>
                            Gamer
                          </Typography>
                          <Typography variant="body1" color="text.secondary" align="center">
                            Purchase and play games, connect with friends, track your game collection, and more.
                          </Typography>
                        </CardActionArea>
                      </UserTypeCard>
                    )}
                  </motion.div>
                </Grid>
              </Grid>
            </Box>
          </motion.div>
        );
      case 1:
        return (
          <motion.div
            key="step-1"
            variants={stepVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <Box sx={{ mt: 4 }} id="signup-form">
              <Typography
                variant="h5"
                gutterBottom
                sx={{
                  fontWeight: 700,
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 3,
                }}
              >
                Create your account:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <StyledTextField
                    fullWidth
                    label="First Name"
                    name="firstName"
                    variant="outlined"
                    margin="normal"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    error={!!errors.firstName}
                    helperText={errors.firstName}
                    disabled={loading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="primary" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <StyledTextField
                    fullWidth
                    label="Last Name"
                    name="lastName"
                    variant="outlined"
                    margin="normal"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    error={!!errors.lastName}
                    helperText={errors.lastName}
                    disabled={loading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="primary" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <StyledTextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    variant="outlined"
                    margin="normal"
                    value={formData.email}
                    onChange={handleInputChange}
                    error={!!errors.email}
                    helperText={errors.email}
                    disabled={loading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <EmailIcon color="primary" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <StyledTextField
                    fullWidth
                    label="Password"
                    name="password"
                    variant="outlined"
                    margin="normal"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    error={!!errors.password}
                    helperText={errors.password}
                    disabled={loading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon color="primary" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                            disabled={loading}
                          >
                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <StyledTextField
                    fullWidth
                    label="Confirm Password"
                    name="confirmPassword"
                    variant="outlined"
                    margin="normal"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    error={!!errors.confirmPassword}
                    helperText={errors.confirmPassword}
                    disabled={loading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon color="primary" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
          </motion.div>
        );
      case 2:
        return (
          <motion.div
            key="step-2"
            variants={stepVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <Box sx={{ mt: 4, textAlign: 'center' }}>
              <Typography
                variant="h5"
                fontWeight="bold"
                gutterBottom
                sx={{
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Ready to complete your registration?
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Please review our terms and conditions before creating your account.
              </Typography>
              <motion.div
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      bgcolor: theme.palette.primary.main,
                      boxShadow: `0 10px 30px ${alpha(theme.palette.primary.main, 0.3)}`,
                    }}
                  >
                    {userType === 'developer' ?
                      <BusinessIcon sx={{ fontSize: 40 }} /> :
                      <SportsEsportsIcon sx={{ fontSize: 40 }} />
                    }
                  </Avatar>
                </Box>
              </motion.div>
              <Box sx={{
                p: 3,
                borderRadius: theme.shape.borderRadius * 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.5),
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                mb: 3,
                textAlign: 'left',
              }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  {formData.firstName} {formData.lastName}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {formData.email}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    mt: 1,
                    color: userType === 'developer' ? theme.palette.primary.main : theme.palette.secondary.main,
                    fontWeight: 'bold',
                  }}
                >
                  {userType === 'developer' ? 'Developer Account' : 'Gamer Account'}
                </Typography>
              </Box>

              {/* Disclaimer Box */}
              <Box sx={{ textAlign: 'left', mb: 3 }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Terms and Conditions
                </Typography>
                <DisclaimerBox
                  ref={disclaimerRef}
                  onScroll={handleDisclaimerScroll}
                >
                  <Typography variant="body2" paragraph>
                    <strong>1. ACCEPTANCE OF TERMS</strong><br />
                    By accessing or using Gamestorme's services, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use our services.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>2. ACCOUNT REGISTRATION</strong><br />
                    To use certain features of Gamestorme, you must register for an account. You agree to provide accurate information and to keep this information updated. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>3. USER CONDUCT</strong><br />
                    You agree not to use Gamestorme for any illegal purposes or in violation of any local, state, national, or international law. You agree not to upload, post, or transmit content that is harmful, threatening, abusive, harassing, defamatory, or otherwise objectionable.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>4. INTELLECTUAL PROPERTY</strong><br />
                    All content on Gamestorme, including but not limited to text, graphics, logos, and software, is the property of Gamestorme or its content suppliers and is protected by copyright and other intellectual property laws.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>5. PRIVACY POLICY</strong><br />
                    Your use of Gamestorme is also governed by our Privacy Policy, which outlines how we collect, use, and protect your personal information.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>6. LIMITATION OF LIABILITY</strong><br />
                    Gamestorme shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your access to or use of, or inability to access or use, the services.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>7. TERMINATION</strong><br />
                    Gamestorme reserves the right to terminate or suspend your account and access to the services at any time, without notice, for conduct that we believe violates these Terms and Conditions or is harmful to other users, us, or third parties, or for any other reason.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>8. CHANGES TO TERMS</strong><br />
                    Gamestorme reserves the right to modify these Terms and Conditions at any time. We will provide notice of significant changes by posting a notice on our website or by sending you an email.
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>9. GOVERNING LAW</strong><br />
                    These Terms and Conditions shall be governed by and construed in accordance with the laws of the jurisdiction in which Gamestorme operates, without regard to its conflict of law provisions.
                  </Typography>
                  <Typography variant="body2">
                    <strong>10. CONTACT INFORMATION</strong><br />
                    If you have any questions about these Terms and Conditions, please contact <NAME_EMAIL>.
                  </Typography>
                </DisclaimerBox>

                {/* Scroll indicator */}
                <Box
                  id="scroll-indicator"
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    mt: 1,
                    mb: 2,
                    color: hasScrolledToBottom ? theme.palette.success.main : theme.palette.text.secondary,
                    transition: 'color 0.3s ease',
                  }}
                >
                  {hasScrolledToBottom ? (
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ marginRight: '4px', fontSize: '16px' }}>✓</span> You've read the entire document
                    </Typography>
                  ) : (
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
                      Please scroll to the bottom to continue
                    </Typography>
                  )}
                </Box>

                {/* Agreement checkbox */}
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={hasAgreedToTerms}
                      onChange={handleTermsAgreement}
                      color="primary"
                      disabled={!hasScrolledToBottom}
                      className={hasScrolledToBottom && !hasAgreedToTerms ? 'pulse-animation' : ''}
                      sx={{
                        '&.Mui-checked': {
                          color: theme.palette.success.main,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: hasScrolledToBottom ? 600 : 400,
                        color: hasScrolledToBottom
                          ? hasAgreedToTerms
                            ? theme.palette.success.main
                            : theme.palette.text.primary
                          : theme.palette.text.secondary,
                      }}
                    >
                      I have read and agree to the Terms and Conditions
                    </Typography>
                  }
                  sx={{
                    opacity: hasScrolledToBottom ? 1 : 0.7,
                    transition: 'all 0.3s ease',
                    transform: hasScrolledToBottom && !hasAgreedToTerms ? 'scale(1.03)' : 'scale(1)',
                    animation: hasScrolledToBottom && !hasAgreedToTerms ? 'pulse 2s infinite' : 'none',
                  }}
                />
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Click "Complete" to create your {userType} account and start your journey with Gamestorme.
              </Typography>
            </Box>
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <Layout>
      <SignupContainer>
        {/* Confetti effect when signup is successful */}
        {showConfetti && (
          <Confetti
            width={windowSize.current.width}
            height={windowSize.current.height}
            recycle={false}
            numberOfPieces={500}
            gravity={0.15}
            colors={[
              theme.palette.primary.main,
              theme.palette.secondary.main,
              theme.palette.success.main,
              theme.palette.info.main,
              '#FFD700', // Gold
              '#FF6B6B', // Light Red
              '#4ECDC4', // Teal
            ]}
          />
        )}

        <Container maxWidth="md">
          <AnimatePresence mode="wait">
            <motion.div
              key={`step-${activeStep}-container`}
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <SignupPaper elevation={3}>
                <Typography
                  variant="h4"
                  fontWeight="bold"
                  align="center"
                  gutterBottom
                  sx={{
                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontFamily: '"Poppins", sans-serif',
                  }}
                >
                  {signupSuccess ? 'Welcome to Gamestorme!' : 'Create Your Account'}
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  align="center"
                  sx={{
                    mb: 4,
                    fontWeight: 500,
                  }}
                >
                  {signupSuccess ? 'Your journey begins now!' : 'Join the Gamestorme community'}
                </Typography>

                {!signupSuccess && (
                  <Stepper
                    activeStep={activeStep}
                    alternativeLabel
                    sx={{
                      mb: 4,
                      '& .MuiStepLabel-root .Mui-completed': {
                        color: theme.palette.success.main,
                      },
                      '& .MuiStepLabel-root .Mui-active': {
                        color: theme.palette.primary.main,
                      },
                    }}
                  >
                    {steps.map((label) => (
                      <Step key={label}>
                        <StepLabel>{label}</StepLabel>
                      </Step>
                    ))}
                  </Stepper>
                )}

                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Alert
                      severity="error"
                      sx={{
                        mb: 3,
                        borderRadius: theme.shape.borderRadius * 1.5,
                        boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.2)}`,
                      }}
                    >
                      {error}
                    </Alert>
                  </motion.div>
                )}

                {renderStepContent()}

                {!signupSuccess && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                    <AnimatedButton
                      variant="outlined"
                      color="primary"
                      onClick={handleBack}
                      disabled={activeStep === 0 || loading}
                      sx={{
                        px: 3,
                        py: 1.2,
                        fontWeight: 600,
                      }}
                    >
                      Back
                    </AnimatedButton>
                    <AnimatedButton
                      variant="contained"
                      color="primary"
                      onClick={handleNext}
                      disabled={
                        (activeStep === 0 && !userType) ||
                        loading ||
                        (activeStep === steps.length - 1 && (!hasScrolledToBottom || !hasAgreedToTerms))
                      }
                      sx={{
                        px: 4,
                        py: 1.2,
                        fontWeight: 600,
                        boxShadow: `0 4px 14px ${alpha(theme.palette.primary.main, 0.4)}`,
                        opacity: (activeStep === steps.length - 1 && (!hasScrolledToBottom || !hasAgreedToTerms)) ? 0.6 : 1,
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {loading ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        activeStep === steps.length - 1 ? 'Complete' : 'Next'
                      )}
                    </AnimatedButton>
                  </Box>
                )}

                {activeStep === 0 && !signupSuccess && (
                  <>
                    <OrDivider>
                      <Divider sx={{ flexGrow: 1 }} />
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          px: 2,
                          fontWeight: 500,
                        }}
                      >
                        OR
                      </Typography>
                      <Divider sx={{ flexGrow: 1 }} />
                    </OrDivider>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>
                      <motion.div
                        whileHover={{ y: -3 }}
                        whileTap={{ scale: 0.98 }}
                        style={{ width: '100%' }}
                      >
                        <SocialButton
                          fullWidth
                          startIcon={<GoogleIcon />}
                        >
                          Google
                        </SocialButton>
                      </motion.div>
                      <motion.div
                        whileHover={{ y: -3 }}
                        whileTap={{ scale: 0.98 }}
                        style={{ width: '100%' }}
                      >
                        <SocialButton
                          fullWidth
                          startIcon={<FacebookIcon />}
                        >
                          Facebook
                        </SocialButton>
                      </motion.div>
                      <motion.div
                        whileHover={{ y: -3 }}
                        whileTap={{ scale: 0.98 }}
                        style={{ width: '100%' }}
                      >
                        <SocialButton
                          fullWidth
                          startIcon={<AppleIcon />}
                        >
                          Apple
                        </SocialButton>
                      </motion.div>
                    </Box>
                  </>
                )}

                {!signupSuccess && (
                  <Box sx={{ mt: 3, textAlign: 'center' }}>
                    <Typography variant="body2">
                      Already have an account?{' '}
                      <Link href="/login" passHref>
                        <MuiLink
                          color="primary"
                          underline="hover"
                          sx={{
                            fontWeight: 600,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              textDecoration: 'none',
                              textShadow: `0 0 8px ${alpha(theme.palette.primary.main, 0.4)}`,
                            }
                          }}
                        >
                          Sign in
                        </MuiLink>
                      </Link>
                    </Typography>
                  </Box>
                )}
              </SignupPaper>
            </motion.div>
          </AnimatePresence>
        </Container>

        {/* Animation classes are imported from animations.css */}
      </SignupContainer>
    </Layout>
  );
};

export default Signup;
