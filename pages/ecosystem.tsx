import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import BusinessIcon from '@mui/icons-material/Business';
import CodeIcon from '@mui/icons-material/Code';
import NewspaperIcon from '@mui/icons-material/Newspaper';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import Link from 'next/link';
import Image from 'next/image';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(6),
  maxWidth: '800px',
}));

const EcosystemCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 70,
  height: 70,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& svg': {
    fontSize: 35,
    color: theme.palette.primary.main,
  },
}));

const PartnerCard = styled(Card)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const PartnerLogo = styled(Box)(({ theme }) => ({
  width: 120,
  height: 120,
  margin: '0 auto',
  marginBottom: theme.spacing(2),
  position: 'relative',
}));

// Ecosystem components
const ecosystemComponents = [
  {
    title: 'Game Developers',
    description: 'Join our platform to reach a wider audience, leverage our blockchain infrastructure, and monetize your games in new ways.',
    icon: <CodeIcon />,
    benefits: [
      'Access to our player base and marketing resources',
      'Integration with our blockchain infrastructure',
      'Revenue sharing and monetization opportunities',
      'Technical support and development resources',
    ],
    link: '/developers',
  },
  {
    title: 'Content Creators',
    description: 'Create content around our games and ecosystem to engage with our community and earn rewards.',
    icon: <NewspaperIcon />,
    benefits: [
      'Creator program with revenue sharing',
      'Early access to new games and features',
      'Collaboration opportunities with our team',
      'Promotion across our marketing channels',
    ],
    link: '/creators',
  },
  {
    title: 'Investors',
    description: 'Explore investment opportunities in our growing ecosystem and be part of the future of gaming.',
    icon: <MonetizationOnIcon />,
    benefits: [
      'Early access to investment rounds',
      'Detailed insights into our growth metrics',
      'Regular investor updates and reports',
      'Networking with other investors and partners',
    ],
    link: '/investors',
  },
  {
    title: 'Business Partners',
    description: 'Form strategic partnerships with us to create mutual value and expand our collective reach.',
    icon: <BusinessIcon />,
    benefits: [
      'Co-marketing opportunities',
      'Integration with our platform',
      'Access to our user base and analytics',
      'Joint product development',
    ],
    link: '/partners',
  },
];

// Partner logos
const partners = [
  { name: 'Partner 1', logo: '/partner1.png' },
  { name: 'Partner 2', logo: '/partner2.png' },
  { name: 'Partner 3', logo: '/partner3.png' },
  { name: 'Partner 4', logo: '/partner4.png' },
  { name: 'Partner 5', logo: '/partner5.png' },
];

const Ecosystem: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <Layout>
      <PageHeader
        title="Join Our"
        highlightedTitle="Ecosystem"
        description="Discover the various ways you can become part of the Gamestorme ecosystem and contribute to the future of gaming."
      />

      {/* Overview Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <SectionTitle variant="h3">Our Ecosystem</SectionTitle>
                  <Typography variant="body1" paragraph sx={{ mb: 3 }}>
                    The Gamestorme ecosystem is a vibrant network of game developers, content creators, players, investors, and business partners working together to revolutionize the gaming industry.
                  </Typography>
                  <Typography variant="body1" paragraph>
                    We believe in the power of collaboration and are committed to creating an environment where all participants can thrive and contribute to our shared vision of the future of gaming.
                  </Typography>
                  <Typography variant="body1" paragraph>
                    Whether you&apos;re a game developer looking to reach new audiences, a content creator seeking to engage with our community, an investor interested in the future of gaming, or a business looking for strategic partnerships, there&apos;s a place for you in our ecosystem.
                  </Typography>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Box sx={{ position: 'relative', width: '100%', height: 400, borderRadius: '20px', overflow: 'hidden' }}>
                    <Image
                      src="/assets/images/community/Gamestorme Ecosystem.jpg"
                      alt="Gamestorme Ecosystem"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </Box>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Ecosystem Components Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.5) }}>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Ways to Join</SectionTitle>
            <SectionSubtitle variant="h6">
              Explore the different ways you can become part of our ecosystem and contribute to our vision.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {ecosystemComponents.map((component, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <motion.div variants={itemVariants} style={{ height: '100%' }}>
                    <EcosystemCard>
                      <CardContent sx={{ p: 4 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <IconBox>{component.icon}</IconBox>
                          <Typography variant="h5" fontWeight="bold" sx={{ ml: 2 }}>
                            {component.title}
                          </Typography>
                        </Box>
                        <Typography variant="body1" color="text.secondary" paragraph>
                          {component.description}
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                          Benefits:
                        </Typography>
                        <List disablePadding>
                          {component.benefits.map((benefit, i) => (
                            <ListItem key={i} disablePadding sx={{ mb: 1 }}>
                              <ListItemIcon sx={{ minWidth: 36 }}>
                                <CheckCircleOutlineIcon color="primary" fontSize="small" />
                              </ListItemIcon>
                              <ListItemText
                                primary={benefit}
                                primaryTypographyProps={{ variant: 'body2' }}
                              />
                            </ListItem>
                          ))}
                        </List>
                        <Link href={component.link} passHref style={{ textDecoration: 'none', width: '100%' }}>
                          <Button
                            variant="contained"
                            color="primary"
                            endIcon={<ArrowForwardIcon />}
                            sx={{ mt: 3 }}
                            fullWidth
                          >
                            Learn More
                          </Button>
                        </Link>
                      </CardContent>
                    </EcosystemCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* News & Updates Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Latest News</SectionTitle>
            <SectionSubtitle variant="h6">
              Stay updated with the latest developments and opportunities in our ecosystem.
            </SectionSubtitle>

            <Grid container spacing={4}>
              <Grid item xs={12} md={4}>
                <motion.div variants={itemVariants}>
                  <EcosystemCard>
                    <Box sx={{ position: 'relative', width: '100%', height: 200 }}>
                      <Image
                        src="/assets/images/community/News 1.jpg"
                        alt="News 1"
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                    </Box>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="caption" color="primary.main" fontWeight="bold">
                        June 15, 2023
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        New Developer Program Launched
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        We&apos;re excited to announce our new developer program designed to support indie game developers...
                      </Typography>
                      <Link href="/news/developer-program" passHref style={{ textDecoration: 'none' }}>
                        <Button
                          variant="text"
                          color="primary"
                          endIcon={<ArrowForwardIcon />}
                        >
                          Read More
                        </Button>
                      </Link>
                    </CardContent>
                  </EcosystemCard>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={4}>
                <motion.div variants={itemVariants}>
                  <EcosystemCard>
                    <Box sx={{ position: 'relative', width: '100%', height: 200 }}>
                      <Image
                        src="/assets/images/community/News 2.jpg"
                        alt="News 2"
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                    </Box>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="caption" color="primary.main" fontWeight="bold">
                        May 28, 2023
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Investor Relations Update
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Our latest investor relations report shows strong growth in user acquisition and revenue...
                      </Typography>
                      <Link href="/news/investor-update" passHref style={{ textDecoration: 'none' }}>
                        <Button
                          variant="text"
                          color="primary"
                          endIcon={<ArrowForwardIcon />}
                        >
                          Read More
                        </Button>
                      </Link>
                    </CardContent>
                  </EcosystemCard>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={4}>
                <motion.div variants={itemVariants}>
                  <EcosystemCard>
                    <Box sx={{ position: 'relative', width: '100%', height: 200 }}>
                      <Image
                        src="/assets/images/community/News 3.jpg"
                        alt="News 3"
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                    </Box>
                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="caption" color="primary.main" fontWeight="bold">
                        May 10, 2023
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Strategic Partnership Announced
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        We&apos;re thrilled to announce our strategic partnership with a leading blockchain platform...
                      </Typography>
                      <Link href="/news/partnership" passHref style={{ textDecoration: 'none' }}>
                        <Button
                          variant="text"
                          color="primary"
                          endIcon={<ArrowForwardIcon />}
                        >
                          Read More
                        </Button>
                      </Link>
                    </CardContent>
                  </EcosystemCard>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Ecosystem;
