import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  useTheme,
  alpha,
  CircularProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import SendIcon from '@mui/icons-material/Send';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';
import { toast } from 'react-toastify';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const ContactCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const InfoCard = styled(Card)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 60,
  height: 60,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& svg': {
    fontSize: 30,
    color: theme.palette.primary.main,
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.primary.light,
    },
  },
}));

const Contact: React.FC = () => {
  const theme = useTheme();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState({
    name: false,
    email: false,
    subject: false,
    message: false,
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Contact info items
  const contactInfo = [
    {
      icon: <EmailIcon />,
      title: 'Email',
      content: '<EMAIL>',
    },
    {
      icon: <LocationOnIcon />,
      title: 'Address',
      content: '123 Gaming Street, Digital City, Metaverse',
    },
    {
      icon: <PhoneIcon />,
      title: 'Phone',
      content: '+****************',
    },
  ];

  // Validate email format
  const validateEmail = (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Form validation
    const errors = {
      name: !name,
      email: !email || !validateEmail(email),
      subject: !subject,
      message: !message,
    };

    setFormErrors(errors);

    if (Object.values(errors).some(error => error)) {
      toast.error('Please fill all required fields correctly.');
      return;
    }

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      toast.success('Your message has been sent successfully!');
      setLoading(false);
      
      // Reset form
      setName('');
      setEmail('');
      setSubject('');
      setMessage('');
    }, 1500);
  };

  return (
    <Layout>
      <PageHeader
        title="Contact"
        highlightedTitle="Us"
        description="Have questions or feedback? We'd love to hear from you. Get in touch with our team."
      />

      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={6}>
              {/* Contact Form */}
              <Grid item xs={12} md={7}>
                <motion.div variants={itemVariants}>
                  <ContactCard>
                    <CardContent sx={{ p: 4 }}>
                      <Typography variant="h4" fontWeight="bold" gutterBottom>
                        Send Us a Message
                      </Typography>
                      <Typography variant="body1" color="text.secondary" paragraph>
                        Fill out the form below and we'll get back to you as soon as possible.
                      </Typography>

                      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 4 }}>
                        <StyledTextField
                          fullWidth
                          label="Your Name"
                          variant="outlined"
                          value={name}
                          onChange={(e) => {
                            setName(e.target.value);
                            setFormErrors({ ...formErrors, name: false });
                          }}
                          error={formErrors.name}
                          helperText={formErrors.name ? 'Name is required' : ''}
                          required
                        />
                        <StyledTextField
                          fullWidth
                          label="Your Email"
                          variant="outlined"
                          type="email"
                          value={email}
                          onChange={(e) => {
                            setEmail(e.target.value);
                            setFormErrors({ ...formErrors, email: false });
                          }}
                          error={formErrors.email}
                          helperText={formErrors.email ? 'Valid email is required' : ''}
                          required
                        />
                        <StyledTextField
                          fullWidth
                          label="Subject"
                          variant="outlined"
                          value={subject}
                          onChange={(e) => {
                            setSubject(e.target.value);
                            setFormErrors({ ...formErrors, subject: false });
                          }}
                          error={formErrors.subject}
                          helperText={formErrors.subject ? 'Subject is required' : ''}
                          required
                        />
                        <StyledTextField
                          fullWidth
                          label="Message"
                          variant="outlined"
                          multiline
                          rows={5}
                          value={message}
                          onChange={(e) => {
                            setMessage(e.target.value);
                            setFormErrors({ ...formErrors, message: false });
                          }}
                          error={formErrors.message}
                          helperText={formErrors.message ? 'Message is required' : ''}
                          required
                        />
                        <Button
                          type="submit"
                          variant="contained"
                          color="primary"
                          size="large"
                          endIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                          disabled={loading}
                          sx={{ mt: 2 }}
                        >
                          {loading ? 'Sending...' : 'Send Message'}
                        </Button>
                      </Box>
                    </CardContent>
                  </ContactCard>
                </motion.div>
              </Grid>

              {/* Contact Info */}
              <Grid item xs={12} md={5}>
                <Grid container spacing={3} direction="column">
                  {contactInfo.map((info, index) => (
                    <Grid item key={index}>
                      <motion.div
                        variants={itemVariants}
                        transition={{ delay: 0.1 * index }}
                      >
                        <InfoCard>
                          <IconBox>{info.icon}</IconBox>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {info.title}
                          </Typography>
                          <Typography variant="body1" color="text.secondary">
                            {info.content}
                          </Typography>
                        </InfoCard>
                      </motion.div>
                    </Grid>
                  ))}

                  {/* Map or Additional Info */}
                  <Grid item sx={{ mt: 3 }}>
                    <motion.div variants={itemVariants}>
                      <ContactCard>
                        <Box
                          sx={{
                            height: 200,
                            width: '100%',
                            backgroundColor: alpha(theme.palette.primary.main, 0.1),
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="body1" color="text.secondary">
                            Map will be displayed here
                          </Typography>
                        </Box>
                      </ContactCard>
                    </motion.div>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Contact;
