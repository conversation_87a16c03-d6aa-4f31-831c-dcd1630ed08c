import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  useTheme,
  alpha,
  TextField,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Image from 'next/image';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 60,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(6),
  maxWidth: '800px',
}));

const JobCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const JobTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const JobDetail = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(1),
  color: theme.palette.text.secondary,
  '& svg': {
    marginRight: theme.spacing(1),
    fontSize: '1rem',
  },
}));

const BenefitCard = styled(Card)(({ theme }) => ({
  height: '100%',
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 60,
  height: 60,
  borderRadius: '50%',
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& svg': {
    fontSize: 30,
    color: theme.palette.primary.main,
  },
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: `${theme.shape.borderRadius}px !important`,
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  '&.Mui-expanded': {
    margin: theme.spacing(1, 0),
  },
  marginBottom: theme.spacing(2),
  overflow: 'hidden',
}));

// Sample job listings
const jobs = [
  {
    id: 1,
    title: 'Senior Game Developer',
    department: 'Engineering',
    location: 'Remote',
    type: 'Full-time',
    description: 'We are looking for an experienced game developer to join our team and help create innovative gaming experiences.',
    requirements: [
      'Minimum 5 years of experience in game development',
      'Proficiency in Unity or Unreal Engine',
      'Experience with blockchain technology is a plus',
      'Strong problem-solving skills and attention to detail',
      'Excellent communication and teamwork abilities',
    ],
  },
  {
    id: 2,
    title: 'Blockchain Engineer',
    department: 'Engineering',
    location: 'Remote',
    type: 'Full-time',
    description: 'Join our blockchain team to develop and maintain the infrastructure that powers our gaming ecosystem.',
    requirements: [
      'Experience with Ethereum, Solidity, and smart contract development',
      'Understanding of blockchain consensus mechanisms',
      'Knowledge of web3.js or ethers.js',
      'Experience with NFT standards (ERC-721, ERC-1155)',
      'Background in secure coding practices',
    ],
  },
  {
    id: 3,
    title: 'UI/UX Designer',
    department: 'Design',
    location: 'Remote',
    type: 'Full-time',
    description: 'Create intuitive and engaging user interfaces for our games and platform.',
    requirements: [
      'Portfolio demonstrating UI/UX design for games or interactive applications',
      'Proficiency in design tools such as Figma, Adobe XD, or Sketch',
      'Understanding of game design principles',
      'Experience with responsive design',
      'Ability to work closely with developers to implement designs',
    ],
  },
  {
    id: 4,
    title: 'Community Manager',
    department: 'Marketing',
    location: 'Remote',
    type: 'Full-time',
    description: 'Build and nurture our community across various platforms and help grow our user base.',
    requirements: [
      'Experience managing online communities in gaming or blockchain',
      'Strong communication and interpersonal skills',
      'Knowledge of social media platforms and community tools',
      'Ability to create engaging content',
      'Understanding of analytics and reporting',
    ],
  },
];

// Company benefits
const benefits = [
  {
    title: 'Remote-First Culture',
    description: 'Work from anywhere in the world with our fully remote setup.',
    icon: <LocationOnIcon />,
  },
  {
    title: 'Flexible Hours',
    description: 'Set your own schedule and work when you\'re most productive.',
    icon: <AccessTimeIcon />,
  },
  {
    title: 'Competitive Salary',
    description: 'We offer industry-leading compensation packages.',
    icon: <WorkIcon />,
  },
  {
    title: 'Professional Growth',
    description: 'Continuous learning opportunities and career advancement.',
    icon: <WorkIcon />,
  },
];

// FAQs
const faqs = [
  {
    question: 'What is the hiring process like?',
    answer: 'Our hiring process typically includes an initial application review, a screening call, a technical or skills assessment, and final interviews with the team. The entire process usually takes 2-3 weeks.',
  },
  {
    question: 'Do you offer relocation assistance?',
    answer: 'As a remote-first company, we don\'t typically offer relocation assistance. However, we provide all the equipment and resources you need to set up an effective home office.',
  },
  {
    question: 'What is the company culture like?',
    answer: 'We foster a culture of innovation, collaboration, and continuous learning. We value work-life balance and believe in empowering our team members to take ownership of their work.',
  },
  {
    question: 'Do you offer internships or entry-level positions?',
    answer: 'Yes, we offer internships and entry-level positions throughout the year. These opportunities are posted on our careers page when available.',
  },
];

const Careers: React.FC = () => {
  const theme = useTheme();
  const [department, setDepartment] = useState('All');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Filter jobs by department
  const filteredJobs = department === 'All'
    ? jobs
    : jobs.filter(job => job.department === department);

  // Unique departments for filter
  const departments = ['All', ...Array.from(new Set(jobs.map(job => job.department)))];

  return (
    <Layout>
      <PageHeader
        title="Join Our"
        highlightedTitle="Team"
        description="Explore career opportunities at Gamestorme and help us revolutionize the gaming industry."
      />

      {/* Company Culture Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <SectionTitle variant="h3">Our Culture</SectionTitle>
                  <Typography variant="body1" paragraph sx={{ mb: 3 }}>
                    At Gamestorme, we&apos;re building the future of gaming. Our team is passionate about creating innovative experiences that bridge the gap between traditional gaming and blockchain technology.
                  </Typography>
                  <Typography variant="body1" paragraph>
                    We believe in fostering a collaborative environment where creativity thrives and every team member has the opportunity to make a significant impact. As a remote-first company, we value flexibility, autonomy, and results.
                  </Typography>
                  <Typography variant="body1" paragraph>
                    If you&apos;re excited about pushing the boundaries of what&apos;s possible in gaming and want to be part of a dynamic, forward-thinking team, we&apos;d love to hear from you.
                  </Typography>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Box sx={{ position: 'relative', width: '100%', height: 400, borderRadius: '20px', overflow: 'hidden' }}>
                    <Image
                      src="/assets/images/careers/Gamestorme Careers.jpg"
                      alt="Gamestorme Team Culture"
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </Box>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Benefits Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.5) }}>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Benefits & Perks</SectionTitle>
            <SectionSubtitle variant="h6">
              We offer a range of benefits designed to support your well-being and professional growth.
            </SectionSubtitle>

            <Grid container spacing={4}>
              {benefits.map((benefit, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div variants={itemVariants}>
                    <BenefitCard>
                      <IconBox>{benefit.icon}</IconBox>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {benefit.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {benefit.description}
                      </Typography>
                    </BenefitCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* Open Positions Section */}
      <SectionContainer>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Open Positions</SectionTitle>
            <SectionSubtitle variant="h6">
              Explore our current job openings and find your next career opportunity.
            </SectionSubtitle>

            {/* Department Filter */}
            <Box sx={{ mb: 4, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {departments.map((dept) => (
                <Chip
                  key={dept}
                  label={dept}
                  onClick={() => setDepartment(dept)}
                  color={department === dept ? 'primary' : 'default'}
                  sx={{
                    fontWeight: department === dept ? 600 : 400,
                    '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.1) }
                  }}
                />
              ))}
            </Box>

            <Grid container spacing={4}>
              {filteredJobs.map((job) => (
                <Grid item xs={12} md={6} key={job.id}>
                  <motion.div variants={itemVariants}>
                    <JobCard>
                      <CardContent sx={{ p: 3 }}>
                        <Chip
                          label={job.department}
                          size="small"
                          color="primary"
                          sx={{ mb: 2 }}
                        />
                        <JobTitle variant="h5">{job.title}</JobTitle>
                        <Box sx={{ mb: 2 }}>
                          <JobDetail>
                            <LocationOnIcon />
                            <Typography variant="body2">{job.location}</Typography>
                          </JobDetail>
                          <JobDetail>
                            <AccessTimeIcon />
                            <Typography variant="body2">{job.type}</Typography>
                          </JobDetail>
                        </Box>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {job.description}
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                          Requirements:
                        </Typography>
                        <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                          {job.requirements.map((req, index) => (
                            <Typography component="li" variant="body2" color="text.secondary" key={index} sx={{ mb: 0.5 }}>
                              {req}
                            </Typography>
                          ))}
                        </Box>
                        <Button
                          variant="contained"
                          color="primary"
                          endIcon={<ArrowForwardIcon />}
                          fullWidth
                        >
                          Apply Now
                        </Button>
                      </CardContent>
                    </JobCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </SectionContainer>

      {/* FAQs Section */}
      <SectionContainer sx={{ backgroundColor: alpha(theme.palette.background.paper, 0.5) }}>
        <Container maxWidth="md">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <SectionTitle variant="h3">Frequently Asked Questions</SectionTitle>
            <SectionSubtitle variant="h6">
              Find answers to common questions about working at Gamestorme.
            </SectionSubtitle>

            <Box>
              {faqs.map((faq, index) => (
                <motion.div key={index} variants={itemVariants}>
                  <StyledAccordion>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls={`panel${index}a-content`}
                      id={`panel${index}a-header`}
                    >
                      <Typography variant="h6" fontWeight="600">
                        {faq.question}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body1" color="text.secondary">
                        {faq.answer}
                      </Typography>
                    </AccordionDetails>
                  </StyledAccordion>
                </motion.div>
              ))}
            </Box>
          </motion.div>
        </Container>
      </SectionContainer>
    </Layout>
  );
};

export default Careers;
