import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
  Chip,
  useMediaQuery,
  <PERSON>er,
  <PERSON><PERSON>B<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';

// Firebase imports
import { firestore } from '../lib/firebase';
import { 
  collection, 
  query, 
  orderBy, 
  onSnapshot, 
} from 'firebase/firestore';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import MenuIcon from '@mui/icons-material/Menu';
import RefreshIcon from '@mui/icons-material/Refresh';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
}));

const MainContent = styled(Box)(({ theme }) => ({
  marginLeft: 280,
  padding: theme.spacing(3),
  [theme.breakpoints.down('md')]: {
    marginLeft: 0,
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const AdminDashboardSimple: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [games, setGames] = useState<any[]>([]);
  const [supportTickets, setSupportTickets] = useState<any[]>([]);
  const [connectionStatus, setConnectionStatus] = useState('Connecting...');

  // Test Firebase connection
  useEffect(() => {
    console.log('🔥 Testing Firebase connection...');
    
    try {
      // Test games collection
      const gamesQuery = query(
        collection(firestore, 'games'),
        orderBy('createdAt', 'desc')
      );

      const unsubscribeGames = onSnapshot(gamesQuery, (snapshot) => {
        console.log(`📊 Received ${snapshot.docs.length} games from Firebase`);
        const gamesData: any[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          console.log(`🎮 Game: ${data.title || 'Unknown'} - Status: ${data.status || 'Unknown'}`);
          gamesData.push({
            id: doc.id,
            ...data,
          });
        });
        setGames(gamesData);
        setConnectionStatus(`Connected - ${gamesData.length} games found`);
      }, (error) => {
        console.error('❌ Error fetching games:', error);
        setConnectionStatus(`Error: ${error.message}`);
      });

      // Test support tickets collection
      const ticketsQuery = query(
        collection(firestore, 'supportTickets'),
        orderBy('createdAt', 'desc')
      );

      const unsubscribeTickets = onSnapshot(ticketsQuery, (snapshot) => {
        console.log(`📊 Received ${snapshot.docs.length} support tickets from Firebase`);
        const ticketsData: any[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          ticketsData.push({
            id: doc.id,
            ...data,
          });
        });
        setSupportTickets(ticketsData);
      }, (error) => {
        console.error('❌ Error fetching support tickets:', error);
      });

      return () => {
        console.log('🔄 Cleaning up Firebase listeners...');
        unsubscribeGames();
        unsubscribeTickets();
      };
    } catch (error) {
      console.error('❌ Firebase connection error:', error);
      setConnectionStatus(`Connection Error: ${error}`);
    }
  }, []);

  const sidebarItems = [
    { label: 'Overview', icon: <DashboardIcon />, index: 0 },
    { label: 'Game Management', icon: <SportsEsportsIcon />, index: 1 },
    { label: 'Support Tickets', icon: <SupportAgentIcon />, index: 2 },
  ];

  return (
    <DashboardContainer>
      {/* Mobile Header */}
      {isMobile && (
        <AppBar position="fixed" sx={{ bgcolor: 'background.paper', color: 'text.primary' }}>
          <Toolbar>
            <Button onClick={() => setSidebarOpen(true)}>
              <MenuIcon />
            </Button>
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
              Admin Dashboard (Simple)
            </Typography>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            background: `linear-gradient(180deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
            backdropFilter: 'blur(20px)',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" fontWeight="bold" color="primary.main" gutterBottom>
            Gamestorme
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Admin Dashboard (Simple)
          </Typography>
        </Box>

        <Divider />

        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{ mr: 2, bgcolor: 'error.main' }}>
              A
            </Avatar>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                Admin
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Administrator
              </Typography>
            </Box>
          </Box>
        </Box>

        <Divider />

        <List sx={{ px: 2, py: 1 }}>
          {sidebarItems.map((item) => (
            <ListItem
              key={item.index}
              button
              selected={activeTab === item.index}
              onClick={() => {
                setActiveTab(item.index);
                if (isMobile) setSidebarOpen(false);
              }}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&.Mui-selected': {
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                },
              }}
            >
              <ListItemIcon sx={{ color: activeTab === item.index ? 'primary.main' : 'text.secondary' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: activeTab === item.index ? 'bold' : 'normal',
                  color: activeTab === item.index ? 'primary.main' : 'text.primary',
                }}
              />
              {/* Show badges for data */}
              {item.index === 1 && games.length > 0 && (
                <Badge badgeContent={games.length} color="primary" />
              )}
              {item.index === 2 && supportTickets.length > 0 && (
                <Badge badgeContent={supportTickets.length} color="secondary" />
              )}
            </ListItem>
          ))}
        </List>
      </Drawer>

      {/* Main Content */}
      <MainContent sx={{ mt: isMobile ? 8 : 0 }}>
        <Container maxWidth="xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4" fontWeight="bold">
                Admin Dashboard - Simple Test
              </Typography>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => window.location.reload()}
              >
                Refresh
              </Button>
            </Box>

            {/* Connection Status */}
            <Alert 
              severity={connectionStatus.includes('Error') ? 'error' : connectionStatus.includes('Connected') ? 'success' : 'info'} 
              sx={{ mb: 3 }}
            >
              <Typography variant="body2">
                🔥 Firebase Status: {connectionStatus}
              </Typography>
            </Alert>

            {/* Key Metrics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={4}>
                <MetricCard>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <SportsEsportsIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {games.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Games
                    </Typography>
                    <Chip 
                      label={`${games.filter(g => g.status === 'pending').length} Pending`} 
                      size="small" 
                      color="warning" 
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </MetricCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MetricCard>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <SupportAgentIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {supportTickets.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Support Tickets
                    </Typography>
                    <Chip 
                      label={`${supportTickets.filter(t => t.status === 'open').length} Open`} 
                      size="small" 
                      color="error" 
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </MetricCard>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MetricCard>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <DashboardIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                    <Typography variant="h4" fontWeight="bold">
                      {new Set(games.map(g => g.developer?.uid)).size}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Developers
                    </Typography>
                  </CardContent>
                </MetricCard>
              </Grid>
            </Grid>

            {/* Data Display */}
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <MetricCard>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Recent Games ({games.length})
                    </Typography>
                    <List>
                      {games.slice(0, 5).map((game, index) => (
                        <ListItem key={game.id || index} disablePadding>
                          <ListItemText
                            primary={game.title || 'Unknown Game'}
                            secondary={`Status: ${game.status || 'Unknown'} • Developer: ${game.developer?.name || 'Unknown'}`}
                          />
                          <Chip
                            label={game.status || 'Unknown'}
                            size="small"
                            color={
                              game.status === 'approved' ? 'success' :
                              game.status === 'pending' ? 'warning' : 'default'
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                    {games.length === 0 && (
                      <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                        No games found in Firebase
                      </Typography>
                    )}
                  </CardContent>
                </MetricCard>
              </Grid>
              <Grid item xs={12} md={6}>
                <MetricCard>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Support Tickets ({supportTickets.length})
                    </Typography>
                    <List>
                      {supportTickets.slice(0, 5).map((ticket, index) => (
                        <ListItem key={ticket.id || index} disablePadding>
                          <ListItemText
                            primary={ticket.title || 'Unknown Ticket'}
                            secondary={`Status: ${ticket.status || 'Unknown'} • Priority: ${ticket.priority || 'Unknown'}`}
                          />
                          <Chip
                            label={ticket.status || 'Unknown'}
                            size="small"
                            color={
                              ticket.status === 'resolved' ? 'success' :
                              ticket.status === 'open' ? 'error' : 'default'
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                    {supportTickets.length === 0 && (
                      <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                        No support tickets found in Firebase
                      </Typography>
                    )}
                  </CardContent>
                </MetricCard>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </MainContent>
    </DashboardContainer>
  );
};

export default AdminDashboardSimple;
