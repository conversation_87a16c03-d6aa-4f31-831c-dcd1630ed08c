import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Tab,
  Tabs,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import PageHeader from '../components/shared/PageHeader';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import BusinessIcon from '@mui/icons-material/Business';
import IntegrationInstructionsIcon from '@mui/icons-material/IntegrationInstructions';
import PublicIcon from '@mui/icons-material/Public';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SecurityIcon from '@mui/icons-material/Security';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import ApiIcon from '@mui/icons-material/Api';
import CloudIcon from '@mui/icons-material/Cloud';
import PaymentIcon from '@mui/icons-material/Payment';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import GroupsIcon from '@mui/icons-material/Groups';
import HandshakeIcon from '@mui/icons-material/Handshake';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import Link from 'next/link';

const PartnersContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}));

const PartnerCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`,
  },
}));

const CTACard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: 'white',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(4),
  textAlign: 'center',
}));

const IconBox = styled(Box)(({ theme }) => ({
  width: 64,
  height: 64,
  borderRadius: theme.spacing(1),
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  marginBottom: theme.spacing(2),
}));

const partnershipTypes = [
  {
    title: 'Technology Integration',
    description: 'Integrate your technology stack with our platform to enhance the gaming experience.',
    icon: <IntegrationInstructionsIcon />,
    benefits: [
      'API access and documentation',
      'Technical support and guidance',
      'Co-marketing opportunities',
      'Revenue sharing models',
    ],
    examples: [
      'Payment processors',
      'Cloud infrastructure providers',
      'Analytics platforms',
      'Security solutions',
    ],
  },
  {
    title: 'Distribution Partners',
    description: 'Expand your reach by distributing games through our global network.',
    icon: <PublicIcon />,
    benefits: [
      'Global market access',
      'Localization support',
      'Marketing collaboration',
      'Performance analytics',
    ],
    examples: [
      'Regional game stores',
      'Mobile app stores',
      'Console platforms',
      'Streaming services',
    ],
  },
  {
    title: 'Strategic Alliances',
    description: 'Form strategic partnerships to drive mutual growth and innovation.',
    icon: <HandshakeIcon />,
    benefits: [
      'Joint product development',
      'Shared resources and expertise',
      'Market expansion opportunities',
      'Brand collaboration',
    ],
    examples: [
      'Gaming hardware manufacturers',
      'Educational institutions',
      'Entertainment companies',
      'Technology giants',
    ],
  },
];

const currentPartners = [
  {
    name: 'Unity Technologies',
    type: 'Technology',
    description: 'Game engine integration and developer tools',
    logo: '/partners/unity.png',
    status: 'Active',
  },
  {
    name: 'Stripe',
    type: 'Payment',
    description: 'Global payment processing and financial services',
    logo: '/partners/stripe.png',
    status: 'Active',
  },
  {
    name: 'AWS',
    type: 'Infrastructure',
    description: 'Cloud hosting and scalable infrastructure',
    logo: '/partners/aws.png',
    status: 'Active',
  },
  {
    name: 'Discord',
    type: 'Community',
    description: 'Community building and social features',
    logo: '/partners/discord.png',
    status: 'Active',
  },
  {
    name: 'Twitch',
    type: 'Streaming',
    description: 'Live streaming and content creation',
    logo: '/partners/twitch.png',
    status: 'Active',
  },
  {
    name: 'Google Analytics',
    type: 'Analytics',
    description: 'Advanced analytics and insights',
    logo: '/partners/google.png',
    status: 'Active',
  },
];

const partnerBenefits = [
  {
    title: 'Market Access',
    description: 'Reach millions of gamers and developers worldwide',
    icon: <TrendingUpIcon />,
  },
  {
    title: 'Technical Support',
    description: '24/7 technical support and dedicated account management',
    icon: <SupportAgentIcon />,
  },
  {
    title: 'Revenue Opportunities',
    description: 'Multiple revenue streams and flexible partnership models',
    icon: <PaymentIcon />,
  },
  {
    title: 'Innovation Platform',
    description: 'Access to cutting-edge gaming technologies and trends',
    icon: <RocketLaunchIcon />,
  },
];

const BusinessPartners: React.FC = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [partnershipDialogOpen, setPartnershipDialogOpen] = useState(false);
  const [selectedPartnershipType, setSelectedPartnershipType] = useState<string>('');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  const handlePartnershipInquiry = () => {
    console.log('Partnership inquiry for:', selectedPartnershipType);
    setPartnershipDialogOpen(false);
  };

  return (
    <Layout>
      <PageHeader
        title="Business"
        highlightedTitle="Partners"
        description="Join our ecosystem of innovative partners and unlock new opportunities in the gaming industry through strategic collaborations."
      />

      <PartnersContainer>
        <Container maxWidth="lg">
          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
            <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
              <Tab label="Partnership Types" />
              <Tab label="Current Partners" />
              <Tab label="Benefits" />
              <Tab label="Apply" />
            </Tabs>
          </Box>

          {/* Partnership Types Tab */}
          {activeTab === 0 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Grid container spacing={6} alignItems="center" sx={{ mb: 8 }}>
                <Grid item xs={12} md={6}>
                  <motion.div variants={itemVariants}>
                    <Typography variant="h3" fontWeight="bold" gutterBottom>
                      Strategic Partnerships
                    </Typography>
                    <Typography variant="body1" paragraph sx={{ mb: 3, fontSize: '1.1rem' }}>
                      Partner with Gamestorme to tap into the rapidly growing gaming market. 
                      Our platform connects millions of gamers with innovative developers worldwide.
                    </Typography>
                    <Typography variant="body1" paragraph sx={{ mb: 4, fontSize: '1.1rem' }}>
                      Whether you're a technology provider, distributor, or strategic ally, 
                      we offer flexible partnership models designed to drive mutual success.
                    </Typography>
                    <Button
                      variant="contained"
                      size="large"
                      endIcon={<ArrowForwardIcon />}
                      onClick={() => setActiveTab(3)}
                    >
                      Become a Partner
                    </Button>
                  </motion.div>
                </Grid>
                <Grid item xs={12} md={6}>
                  <motion.div variants={itemVariants}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 4,
                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                        borderRadius: 2,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      }}
                    >
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        Partnership Stats
                      </Typography>
                      <List>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <BusinessIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="50+ Active Partners" secondary="Across technology, distribution, and strategic categories" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <TrendingUpIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="$2.5B+ Partner Revenue" secondary="Generated through our partnership ecosystem" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <PublicIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="Global Reach" secondary="Partners in 40+ countries worldwide" />
                        </ListItem>
                        <ListItem disablePadding>
                          <ListItemIcon>
                            <GroupsIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary="Dedicated Support" secondary="Dedicated partner success team" />
                        </ListItem>
                      </List>
                    </Paper>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Partnership Types Grid */}
              <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom sx={{ mb: 6 }}>
                Partnership Opportunities
              </Typography>
              <Grid container spacing={4} sx={{ mb: 8 }}>
                {partnershipTypes.map((partnership, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <motion.div variants={itemVariants} style={{ height: '100%' }}>
                      <PartnerCard>
                        <CardContent sx={{ p: 3 }}>
                          <IconBox>{partnership.icon}</IconBox>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {partnership.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" paragraph>
                            {partnership.description}
                          </Typography>
                          
                          <Typography variant="subtitle2" fontWeight="bold" gutterBottom sx={{ mt: 3 }}>
                            Benefits:
                          </Typography>
                          <List dense>
                            {partnership.benefits.map((benefit, i) => (
                              <ListItem key={i} disablePadding sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 32 }}>
                                  <CheckCircleOutlineIcon color="primary" sx={{ fontSize: 20 }} />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={benefit} 
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>

                          <Typography variant="subtitle2" fontWeight="bold" gutterBottom sx={{ mt: 2 }}>
                            Examples:
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                            {partnership.examples.map((example, i) => (
                              <Chip key={i} label={example} size="small" variant="outlined" />
                            ))}
                          </Box>

                          <Button
                            fullWidth
                            variant="outlined"
                            sx={{ mt: 2 }}
                            onClick={() => {
                              setSelectedPartnershipType(partnership.title);
                              setPartnershipDialogOpen(true);
                            }}
                          >
                            Learn More
                          </Button>
                        </CardContent>
                      </PartnerCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Current Partners Tab */}
          {activeTab === 1 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Our Trusted Partners
              </Typography>
              <Grid container spacing={4}>
                {currentPartners.map((partner, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <motion.div variants={itemVariants}>
                      <PartnerCard>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <Avatar
                            src={partner.logo}
                            sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}
                          />
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {partner.name}
                          </Typography>
                          <Chip 
                            label={partner.type} 
                            color="primary" 
                            size="small" 
                            sx={{ mb: 2 }}
                          />
                          <Typography variant="body2" color="text.secondary" paragraph>
                            {partner.description}
                          </Typography>
                          <Chip 
                            label={partner.status} 
                            color="success" 
                            size="small"
                          />
                        </CardContent>
                      </PartnerCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}

          {/* Benefits Tab */}
          {activeTab === 2 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 4 }}>
                Partnership Benefits
              </Typography>
              <Grid container spacing={4} sx={{ mb: 6 }}>
                {partnerBenefits.map((benefit, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <motion.div variants={itemVariants}>
                      <PartnerCard>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <IconBox sx={{ mx: 'auto' }}>
                            {benefit.icon}
                          </IconBox>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {benefit.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {benefit.description}
                          </Typography>
                        </CardContent>
                      </PartnerCard>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>

              {/* Detailed Benefits */}
              <motion.div variants={itemVariants}>
                <PartnerCard>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Why Partner with Gamestorme?
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <List>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <ApiIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Robust API Platform"
                              secondary="Comprehensive APIs for seamless integration"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <CloudIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Scalable Infrastructure"
                              secondary="Handle millions of users with enterprise-grade reliability"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <AnalyticsIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Advanced Analytics"
                              secondary="Deep insights into user behavior and market trends"
                            />
                          </ListItem>
                        </List>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <List>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <SecurityIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Enterprise Security"
                              secondary="Bank-level security and compliance standards"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <SupportAgentIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Dedicated Support"
                              secondary="24/7 technical support and account management"
                            />
                          </ListItem>
                          <ListItem disablePadding>
                            <ListItemIcon>
                              <PaymentIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary="Flexible Revenue Models"
                              secondary="Multiple monetization options and revenue sharing"
                            />
                          </ListItem>
                        </List>
                      </Grid>
                    </Grid>
                  </CardContent>
                </PartnerCard>
              </motion.div>
            </motion.div>
          )}

          {/* Apply Tab */}
          {activeTab === 3 && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <CTACard>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  Ready to Partner with Us?
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                  Join our ecosystem of innovative partners and unlock new growth opportunities.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: '#ffffff',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.9),
                      },
                    }}
                    onClick={() => setPartnershipDialogOpen(true)}
                  >
                    Start Partnership
                  </Button>
                  <Link href="/contact" passHref style={{ textDecoration: 'none' }}>
                    <Button
                      variant="outlined"
                      size="large"
                      sx={{
                        borderColor: '#ffffff',
                        color: '#ffffff',
                        '&:hover': {
                          borderColor: '#ffffff',
                          bgcolor: alpha('#ffffff', 0.1),
                        },
                      }}
                    >
                      Contact Sales
                    </Button>
                  </Link>
                </Box>
              </CTACard>
            </motion.div>
          )}
        </Container>
      </PartnersContainer>

      {/* Partnership Inquiry Dialog */}
      <Dialog open={partnershipDialogOpen} onClose={() => setPartnershipDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Partnership Inquiry</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Partnership Type</InputLabel>
                <Select
                  value={selectedPartnershipType}
                  onChange={(e) => setSelectedPartnershipType(e.target.value)}
                  label="Partnership Type"
                >
                  {partnershipTypes.map((type) => (
                    <MenuItem key={type.title} value={type.title}>
                      {type.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Company Name" variant="outlined" required />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Contact Person" variant="outlined" required />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Email" variant="outlined" type="email" required />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Phone" variant="outlined" />
            </Grid>
            <Grid item xs={12}>
              <TextField fullWidth label="Company Website" variant="outlined" />
            </Grid>
            <Grid item xs={12}>
              <TextField fullWidth label="Company Size" variant="outlined" />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Partnership Proposal"
                variant="outlined"
                multiline
                rows={4}
                placeholder="Please describe your partnership proposal and how you envision working together..."
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Expected Timeline"
                variant="outlined"
                placeholder="When would you like to start the partnership?"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPartnershipDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handlePartnershipInquiry}>
            Submit Inquiry
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default BusinessPartners;
