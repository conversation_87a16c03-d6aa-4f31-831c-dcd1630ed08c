# 🔥 Firebase Setup Guide

This guide will help you configure Firebase with the correct security rules and permissions for the Gamestorme platform.

## 🚨 **IMPORTANT: Fix Upload Permissions**

If you're getting "Missing or insufficient permissions" errors when uploading, follow these steps:

### **Step 1: Apply Firestore Security Rules**

1. Go to your [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `gamestorme-faf42`
3. Navigate to **Firestore Database** → **Rules**
4. Replace the existing rules with the content from `firebase-firestore-rules.txt`
5. Click **Publish**

### **Step 2: Apply Storage Security Rules**

1. In Firebase Console, navigate to **Storage** → **Rules**
2. Replace the existing rules with the content from `firebase-storage-rules.txt`
3. Click **Publish**

### **Step 3: Apply Realtime Database Rules**

1. In Firebase Console, navigate to **Realtime Database** → **Rules**
2. Replace the existing rules with the content from `firebase-realtime-rules.txt`
3. Click **Publish**

## 📋 **Complete Firebase Configuration**

### **Your Project Details**
```
Project ID: gamestorme-faf42
API Key: AIzaSyDHz0X-6x78FuzQyGgIeKRy1nfNMw1-_F4
Auth Domain: gamestorme-faf42.firebaseapp.com
Storage Bucket: gamestorme-faf42.firebasestorage.app
Database URL: https://gamestorme-faf42-default-rtdb.firebaseio.com
```

### **Required Services**
Make sure these Firebase services are enabled:

1. **Authentication**
   - Email/Password provider
   - Google provider (optional)
   - Anonymous authentication (for analytics)

2. **Firestore Database**
   - Native mode
   - Multi-region (for better performance)

3. **Realtime Database**
   - For live analytics and real-time features

4. **Storage**
   - For game files, images, and user uploads

5. **Analytics**
   - For user behavior tracking

6. **Cloud Functions** (optional)
   - For server-side operations

## 🔐 **Security Rules Explanation**

### **Firestore Rules Features**
- ✅ **Public game reading** for approved games
- ✅ **Developer ownership** - developers can only edit their own games
- ✅ **Admin privileges** for platform management
- ✅ **Support ticket system** with proper access control
- ✅ **Analytics tracking** with anonymous support
- ✅ **User notifications** with privacy protection

### **Storage Rules Features**
- ✅ **File size limits** to prevent abuse
- ✅ **Content type validation** for security
- ✅ **Developer uploads** with proper ownership
- ✅ **Public asset access** for game media
- ✅ **Temporary uploads** with automatic cleanup

### **Key Permissions**
```javascript
// Developers can upload games
allow write: if request.auth != null && 
             request.auth.uid == developerId &&
             request.resource.size < 500 * 1024 * 1024; // 500MB limit

// Public can read approved games
allow read: if resource.data.status == 'approved';

// Analytics tracking (anonymous allowed)
allow create: if true;
```

## 🛠 **Troubleshooting Upload Issues**

### **Common Error: "Missing or insufficient permissions"**

**Cause**: Firebase security rules are too restrictive or not properly configured.

**Solution**:
1. Apply the updated security rules from the rule files
2. Make sure you're authenticated when uploading
3. Check that file sizes are within limits
4. Verify file types are allowed

### **File Size Limits**
- **Game files**: 500MB maximum
- **Images**: 10MB maximum
- **Avatars**: 5MB maximum
- **Support attachments**: 25MB maximum

### **Allowed File Types**
- **Games**: `.zip`, `.rar`, `.exe`, `.dmg`, `.app`
- **Images**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`
- **Documents**: `.pdf`, `.txt`, `.doc`, `.docx`

## 🔧 **Environment Setup**

### **1. Update .env.local**
```env
# Your actual Firebase credentials
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDHz0X-6x78FuzQyGgIeKRy1nfNMw1-_F4
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=gamestorme-faf42.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=gamestorme-faf42
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=gamestorme-faf42.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:01704242f816095f4711f7
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://gamestorme-faf42-default-rtdb.firebaseio.com
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-CBM026VFVM

# Firebase Admin (for server-side operations)
FIREBASE_ADMIN_PROJECT_ID=gamestorme-faf42
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
```

### **2. Get Service Account Key**
1. Go to Firebase Console → Project Settings → Service Accounts
2. Click "Generate new private key"
3. Download the JSON file
4. Extract the `client_email` and `private_key` values
5. Add them to your `.env.local` file

## 🚀 **Testing the Setup**

### **1. Test Authentication**
```bash
# Start the development server
npm run dev

# Navigate to the developer dashboard
# Try signing up/logging in
```

### **2. Test File Upload**
```bash
# Go to developer dashboard
# Try uploading a game file
# Check for permission errors
```

### **3. Test Analytics**
```bash
# Navigate around the site
# Check Firebase Console → Analytics for events
# Verify real-time data is flowing
```

## 📊 **Firebase Console Checklist**

### **Authentication**
- [ ] Email/Password provider enabled
- [ ] Test user accounts created
- [ ] Custom claims configured (for admin users)

### **Firestore Database**
- [ ] Database created in native mode
- [ ] Security rules applied from `firebase-firestore-rules.txt`
- [ ] Test collections created (games, users, developers)

### **Storage**
- [ ] Storage bucket created
- [ ] Security rules applied from `firebase-storage-rules.txt`
- [ ] CORS configured for web access

### **Realtime Database**
- [ ] Database created
- [ ] Security rules applied from `firebase-realtime-rules.txt`
- [ ] Test data structure created

### **Analytics**
- [ ] Google Analytics enabled
- [ ] Data streams configured
- [ ] Custom events set up

## 🔍 **Monitoring and Debugging**

### **Firebase Console Monitoring**
- **Authentication**: Monitor user sign-ups and logins
- **Firestore**: Check read/write operations and errors
- **Storage**: Monitor file uploads and downloads
- **Analytics**: Track user behavior and events

### **Debug Mode**
Add to your `.env.local` for debugging:
```env
NEXT_PUBLIC_DEBUG=true
FIREBASE_DEBUG=true
```

### **Common Issues**
1. **CORS errors**: Configure Storage CORS settings
2. **Auth errors**: Check authentication provider settings
3. **Permission errors**: Verify security rules are applied
4. **Size errors**: Check file size limits in rules

## 📞 **Support**

If you're still having issues:

1. **Check Firebase Console logs** for detailed error messages
2. **Verify all rules are published** and not in draft mode
3. **Test with a simple file upload** to isolate the issue
4. **Check browser console** for JavaScript errors

### **Quick Fix Commands**
```bash
# Restart development server
npm run dev

# Clear browser cache and cookies
# Try uploading again

# Check Firebase rules are published
# Verify authentication is working
```

---

**🎉 Once these rules are applied, your upload permissions should work perfectly!**
