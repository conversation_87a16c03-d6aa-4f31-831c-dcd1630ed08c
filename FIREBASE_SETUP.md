# 🔥 Firebase Setup Guide for Gamestorme

This guide will help you set up Firebase for the Gamestorme platform with all the necessary services and configurations.

## 📋 Prerequisites

- Firebase account
- Node.js 20.x or higher
- Gamestorme project cloned locally

## 🚀 Firebase Project Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Use project name: `gamestorme-faf42` (or your preferred name)
4. Enable Google Analytics (recommended)
5. Complete project creation

### 2. Enable Required Services

#### Firestore Database
1. Go to Firestore Database in Firebase Console
2. Click "Create database"
3. Choose "Start in test mode" (we'll configure rules later)
4. Select your preferred region

#### Realtime Database
1. Go to Realtime Database in Firebase Console
2. Click "Create Database"
3. Choose "Start in test mode"
4. Select your preferred region

#### Firebase Storage
1. Go to Storage in Firebase Console
2. Click "Get started"
3. Choose "Start in test mode"
4. Select your preferred region

#### Authentication
1. Go to Authentication in Firebase Console
2. Click "Get started"
3. Enable Email/Password provider
4. Configure other providers as needed

### 3. Get Firebase Configuration

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Add app" and select Web (</>) 
4. Register your app with name "Gamestorme"
5. Copy the configuration object

## 🔧 Environment Configuration

### 1. Update .env File

Create a `.env.local` file in your project root with your Firebase credentials:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDHz0X-6x78FuzQyGgIeKRy1nfNMw1-_F4
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=gamestorme-faf42.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=gamestorme-faf42
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=gamestorme-faf42.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=206243766870
NEXT_PUBLIC_FIREBASE_APP_ID=1:206243766870:web:BH_jAxT4SxYWYH7E5gM27vhopdr_J7Hvq5xpVL1Pyuf1hHicDkpo8gs_fegcoAQSKHMudrodk0GrzeLbupRchls
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://gamestorme-faf42-default-rtdb.firebaseio.com

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PROJECT_ID=gamestorme-faf42
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"

# Stormie AI Configuration
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Application Settings
NODE_ENV=development
PORT=8000
```

### 2. Generate Service Account Key

1. Go to Project Settings > Service Accounts
2. Click "Generate new private key"
3. Download the JSON file
4. Extract the `client_email` and `private_key` values
5. Add them to your `.env.local` file

## 📊 Database Schema

### Firestore Collections

The platform uses the following Firestore collections:

#### `games`
- Game information, metadata, and approval status
- Developer details and statistics
- Images, screenshots, and media assets

#### `news`
- News articles and blog posts
- Author information and publication status
- SEO metadata and engagement stats

#### `users`
- User profiles and authentication data
- Developer and gamer specific information
- Preferences and settings

#### `reviews`
- Game reviews and ratings
- User feedback and moderation status

## 🔒 Security Rules

### Firestore Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Games collection
    match /games/{gameId} {
      allow read: if resource.data.status == 'approved';
      allow write: if request.auth != null && 
                      (request.auth.uid == resource.data.developer.uid || 
                       request.auth.token.admin == true);
    }
    
    // News collection
    match /news/{articleId} {
      allow read: if resource.data.status == 'published';
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### Storage Rules

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Game assets
    match /games/{gameId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // News images
    match /news/{articleId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // User uploads
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public assets
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
  }
}
```

## 🌱 Database Seeding

### 1. Install Dependencies

```bash
npm install firebase-admin
```

### 2. Update Seeding Script

Edit `scripts/seed-database.js` with your actual Firebase credentials.

### 3. Run Seeding

```bash
node scripts/seed-database.js
```

This will populate your database with sample games and news articles.

## 🧪 Testing the Setup

### 1. Start Development Server

```bash
npm run dev
```

### 2. Test Pages

- Visit `/games` to see the games from Firestore
- Visit `/news` to see news articles
- Visit `/games/[id]` to test individual game pages
- Visit `/admin` to test admin functionality

### 3. Verify API Endpoints

- `GET /api/games` - List games
- `GET /api/games/[id]` - Get specific game
- `GET /api/news` - List news articles
- `GET /api/news/[id]` - Get specific article

## 🔧 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify your service account key is correct
   - Check that the private key includes proper line breaks (`\n`)

2. **Permission Denied**
   - Update Firestore security rules
   - Ensure user has proper authentication

3. **API Errors**
   - Check Firebase project ID matches your configuration
   - Verify all required environment variables are set

4. **Missing Data**
   - Run the database seeding script
   - Check Firestore console for data

### Debug Mode

Enable debug logging by adding to your `.env.local`:

```env
FIREBASE_DEBUG=true
```

## 📚 Additional Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Storage Rules](https://firebase.google.com/docs/storage/security)
- [Next.js with Firebase](https://firebase.google.com/docs/web/setup)

## 🆘 Support

If you encounter issues:

1. Check the Firebase Console for errors
2. Review the browser console for client-side errors
3. Check server logs for API errors
4. Verify your environment variables are correct

For additional help, contact the development team or check the project documentation.
