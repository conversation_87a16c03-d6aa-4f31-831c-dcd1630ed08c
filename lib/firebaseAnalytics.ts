import { analytics } from './firebase';
import { logEvent, setUserProperties, setUserId } from 'firebase/analytics';

// Enhanced Firebase Analytics integration for Gamestorme
export class GamestormeAnalytics {
  private static instance: GamestormeAnalytics;
  private isInitialized = false;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): GamestormeAnalytics {
    if (!GamestormeAnalytics.instance) {
      GamestormeAnalytics.instance = new GamestormeAnalytics();
    }
    return GamestormeAnalytics.instance;
  }

  private initialize() {
    if (typeof window !== 'undefined' && analytics) {
      this.isInitialized = true;
      console.log('🔥 Firebase Analytics initialized for Gamestorme');
    }
  }

  // Set user properties for better analytics
  setUser(userId: string, properties: {
    user_type?: 'developer' | 'gamer' | 'admin';
    subscription_tier?: 'free' | 'pro' | 'enterprise';
    registration_date?: string;
    total_games?: number;
    total_revenue?: number;
  }) {
    if (!this.isInitialized || !analytics) return;

    setUserId(analytics, userId);
    setUserProperties(analytics, {
      user_type: properties.user_type || 'gamer',
      subscription_tier: properties.subscription_tier || 'free',
      registration_date: properties.registration_date,
      total_games: properties.total_games?.toString(),
      total_revenue: properties.total_revenue?.toString(),
    });
  }

  // Game-related events
  trackGameView(gameId: string, gameTitle: string, developerId?: string, category?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'view_item', {
      item_id: gameId,
      item_name: gameTitle,
      item_category: category || 'game',
      content_type: 'game',
      developer_id: developerId,
    });
  }

  trackGameDownload(gameId: string, gameTitle: string, developerId?: string, price?: number) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'select_content', {
      content_type: 'game_download',
      item_id: gameId,
      item_name: gameTitle,
      developer_id: developerId,
      value: price || 0,
    });
  }

  trackGamePurchase(gameId: string, gameTitle: string, price: number, currency: string = 'USD', developerId?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'purchase', {
      transaction_id: `${gameId}_${Date.now()}`,
      value: price,
      currency: currency,
      items: [{
        item_id: gameId,
        item_name: gameTitle,
        item_category: 'game',
        quantity: 1,
        price: price,
      }],
      developer_id: developerId,
    });
  }

  trackGameLike(gameId: string, gameTitle: string, developerId?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'like', {
      content_type: 'game',
      item_id: gameId,
      item_name: gameTitle,
      developer_id: developerId,
    });
  }

  trackGameShare(gameId: string, gameTitle: string, method: string, developerId?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'share', {
      content_type: 'game',
      item_id: gameId,
      item_name: gameTitle,
      method: method,
      developer_id: developerId,
    });
  }

  // Search and discovery
  trackSearch(searchTerm: string, category?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'search', {
      search_term: searchTerm,
      content_category: category || 'games',
    });
  }

  // User engagement
  trackPageView(pageName: string, pageTitle?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'page_view', {
      page_title: pageTitle || pageName,
      page_location: window.location.href,
      page_path: window.location.pathname,
    });
  }

  trackUserSignup(method: string = 'email') {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'sign_up', {
      method: method,
    });
  }

  trackUserLogin(method: string = 'email') {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'login', {
      method: method,
    });
  }

  // Developer-specific events
  trackGameUpload(gameId: string, gameTitle: string, category: string, price: number) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'game_upload', {
      item_id: gameId,
      item_name: gameTitle,
      item_category: category,
      value: price,
      content_type: 'game_submission',
    });
  }

  trackDeveloperDashboardView(section: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'developer_dashboard_view', {
      dashboard_section: section,
      content_type: 'dashboard',
    });
  }

  trackSupportTicketCreated(category: string, priority: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'support_ticket_created', {
      ticket_category: category,
      ticket_priority: priority,
      content_type: 'support',
    });
  }

  // AI and marketing events
  trackAIInsightViewed(insightType: string, gameId?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'ai_insight_viewed', {
      insight_type: insightType,
      item_id: gameId,
      content_type: 'ai_marketing',
    });
  }

  trackMarketingCampaignClick(campaignId: string, gameId: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'campaign_click', {
      campaign_id: campaignId,
      item_id: gameId,
      content_type: 'marketing',
    });
  }

  // Performance and technical events
  trackPerformanceMetric(metricName: string, value: number, unit: string = 'ms') {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'performance_metric', {
      metric_name: metricName,
      metric_value: value,
      metric_unit: unit,
      content_type: 'performance',
    });
  }

  trackError(errorType: string, errorMessage: string, page?: string) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'exception', {
      description: errorMessage,
      fatal: false,
      error_type: errorType,
      page_path: page || window.location.pathname,
    });
  }

  // Custom events for business intelligence
  trackBusinessEvent(eventName: string, parameters: { [key: string]: any }) {
    if (!this.isInitialized || !analytics) return;

    // Ensure parameters are properly formatted for Firebase Analytics
    const formattedParams: { [key: string]: string | number } = {};
    
    Object.keys(parameters).forEach(key => {
      const value = parameters[key];
      if (typeof value === 'string' || typeof value === 'number') {
        formattedParams[key] = value;
      } else if (typeof value === 'boolean') {
        formattedParams[key] = value ? 'true' : 'false';
      } else {
        formattedParams[key] = String(value);
      }
    });

    logEvent(analytics, eventName, formattedParams);
  }

  // Revenue and monetization tracking
  trackRevenue(amount: number, currency: string = 'USD', source: string = 'game_sales') {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'earn_virtual_currency', {
      virtual_currency_name: currency,
      value: amount,
      source: source,
    });
  }

  // Session tracking
  trackSessionStart() {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'session_start', {
      engagement_time_msec: 0,
    });
  }

  trackSessionEnd(duration: number) {
    if (!this.isInitialized || !analytics) return;

    logEvent(analytics, 'session_end', {
      engagement_time_msec: duration,
    });
  }
}

// Export singleton instance
export const gamestormeAnalytics = GamestormeAnalytics.getInstance();

// Export convenience functions
export const trackGameView = (gameId: string, gameTitle: string, developerId?: string, category?: string) => 
  gamestormeAnalytics.trackGameView(gameId, gameTitle, developerId, category);

export const trackGameDownload = (gameId: string, gameTitle: string, developerId?: string, price?: number) => 
  gamestormeAnalytics.trackGameDownload(gameId, gameTitle, developerId, price);

export const trackGamePurchase = (gameId: string, gameTitle: string, price: number, currency?: string, developerId?: string) => 
  gamestormeAnalytics.trackGamePurchase(gameId, gameTitle, price, currency, developerId);

export const trackPageView = (pageName: string, pageTitle?: string) => 
  gamestormeAnalytics.trackPageView(pageName, pageTitle);

export const trackSearch = (searchTerm: string, category?: string) => 
  gamestormeAnalytics.trackSearch(searchTerm, category);

export const trackUserSignup = (method?: string) => 
  gamestormeAnalytics.trackUserSignup(method);

export const trackUserLogin = (method?: string) => 
  gamestormeAnalytics.trackUserLogin(method);

export default gamestormeAnalytics;
