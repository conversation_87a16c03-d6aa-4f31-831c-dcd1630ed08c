// 🛡️ Gamestorme Security Suite - Advanced Protection System
// Comprehensive security measures against DDoS, hacks, and malicious attacks

import { NextRequest, NextResponse } from 'next/server';
import { rateLimit } from 'express-rate-limit';
import { slowDown } from 'express-slow-down';

// 🔒 Security Configuration
export const SECURITY_CONFIG = {
  // Rate Limiting
  RATE_LIMITS: {
    GENERAL: { windowMs: 15 * 60 * 1000, max: 100 }, // 100 requests per 15 minutes
    AUTH: { windowMs: 15 * 60 * 1000, max: 5 }, // 5 login attempts per 15 minutes
    API: { windowMs: 1 * 60 * 1000, max: 60 }, // 60 API calls per minute
    UPLOAD: { windowMs: 60 * 60 * 1000, max: 10 }, // 10 uploads per hour
    ADMIN: { windowMs: 5 * 60 * 1000, max: 20 }, // 20 admin actions per 5 minutes
  },
  
  // DDoS Protection
  DDOS_PROTECTION: {
    THRESHOLD: 1000, // Requests per minute before triggering protection
    BAN_DURATION: 24 * 60 * 60 * 1000, // 24 hours ban
    WHITELIST: ['127.0.0.1', '::1'], // Localhost whitelist
  },
  
  // Security Headers
  SECURITY_HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://www.gstatic.com https://*.firebaseapp.com https://*.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.gamestorme.com wss://gamestorme.com https://*.firebaseapp.com https://*.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com;",
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
  },
  
  // Input Validation
  INPUT_VALIDATION: {
    MAX_STRING_LENGTH: 10000,
    MAX_ARRAY_LENGTH: 1000,
    ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/zip'],
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  },
  
  // Suspicious Activity Detection
  THREAT_DETECTION: {
    SQL_INJECTION_PATTERNS: [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\'|\"|;|--|\*|\/\*|\*\/)/,
      /(\bOR\b|\bAND\b).*(\=|\<|\>)/i,
    ],
    XSS_PATTERNS: [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    ],
    COMMAND_INJECTION_PATTERNS: [
      /(\||&|;|\$\(|\`)/,
      /(rm\s|wget\s|curl\s|nc\s|telnet\s)/i,
    ],
  },
};

// 🛡️ IP-based Request Tracking
class SecurityTracker {
  private static instance: SecurityTracker;
  private requestCounts: Map<string, { count: number; firstRequest: number; banned: boolean; banExpiry?: number }> = new Map();
  private suspiciousIPs: Set<string> = new Set();
  
  static getInstance(): SecurityTracker {
    if (!SecurityTracker.instance) {
      SecurityTracker.instance = new SecurityTracker();
    }
    return SecurityTracker.instance;
  }
  
  // Track request from IP
  trackRequest(ip: string): boolean {
    const now = Date.now();
    const record = this.requestCounts.get(ip) || { count: 0, firstRequest: now, banned: false };
    
    // Check if IP is banned and ban has expired
    if (record.banned && record.banExpiry && now > record.banExpiry) {
      record.banned = false;
      record.banExpiry = undefined;
      record.count = 0;
      record.firstRequest = now;
    }
    
    // If IP is currently banned, reject request
    if (record.banned) {
      return false;
    }
    
    // Reset counter if window has passed
    if (now - record.firstRequest > 60000) { // 1 minute window
      record.count = 0;
      record.firstRequest = now;
    }
    
    record.count++;
    this.requestCounts.set(ip, record);
    
    // Check for DDoS threshold
    if (record.count > SECURITY_CONFIG.DDOS_PROTECTION.THRESHOLD) {
      this.banIP(ip);
      return false;
    }
    
    return true;
  }
  
  // Ban IP address
  banIP(ip: string): void {
    const record = this.requestCounts.get(ip) || { count: 0, firstRequest: Date.now(), banned: false };
    record.banned = true;
    record.banExpiry = Date.now() + SECURITY_CONFIG.DDOS_PROTECTION.BAN_DURATION;
    this.requestCounts.set(ip, record);
    this.suspiciousIPs.add(ip);
    
    console.log(`🚨 SECURITY ALERT: IP ${ip} has been banned for suspicious activity`);
    
    // Log to security monitoring system
    this.logSecurityEvent('IP_BANNED', { ip, reason: 'DDoS_THRESHOLD_EXCEEDED', banExpiry: record.banExpiry });
  }
  
  // Check if IP is suspicious
  isSuspicious(ip: string): boolean {
    return this.suspiciousIPs.has(ip);
  }
  
  // Log security events
  private logSecurityEvent(event: string, data: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      data,
      severity: 'HIGH',
    };
    
    // In production, send to security monitoring service
    console.log('🔒 SECURITY LOG:', JSON.stringify(logEntry, null, 2));
  }
}

// 🔍 Input Validation and Sanitization
export class InputValidator {
  static validateString(input: string, maxLength: number = SECURITY_CONFIG.INPUT_VALIDATION.MAX_STRING_LENGTH): boolean {
    if (typeof input !== 'string' || input.length > maxLength) {
      return false;
    }
    
    // Check for malicious patterns
    for (const pattern of SECURITY_CONFIG.THREAT_DETECTION.SQL_INJECTION_PATTERNS) {
      if (pattern.test(input)) {
        console.log('🚨 SQL Injection attempt detected:', input);
        return false;
      }
    }
    
    for (const pattern of SECURITY_CONFIG.THREAT_DETECTION.XSS_PATTERNS) {
      if (pattern.test(input)) {
        console.log('🚨 XSS attempt detected:', input);
        return false;
      }
    }
    
    for (const pattern of SECURITY_CONFIG.THREAT_DETECTION.COMMAND_INJECTION_PATTERNS) {
      if (pattern.test(input)) {
        console.log('🚨 Command injection attempt detected:', input);
        return false;
      }
    }
    
    return true;
  }
  
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[;&|`$()]/g, '') // Remove command injection chars
      .trim();
  }
  
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && this.validateString(email, 254);
  }
  
  static validateObject(obj: any, maxDepth: number = 10): boolean {
    if (maxDepth <= 0) return false;
    
    if (typeof obj === 'string') {
      return this.validateString(obj);
    }
    
    if (Array.isArray(obj)) {
      if (obj.length > SECURITY_CONFIG.INPUT_VALIDATION.MAX_ARRAY_LENGTH) {
        return false;
      }
      return obj.every(item => this.validateObject(item, maxDepth - 1));
    }
    
    if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).every(value => this.validateObject(value, maxDepth - 1));
    }
    
    return true;
  }
}

// 🛡️ Security Middleware
export function createSecurityMiddleware() {
  const tracker = SecurityTracker.getInstance();
  
  return (req: NextRequest): NextResponse | null => {
    const ip = req.ip || req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    
    // Check if IP is whitelisted
    if (SECURITY_CONFIG.DDOS_PROTECTION.WHITELIST.includes(ip)) {
      return null; // Allow whitelisted IPs
    }
    
    // Track and validate request
    if (!tracker.trackRequest(ip)) {
      console.log(`🚨 BLOCKED REQUEST from banned IP: ${ip}`);
      return new NextResponse('Too Many Requests - IP Banned', { status: 429 });
    }
    
    // Add security headers
    const response = NextResponse.next();
    Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  };
}

// 🔒 API Route Protection
export function protectAPIRoute(handler: Function) {
  return async (req: NextRequest, res: NextResponse) => {
    const validator = new InputValidator();
    
    // Validate request body
    if (req.body) {
      if (!InputValidator.validateObject(req.body)) {
        return NextResponse.json({ error: 'Invalid input detected' }, { status: 400 });
      }
    }
    
    // Validate query parameters
    const url = new URL(req.url);
    for (const [key, value] of url.searchParams.entries()) {
      if (!InputValidator.validateString(value)) {
        return NextResponse.json({ error: 'Invalid query parameter' }, { status: 400 });
      }
    }
    
    return handler(req, res);
  };
}

// 🚨 Honeypot System - Trap malicious bots
export class HoneypotSystem {
  private static trappedIPs: Set<string> = new Set();
  
  static createHoneypot(): string {
    // Create invisible form field that bots might fill
    return `
      <input type="text" name="website" style="display:none !important" tabindex="-1" autocomplete="off">
      <input type="email" name="email_confirm" style="position:absolute;left:-9999px" tabindex="-1" autocomplete="off">
    `;
  }
  
  static checkHoneypot(formData: any, ip: string): boolean {
    // If honeypot fields are filled, it's likely a bot
    if (formData.website || formData.email_confirm) {
      this.trappedIPs.add(ip);
      console.log(`🍯 HONEYPOT TRIGGERED: Bot detected from IP ${ip}`);
      return false;
    }
    return true;
  }
  
  static isTrapped(ip: string): boolean {
    return this.trappedIPs.has(ip);
  }
}

// 🔐 Advanced Threat Detection
export class ThreatDetector {
  static analyzeRequest(req: NextRequest): { threat: boolean; type?: string; severity?: string } {
    const userAgent = req.headers.get('user-agent') || '';
    const referer = req.headers.get('referer') || '';
    
    // Check for suspicious user agents
    const suspiciousAgents = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i,
      /sqlmap/i, /nikto/i, /nmap/i, /masscan/i
    ];
    
    for (const pattern of suspiciousAgents) {
      if (pattern.test(userAgent)) {
        return { threat: true, type: 'SUSPICIOUS_USER_AGENT', severity: 'MEDIUM' };
      }
    }
    
    // Check for suspicious referers
    const suspiciousReferers = [
      /malware/i, /phishing/i, /spam/i, /hack/i
    ];
    
    for (const pattern of suspiciousReferers) {
      if (pattern.test(referer)) {
        return { threat: true, type: 'SUSPICIOUS_REFERER', severity: 'HIGH' };
      }
    }
    
    return { threat: false };
  }
}

export default SecurityTracker;
