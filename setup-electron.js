const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Gamestorme Electron Setup Script');
console.log('=====================================');

const args = process.argv.slice(2);
const command = args[0];

if (!command || !['web', 'desktop', 'build', 'upload'].includes(command)) {
  console.log(`
Usage: node setup-electron.js <command>

Commands:
  web      - Switch to web development mode (Heroku-ready)
  desktop  - Switch to desktop development mode (Electron)
  build    - Build desktop executable
  upload   - Upload executable to Firebase

Examples:
  node setup-electron.js web      # For web development/Heroku
  node setup-electron.js desktop  # For desktop development
  node setup-electron.js build    # Build Windows .exe
  node setup-electron.js upload   # Upload to Firebase
`);
  process.exit(1);
}

const webPackageJson = 'package.json';
const electronPackageJson = 'electron-package.json';
const backupPackageJson = 'package-backup.json';

switch (command) {
  case 'web':
    console.log('🌐 Switching to web development mode...');
    
    // Backup current package.json if it's the electron version
    if (fs.existsSync(webPackageJson)) {
      const currentPkg = JSON.parse(fs.readFileSync(webPackageJson, 'utf8'));
      if (currentPkg.main === 'public/electron.js') {
        fs.copyFileSync(webPackageJson, backupPackageJson);
        console.log('📦 Backed up current package.json');
      }
    }
    
    // Restore web package.json (this should already be the web version)
    console.log('✅ Web mode active - ready for Heroku deployment');
    console.log('📋 Available commands:');
    console.log('   npm run dev     - Start development server');
    console.log('   npm run build   - Build for production');
    console.log('   git push heroku main - Deploy to Heroku');
    break;

  case 'desktop':
    console.log('🖥️  Switching to desktop development mode...');
    
    // Backup current package.json
    if (fs.existsSync(webPackageJson)) {
      fs.copyFileSync(webPackageJson, backupPackageJson);
      console.log('📦 Backed up web package.json');
    }
    
    // Copy electron package.json to main package.json
    if (fs.existsSync(electronPackageJson)) {
      fs.copyFileSync(electronPackageJson, webPackageJson);
      console.log('📦 Switched to Electron package.json');
      
      // Install Electron dependencies
      console.log('📥 Installing Electron dependencies...');
      try {
        execSync('npm install', { stdio: 'inherit' });
        console.log('✅ Desktop mode active - ready for Electron development');
        console.log('📋 Available commands:');
        console.log('   npm run electron-dev  - Start Electron in development');
        console.log('   npm run dist:win      - Build Windows .exe');
        console.log('   npm run dist:mac      - Build macOS .dmg');
        console.log('   npm run dist:linux    - Build Linux .AppImage');
        console.log('   npm run build-launcher - Custom build script');
      } catch (error) {
        console.error('❌ Failed to install dependencies:', error.message);
        // Restore backup
        if (fs.existsSync(backupPackageJson)) {
          fs.copyFileSync(backupPackageJson, webPackageJson);
          console.log('🔄 Restored web package.json');
        }
      }
    } else {
      console.error('❌ electron-package.json not found');
    }
    break;

  case 'build':
    console.log('🔨 Building desktop executable...');
    
    // Check if we're in desktop mode
    if (fs.existsSync(webPackageJson)) {
      const currentPkg = JSON.parse(fs.readFileSync(webPackageJson, 'utf8'));
      if (currentPkg.main !== 'public/electron.js') {
        console.log('⚠️  Not in desktop mode. Switching...');
        execSync('node setup-electron.js desktop', { stdio: 'inherit' });
      }
    }
    
    try {
      console.log('📦 Building Next.js application...');
      execSync('npm run build', { stdio: 'inherit' });
      
      console.log('⚡ Building Electron executable...');
      execSync('npm run dist:win', { stdio: 'inherit' });
      
      console.log('✅ Build completed! Check the "dist" directory.');
      
      // List created files
      const distDir = path.join(__dirname, 'dist');
      if (fs.existsSync(distDir)) {
        console.log('\n📄 Created files:');
        fs.readdirSync(distDir).forEach(file => {
          const filePath = path.join(distDir, file);
          const stats = fs.statSync(filePath);
          if (stats.isFile()) {
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            console.log(`   ${file} (${sizeInMB} MB)`);
          }
        });
      }
    } catch (error) {
      console.error('❌ Build failed:', error.message);
    }
    break;

  case 'upload':
    console.log('📤 Uploading executable to Firebase...');
    
    try {
      execSync('node scripts/upload-to-firebase.js --run', { stdio: 'inherit' });
      console.log('✅ Upload completed!');
      console.log('📋 Next steps:');
      console.log('1. Update download URLs in components/DownloadLauncher.tsx');
      console.log('2. Switch back to web mode: node setup-electron.js web');
      console.log('3. Deploy to Heroku: git push heroku main');
    } catch (error) {
      console.error('❌ Upload failed:', error.message);
      console.log('💡 Make sure you have:');
      console.log('   - Built the executable first (node setup-electron.js build)');
      console.log('   - Updated Firebase credentials in scripts/upload-to-firebase.js');
    }
    break;
}

// Clean up backup if everything went well
if (fs.existsSync(backupPackageJson) && command !== 'desktop') {
  // Keep backup for desktop mode in case we need to switch back
  if (command === 'web') {
    fs.unlinkSync(backupPackageJson);
  }
}

console.log('\n🎮 Gamestorme setup complete!');

// Show current mode
if (fs.existsSync(webPackageJson)) {
  const currentPkg = JSON.parse(fs.readFileSync(webPackageJson, 'utf8'));
  const mode = currentPkg.main === 'public/electron.js' ? 'Desktop' : 'Web';
  console.log(`📊 Current mode: ${mode}`);
}
