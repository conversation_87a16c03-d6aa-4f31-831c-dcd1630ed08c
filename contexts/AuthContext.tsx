import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  User,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile
} from 'firebase/auth';
import { ref, set, get } from 'firebase/database';
import { auth, database } from '../lib/firebase';

// Define user types
export type UserType = 'developer' | 'gamer';

// Define user profile interface
export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  userType: UserType;
  createdAt: number;
}

// Define auth context interface
interface AuthContextType {
  currentUser: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  isAuthPage: boolean;
  setIsAuthPage: (value: boolean) => void;
  signUp: (email: string, password: string, displayName: string, userType: UserType) => Promise<User>;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Create auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthPage, setIsAuthPage] = useState(false);

  // Sign up function
  const signUp = async (email: string, password: string, displayName: string, userType: UserType) => {
    console.log('Starting signup process with:', { email, displayName, userType });

    try {
      console.log('Firebase auth instance:', auth);

      // Create user with email and password
      console.log('Attempting to create user with email and password');
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      console.log('User created successfully:', userCredential);

      const user = userCredential.user;

      // Update user profile with display name
      console.log('Updating user profile with display name:', displayName);
      await updateProfile(user, { displayName });

      // Create user profile in database
      const userProfile: UserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: displayName,
        userType: userType,
        createdAt: Date.now()
      };

      // Save user profile to database
      console.log('Saving user profile to database:', userProfile);
      await set(ref(database, `users/${user.uid}`), userProfile);
      console.log('User profile saved successfully');

      return user;
    } catch (error: any) {
      console.error('Error signing up:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      throw error;
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      console.log('Starting logout process...');
      await signOut(auth);
      console.log('User logged out successfully');
      // Clear user profile state
      setUserProfile(null);
      setCurrentUser(null);
    } catch (error) {
      console.error('Error logging out:', error);
      throw error;
    }
  };

  // Fetch user profile from database
  const fetchUserProfile = async (uid: string) => {
    try {
      const userRef = ref(database, `users/${uid}`);
      const snapshot = await get(userRef);

      if (snapshot.exists()) {
        setUserProfile(snapshot.val() as UserProfile);
      } else {
        console.log('No user profile found');
        setUserProfile(null);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setUserProfile(null);
    }
  };

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);

      if (user) {
        fetchUserProfile(user.uid);
      } else {
        setUserProfile(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userProfile,
    loading,
    isAuthPage,
    setIsAuthPage,
    signUp,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
