import { alpha as muiAlpha } from '@mui/material/styles';

// Color constants to prevent string-based color issues
export const COLORS = {
  // Basic colors
  WHITE: '#ffffff',
  BLACK: '#000000',
  TRANSPARENT: 'transparent',
  
  // Common colors
  RED: '#f44336',
  PINK: '#e91e63',
  PURPLE: '#9c27b0',
  DEEP_PURPLE: '#673ab7',
  INDIGO: '#3f51b5',
  BLUE: '#2196f3',
  LIGHT_BLUE: '#03a9f4',
  CYAN: '#00bcd4',
  TEAL: '#009688',
  GREEN: '#4caf50',
  LIGHT_GREEN: '#8bc34a',
  LIME: '#cddc39',
  YELLOW: '#ffeb3b',
  AMBER: '#ffc107',
  ORANGE: '#ff9800',
  DEEP_ORANGE: '#ff5722',
  BROWN: '#795548',
  GREY: '#9e9e9e',
  BLUE_GREY: '#607d8b',
  
  // Gamestorme brand colors
  PRIMARY: '#4229BC',
  PRIMARY_LIGHT: '#7B65ED',
  PRIMARY_DARK: '#2E1C84',
  SECONDARY: '#F0BC2B',
  SECONDARY_LIGHT: '#F7D36E',
  SECONDARY_DARK: '#D9A61F',
  
  // Background colors
  BACKGROUND_DEFAULT: '#0A0A1A',
  BACKGROUND_PAPER: '#1D1429',
  BACKGROUND_DARK: '#050510',
  BACKGROUND_LIGHT: '#2D2D3D',
  
  // Text colors
  TEXT_PRIMARY: '#FFFFFF',
  TEXT_SECONDARY: '#B8BCC8',
  TEXT_TERTIARY: '#8B8FA3',
} as const;

// Safe alpha function that only accepts valid color values
export function alpha(color: string, opacity: number): string {
  // Validate that color is a proper hex, rgb, or hsl value
  if (!isValidColor(color)) {
    console.warn(`Invalid color value: ${color}. Using fallback color.`);
    return muiAlpha(COLORS.WHITE, opacity);
  }
  
  return muiAlpha(color, opacity);
}

// Color validation function
function isValidColor(color: string): boolean {
  // Check for hex colors
  if (/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
    return true;
  }
  
  // Check for rgb/rgba colors
  if (/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+)?\s*\)$/i.test(color)) {
    return true;
  }
  
  // Check for hsl/hsla colors
  if (/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(,\s*[\d.]+)?\s*\)$/i.test(color)) {
    return true;
  }
  
  // Check if it's one of our predefined colors
  if (Object.values(COLORS).includes(color as any)) {
    return true;
  }
  
  // Check for CSS color keywords (limited set)
  const validKeywords = [
    'transparent', 'currentColor', 'inherit', 'initial', 'unset'
  ];
  
  return validKeywords.includes(color.toLowerCase());
}

// Utility functions for common color operations
export const colorUtils = {
  // Create a semi-transparent white overlay
  whiteAlpha: (opacity: number) => alpha(COLORS.WHITE, opacity),
  
  // Create a semi-transparent black overlay
  blackAlpha: (opacity: number) => alpha(COLORS.BLACK, opacity),
  
  // Create primary color with alpha
  primaryAlpha: (opacity: number) => alpha(COLORS.PRIMARY, opacity),
  
  // Create secondary color with alpha
  secondaryAlpha: (opacity: number) => alpha(COLORS.SECONDARY, opacity),
  
  // Get contrasting text color
  getContrastText: (backgroundColor: string): string => {
    // Simple contrast calculation - in production, you might want a more sophisticated algorithm
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
    return brightness > 128 ? COLORS.BLACK : COLORS.WHITE;
  },
  
  // Lighten a color
  lighten: (color: string, amount: number): string => {
    // This is a simplified version - for production, consider using a color manipulation library
    return color; // Placeholder - implement actual lightening logic
  },
  
  // Darken a color
  darken: (color: string, amount: number): string => {
    // This is a simplified version - for production, consider using a color manipulation library
    return color; // Placeholder - implement actual darkening logic
  },
};

// Common color combinations for consistent theming
export const colorCombinations = {
  glassmorphism: {
    background: alpha(COLORS.BACKGROUND_PAPER, 0.9),
    border: alpha(COLORS.PRIMARY, 0.1),
    backdropFilter: 'blur(20px)',
  },
  
  primaryGradient: {
    background: `linear-gradient(135deg, ${COLORS.PRIMARY} 0%, ${COLORS.PRIMARY_LIGHT} 100%)`,
  },
  
  secondaryGradient: {
    background: `linear-gradient(135deg, ${COLORS.SECONDARY} 0%, ${COLORS.SECONDARY_LIGHT} 100%)`,
  },
  
  heroGradient: {
    background: `linear-gradient(135deg, ${alpha(COLORS.PRIMARY, 0.1)} 0%, ${alpha(COLORS.SECONDARY, 0.1)} 100%)`,
  },
  
  cardHover: {
    transform: 'translateY(-4px)',
    boxShadow: `0 20px 40px ${alpha(COLORS.PRIMARY, 0.2)}`,
  },
};

// Export everything for easy importing
export default {
  COLORS,
  alpha,
  colorUtils,
  colorCombinations,
};
