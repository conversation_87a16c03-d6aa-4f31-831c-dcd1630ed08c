import React from 'react';
import { auth, analytics } from '../lib/firebase';
import { logEvent } from 'firebase/analytics';

// Generate a unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Get or create session ID
const getSessionId = (): string => {
  if (typeof window === 'undefined') return generateSessionId();
  
  let sessionId = sessionStorage.getItem('gamestorme_session_id');
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem('gamestorme_session_id', sessionId);
  }
  return sessionId;
};

// Get device information
const getDeviceInfo = () => {
  if (typeof window === 'undefined') return {};

  const userAgent = navigator.userAgent;
  let deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop';
  
  if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
    deviceType = 'tablet';
  } else if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
    deviceType = 'mobile';
  }

  const getBrowser = () => {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  };

  const getOS = () => {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  };

  return {
    type: deviceType,
    browser: getBrowser(),
    os: getOS(),
  };
};

// Get location information (simplified)
const getLocationInfo = async () => {
  try {
    // In production, you might want to use a geolocation service
    // For now, we'll return mock data
    return {
      country: 'Unknown',
      region: 'Unknown',
      city: 'Unknown',
    };
  } catch (error) {
    return {};
  }
};

// Base analytics tracking function
const trackEvent = async (
  type: 'page_view' | 'game_view' | 'game_download' | 'game_like' | 'search' | 'user_signup' | 'purchase' | 'session_start' | 'session_end',
  metadata: {
    gameId?: string;
    developerId?: string;
    page?: string;
    searchQuery?: string;
    purchaseAmount?: number;
    sessionDuration?: number;
    [key: string]: any;
  } = {}
) => {
  try {
    const sessionId = getSessionId();
    const userId = auth.currentUser?.uid;
    const deviceInfo = getDeviceInfo();
    const locationInfo = await getLocationInfo();

    const eventData = {
      type,
      userId,
      gameId: metadata.gameId,
      developerId: metadata.developerId,
      sessionId,
      metadata: {
        ...metadata,
        page: metadata.page || (typeof window !== 'undefined' ? window.location.pathname : ''),
        referrer: typeof window !== 'undefined' ? document.referrer : '',
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : '',
        location: locationInfo,
        device: deviceInfo,
      },
    };

    // Send to Firebase Analytics
    if (analytics) {
      logEvent(analytics, type, {
        game_id: metadata.gameId,
        developer_id: metadata.developerId,
        page: metadata.page,
        search_query: metadata.searchQuery,
        purchase_amount: metadata.purchaseAmount,
      });
    }

    // Send to analytics API
    await fetch('/api/analytics/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(eventData),
    });
  } catch (error) {
    console.error('Analytics tracking error:', error);
  }
};

// Specific tracking functions
export const analytics = {
  // Page view tracking
  trackPageView: (page?: string) => {
    trackEvent('page_view', { page });
  },

  // Game-related tracking
  trackGameView: (gameId: string, developerId?: string) => {
    trackEvent('game_view', { gameId, developerId });
  },

  trackGameDownload: (gameId: string, developerId?: string) => {
    trackEvent('game_download', { gameId, developerId });
  },

  trackGameLike: (gameId: string, developerId?: string) => {
    trackEvent('game_like', { gameId, developerId });
  },

  // Search tracking
  trackSearch: (searchQuery: string) => {
    trackEvent('search', { searchQuery });
  },

  // User actions
  trackUserSignup: () => {
    trackEvent('user_signup');
  },

  trackPurchase: (gameId: string, amount: number, developerId?: string) => {
    trackEvent('purchase', { gameId, developerId, purchaseAmount: amount });
  },

  // Session tracking
  trackSessionStart: () => {
    trackEvent('session_start');
    
    // Store session start time
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('gamestorme_session_start', Date.now().toString());
    }
  },

  trackSessionEnd: () => {
    const sessionStart = typeof window !== 'undefined' 
      ? sessionStorage.getItem('gamestorme_session_start') 
      : null;
    
    const sessionDuration = sessionStart 
      ? Math.floor((Date.now() - parseInt(sessionStart)) / 1000) 
      : 0;

    trackEvent('session_end', { sessionDuration });
  },

  // Custom event tracking
  trackCustomEvent: (eventType: string, metadata: any = {}) => {
    // For custom events, we'll use the base tracking function
    // but prefix with 'custom_' to distinguish them
    trackEvent(eventType as any, metadata);
  },
};

// Auto-track page views on route changes (for Next.js)
export const initializeAnalytics = () => {
  if (typeof window === 'undefined') return;

  // Track initial page view
  analytics.trackPageView();

  // Track session start
  analytics.trackSessionStart();

  // Track session end on page unload
  window.addEventListener('beforeunload', () => {
    analytics.trackSessionEnd();
  });

  // Track page visibility changes
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      analytics.trackSessionEnd();
    } else {
      analytics.trackSessionStart();
    }
  });
};

// Hook for React components to track page views
export const usePageTracking = (pageName?: string) => {
  React.useEffect(() => {
    analytics.trackPageView(pageName);
  }, [pageName]);
};

// Higher-order component for automatic page tracking
export const withAnalytics = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  pageName?: string
) => {
  return (props: P) => {
    React.useEffect(() => {
      analytics.trackPageView(pageName);
    }, []);

    return <WrappedComponent {...props} />;
  };
};

// Real-time analytics for developer dashboard
export const realtimeAnalytics = {
  // Subscribe to real-time game metrics
  subscribeToGameMetrics: (gameId: string, callback: (metrics: any) => void) => {
    // This would connect to Firebase Realtime Database
    // For now, we'll simulate with periodic updates
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/games/${gameId}/metrics`);
        const data = await response.json();
        if (data.success) {
          callback(data.data);
        }
      } catch (error) {
        console.error('Real-time metrics error:', error);
      }
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  },

  // Subscribe to real-time developer analytics
  subscribeToDeveloperAnalytics: (developerId: string, callback: (analytics: any) => void) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/developer/analytics?developerId=${developerId}`);
        const data = await response.json();
        if (data.success) {
          callback(data.data);
        }
      } catch (error) {
        console.error('Real-time analytics error:', error);
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  },

  // Get live platform metrics
  getLivePlatformMetrics: async () => {
    try {
      const response = await fetch('/api/analytics/platform/live');
      const data = await response.json();
      return data.success ? data.data : null;
    } catch (error) {
      console.error('Live platform metrics error:', error);
      return null;
    }
  },
};

// Performance tracking
export const performanceAnalytics = {
  // Track page load performance
  trackPageLoad: () => {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      const metrics = {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstPaint: 0,
        firstContentfulPaint: 0,
      };

      // Get paint metrics if available
      const paintEntries = performance.getEntriesByType('paint');
      paintEntries.forEach((entry) => {
        if (entry.name === 'first-paint') {
          metrics.firstPaint = entry.startTime;
        } else if (entry.name === 'first-contentful-paint') {
          metrics.firstContentfulPaint = entry.startTime;
        }
      });

      // Track performance metrics
      analytics.trackCustomEvent('performance_metrics', metrics);
    });
  },

  // Track user interactions
  trackInteraction: (element: string, action: string) => {
    analytics.trackCustomEvent('user_interaction', {
      element,
      action,
      timestamp: Date.now(),
    });
  },
};

export default analytics;
