# Setup Heroku Environment Variables for Stormie AI
Write-Host "🚀 Setting up Heroku environment variables for Stormie AI..." -ForegroundColor Green

# Set Hugging Face API Key
heroku config:set HUGGINGFACE_API_KEY=*************************************

# Set Node Environment
heroku config:set NODE_ENV=production

# Optional: Set other AI-related configs
heroku config:set AI_CACHE_DURATION=3600
heroku config:set AI_RATE_LIMIT=100

Write-Host "✅ Heroku environment variables set successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🔍 Current Heroku config:" -ForegroundColor Yellow
heroku config

Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Cyan
Write-Host "1. Deploy to Heroku: git push heroku main"
Write-Host "2. Test Stormie AI: https://your-app.herokuapp.com/test-stormie"
Write-Host "3. Access dashboard: https://your-app.herokuapp.com/developer/dashboard"
