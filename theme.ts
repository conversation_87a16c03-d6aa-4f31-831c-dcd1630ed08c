import { createTheme, responsiveFontSizes } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';

// Define color palette constants
const PRIMARY = {
  main: '#4229BC',
  light: '#7B65ED',
  dark: '#2E1C84',
  darker: '#1A0F5C',
  contrastText: '#FFFFFF',
  gradient: 'linear-gradient(135deg, #4229BC 0%, #7B65ED 50%, #9B7EF0 100%)',
  glow: 'rgba(66, 41, 188, 0.4)'
};

const SECONDARY = {
  main: '#F0BC2B',
  light: '#F7D36E',
  dark: '#D9A61F',
  darker: '#B8891A',
  contrastText: '#121212',
  gradient: 'linear-gradient(135deg, #F0BC2B 0%, #F7D36E 50%, #FFE082 100%)',
  glow: 'rgba(240, 188, 43, 0.4)'
};

const SUCCESS = {
  main: '#00D97E',
  light: '#6BFFCE',
  dark: '#00A86B',
  darker: '#007A4F',
  contrastText: '#FFFFFF',
  gradient: 'linear-gradient(135deg, #00D97E 0%, #6BFFCE 100%)',
  glow: 'rgba(0, 217, 126, 0.4)'
};

const ERROR = {
  main: '#FF5252',
  light: '#FF8A8A',
  dark: '#C41C1C',
  darker: '#8B1414',
  contrastText: '#FFFFFF',
  gradient: 'linear-gradient(135deg, #FF5252 0%, #FF8A8A 100%)',
  glow: 'rgba(255, 82, 82, 0.4)'
};

const INFO = {
  main: '#0288D1',
  light: '#5EB8FF',
  dark: '#01579B',
  darker: '#013E6B',
  contrastText: '#FFFFFF',
  gradient: 'linear-gradient(135deg, #0288D1 0%, #5EB8FF 100%)',
  glow: 'rgba(2, 136, 209, 0.4)'
};

const WARNING = {
  main: '#FF9800',
  light: '#FFB547',
  dark: '#C77700',
  darker: '#8F5500',
  contrastText: '#121212',
  gradient: 'linear-gradient(135deg, #FF9800 0%, #FFB547 100%)',
  glow: 'rgba(255, 152, 0, 0.4)'
};

const BACKGROUND = {
  default: '#0A0A1A',
  paper: '#1D1429',
  dark: '#050510',
  light: '#2D2D3D',
  glass: 'rgba(29, 20, 41, 0.15)',
  glassHover: 'rgba(29, 20, 41, 0.25)',
  gradient: 'linear-gradient(135deg, #0A0A1A 0%, #1D1429 50%, #2A1B3D 100%)',
  mesh: 'radial-gradient(circle at 20% 50%, rgba(66, 41, 188, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(240, 188, 43, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(123, 101, 237, 0.1) 0%, transparent 50%)'
};

const TEXT = {
  primary: '#FFFFFF',
  secondary: '#B8BCC8',
  tertiary: '#8B8FA3',
  disabled: 'rgba(184, 188, 200, 0.4)',
  gradient: 'linear-gradient(135deg, #FFFFFF 0%, #B8BCC8 100%)'
};

// Create base theme
let theme = createTheme({
  palette: {
    mode: 'dark',
    primary: PRIMARY,
    secondary: SECONDARY,
    success: SUCCESS,
    error: ERROR,
    info: INFO,
    warning: WARNING,
    background: BACKGROUND,
    text: TEXT,
    action: {
      active: alpha(TEXT.primary, 0.8),
      hover: alpha(TEXT.primary, 0.1),
      selected: alpha(TEXT.primary, 0.16),
      disabled: alpha(TEXT.primary, 0.3),
      disabledBackground: alpha(TEXT.primary, 0.12),
      focus: alpha(TEXT.primary, 0.12)
    }
  },
  typography: {
    fontFamily: '"Exo", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 600,
    fontWeightBold: 800,
    h1: {
      fontFamily: 'Exo',
      fontWeight: 800,
      fontSize: '4rem',
      lineHeight: 1.2,
      letterSpacing: '-0.01em',
    },
    h2: {
      fontFamily: 'Exo',
      fontWeight: 700,
      fontSize: '3rem',
      lineHeight: 1.3,
      letterSpacing: '-0.005em',
    },
    h3: {
      fontFamily: 'Exo',
      fontWeight: 700,
      fontSize: '2.25rem',
      lineHeight: 1.4,
    },
    h4: {
      fontFamily: 'Exo',
      fontWeight: 700,
      fontSize: '1.75rem',
      lineHeight: 1.4,
    },
    h5: {
      fontFamily: 'Exo',
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.5,
    },
    h6: {
      fontFamily: 'Exo',
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.5,
    },
    subtitle1: {
      fontFamily: 'Exo',
      fontWeight: 600,
      fontSize: '1.125rem',
      lineHeight: 1.5,
    },
    subtitle2: {
      fontFamily: 'Exo',
      fontWeight: 500,
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body1: {
      fontFamily: 'Exo',
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontFamily: 'Exo',
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    button: {
      fontFamily: 'Exo',
      fontWeight: 600,
      fontSize: '0.9375rem',
      lineHeight: 1.75,
      textTransform: 'none',
    },
    caption: {
      fontFamily: 'Exo',
      fontSize: '0.75rem',
      lineHeight: 1.5,
    },
    overline: {
      fontFamily: 'Exo',
      fontWeight: 600,
      fontSize: '0.75rem',
      lineHeight: 1.5,
      textTransform: 'uppercase',
      letterSpacing: '0.08em',
    },
  },
  shape: {
    borderRadius: 16,
  },
  shadows: [
    'none',
    '0px 2px 4px rgba(0, 0, 0, 0.2)',
    '0px 4px 8px rgba(0, 0, 0, 0.2)',
    '0px 8px 16px rgba(0, 0, 0, 0.2)',
    '0px 12px 24px rgba(0, 0, 0, 0.2)',
    '0px 16px 32px rgba(0, 0, 0, 0.2)',
    '0px 20px 40px rgba(0, 0, 0, 0.2)',
    '0px 24px 48px rgba(0, 0, 0, 0.2)',
    '0px 28px 56px rgba(0, 0, 0, 0.2)',
    '0px 32px 64px rgba(0, 0, 0, 0.2)',
    '0px 36px 72px rgba(0, 0, 0, 0.2)',
    '0px 40px 80px rgba(0, 0, 0, 0.2)',
    '0px 44px 88px rgba(0, 0, 0, 0.2)',
    '0px 48px 96px rgba(0, 0, 0, 0.2)',
    '0px 52px 104px rgba(0, 0, 0, 0.2)',
    '0px 56px 112px rgba(0, 0, 0, 0.2)',
    '0px 60px 120px rgba(0, 0, 0, 0.2)',
    '0px 64px 128px rgba(0, 0, 0, 0.2)',
    '0px 68px 136px rgba(0, 0, 0, 0.2)',
    '0px 72px 144px rgba(0, 0, 0, 0.2)',
    '0px 76px 152px rgba(0, 0, 0, 0.2)',
    '0px 80px 160px rgba(0, 0, 0, 0.2)',
    '0px 84px 168px rgba(0, 0, 0, 0.2)',
    '0px 88px 176px rgba(0, 0, 0, 0.2)',
    '0px 92px 184px rgba(0, 0, 0, 0.2)',
  ],
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        '*': {
          boxSizing: 'border-box',
        },
        html: {
          margin: 0,
          padding: 0,
          width: '100%',
          height: '100%',
          WebkitOverflowScrolling: 'touch',
        },
        body: {
          margin: 0,
          padding: 0,
          width: '100%',
          height: '100%',
          background: `${BACKGROUND.gradient}, ${BACKGROUND.mesh}`,
          backgroundAttachment: 'fixed',
          scrollbarColor: `${alpha(PRIMARY.main, 0.6)} ${alpha(BACKGROUND.dark, 0.8)}`,
          "&::-webkit-scrollbar, & *::-webkit-scrollbar": {
            backgroundColor: alpha(BACKGROUND.dark, 0.8),
            width: "10px",
          },
          "&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb": {
            borderRadius: 8,
            background: PRIMARY.gradient,
            minHeight: 24,
            border: `2px solid ${BACKGROUND.dark}`,
          },
          "&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover": {
            background: `linear-gradient(135deg, ${PRIMARY.light} 0%, ${PRIMARY.main} 100%)`,
          },
          "&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track": {
            backgroundColor: alpha(BACKGROUND.dark, 0.4),
            borderRadius: 8,
          },
        },
        '#root': {
          width: '100%',
          height: '100%',
        },
        input: {
          '&[type=number]': {
            MozAppearance: 'textfield',
            '&::-webkit-outer-spin-button': {
              margin: 0,
              WebkitAppearance: 'none',
            },
            '&::-webkit-inner-spin-button': {
              margin: 0,
              WebkitAppearance: 'none',
            },
          },
        },
        img: {
          display: 'block',
          maxWidth: '100%',
        },
        a: {
          textDecoration: 'none',
          color: PRIMARY.main,
        },
      },
    },
    MuiBackdrop: {
      styleOverrides: {
        root: {
          backdropFilter: 'blur(8px)',
          backgroundColor: alpha(BACKGROUND.default, 0.8),
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '12px',
          textTransform: 'none',
          fontWeight: 600,
          fontSize: '1rem',
          lineHeight: '24px',
          padding: '14px 28px',
          position: 'relative',
          overflow: 'hidden',
          zIndex: 1,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(PRIMARY.main, 0.3)}`,
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: PRIMARY.gradient,
            zIndex: -2,
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            top: '50%',
            left: '50%',
            width: '0%',
            height: '0%',
            background: `radial-gradient(circle, ${alpha(PRIMARY.light, 0.4)} 0%, transparent 70%)`,
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: -1,
            transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
          },
          '&:hover': {
            transform: 'translateY(-4px) scale(1.02)',
            boxShadow: `0 20px 40px ${alpha(PRIMARY.main, 0.4)}, 0 0 30px ${PRIMARY.glow}`,
            borderColor: PRIMARY.light,
            '&::before': {
              background: `linear-gradient(135deg, ${PRIMARY.light} 0%, ${PRIMARY.main} 50%, ${PRIMARY.dark} 100%)`,
            },
            '&::after': {
              width: '120%',
              height: '120%',
            },
          },
          '&:active': {
            transform: 'translateY(-2px) scale(0.98)',
            boxShadow: `0 10px 20px ${alpha(PRIMARY.main, 0.3)}`,
          },
        },
        contained: {
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        },
        containedPrimary: {
          color: PRIMARY.contrastText,
        },
        containedSecondary: {
          color: SECONDARY.contrastText,
          '&::before': {
            background: 'linear-gradient(270deg, #F0BC2B 0%, #F7D36E 100%)',
          },
          '&:hover': {
            '&::before': {
              background: 'linear-gradient(270deg, #F7D36E 0%, #F0BC2B 100%)',
            },
          },
        },
        outlined: {
          padding: '11px 23px',
          '&::before': {
            opacity: 0,
            background: 'transparent',
          },
        },
        outlinedPrimary: {
          borderColor: PRIMARY.main,
          '&:hover': {
            backgroundColor: alpha(PRIMARY.main, 0.08),
          },
        },
        outlinedSecondary: {
          borderColor: SECONDARY.main,
          '&:hover': {
            backgroundColor: alpha(SECONDARY.main, 0.08),
          },
        },
        text: {
          padding: '8px 16px',
          '&::before, &::after': {
            display: 'none',
          },
        },
        sizeSmall: {
          padding: '8px 16px',
          fontSize: '0.875rem',
        },
        sizeLarge: {
          padding: '16px 32px',
          fontSize: '1.125rem',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          position: 'relative',
          borderRadius: 20,
          background: `linear-gradient(135deg, ${BACKGROUND.glass} 0%, ${alpha(BACKGROUND.paper, 0.1)} 100%)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(PRIMARY.main, 0.15)}`,
          boxShadow: `0 8px 32px ${alpha('#000', 0.3)}, inset 0 1px 0 ${alpha('#fff', 0.1)}`,
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '1px',
            background: `linear-gradient(90deg, transparent 0%, ${alpha(PRIMARY.light, 0.6)} 50%, transparent 100%)`,
            zIndex: 1,
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '1px',
            height: '100%',
            background: `linear-gradient(180deg, transparent 0%, ${alpha(PRIMARY.light, 0.4)} 50%, transparent 100%)`,
            zIndex: 1,
          },
          '&:hover': {
            transform: 'translateY(-8px) scale(1.02)',
            background: `linear-gradient(135deg, ${BACKGROUND.glassHover} 0%, ${alpha(BACKGROUND.paper, 0.2)} 100%)`,
            borderColor: alpha(PRIMARY.main, 0.3),
            boxShadow: `0 20px 60px ${alpha('#000', 0.4)}, 0 0 40px ${PRIMARY.glow}, inset 0 1px 0 ${alpha('#fff', 0.2)}`,
            '&::before': {
              background: `linear-gradient(90deg, transparent 0%, ${alpha(PRIMARY.light, 0.8)} 50%, transparent 100%)`,
            },
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 24,
        },
      },
    },
    MuiCardHeader: {
      styleOverrides: {
        root: {
          padding: '24px 24px 0',
        },
      },
    },
    MuiCardActions: {
      styleOverrides: {
        root: {
          padding: '0 24px 24px',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
          fontSize: '0.875rem',
        },
        filled: {
          '&.MuiChip-colorPrimary': {
            backgroundColor: alpha(PRIMARY.main, 0.2),
            color: PRIMARY.light,
            border: `1px solid ${alpha(PRIMARY.main, 0.3)}`,
          },
          '&.MuiChip-colorSecondary': {
            backgroundColor: alpha(SECONDARY.main, 0.2),
            color: SECONDARY.light,
            border: `1px solid ${alpha(SECONDARY.main, 0.3)}`,
          },
        },
      },
    },
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: 24,
          paddingRight: 24,
          '@media (min-width: 600px)': {
            paddingLeft: 32,
            paddingRight: 32,
          },
        },
        maxWidthLg: {
          '@media (min-width: 1200px)': {
            maxWidth: 1280,
          },
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: alpha(TEXT.primary, 0.12),
        },
      },
    },
    MuiLink: {
      styleOverrides: {
        root: {
          textDecoration: 'none',
          transition: 'color 0.2s ease-in-out',
          '&:hover': {
            color: PRIMARY.light,
            textDecoration: 'none',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, rgba(29, 20, 41, 0.95) 0%, rgba(29, 20, 41, 0.85) 100%)',
          backdropFilter: 'blur(10px)',
          borderRadius: 16,
        },
        outlined: {
          borderColor: alpha(TEXT.primary, 0.12),
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${alpha(TEXT.primary, 0.12)}`,
        },
        head: {
          fontWeight: 600,
          color: TEXT.primary,
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          backgroundColor: alpha(BACKGROUND.paper, 0.5),
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: PRIMARY.light,
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: PRIMARY.main,
            borderWidth: 2,
          },
        },
        notchedOutline: {
          borderColor: alpha(TEXT.primary, 0.2),
          transition: 'border-color 0.2s ease-in-out',
        },
        input: {
          padding: '16px 14px',
        },
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          fontWeight: 500,
          '&.Mui-focused': {
            color: PRIMARY.main,
          },
        },
        outlined: {
          transform: 'translate(14px, 16px) scale(1)',
          '&.MuiInputLabel-shrink': {
            transform: 'translate(14px, -9px) scale(0.75)',
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          color: TEXT.primary,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            backgroundColor: alpha(TEXT.primary, 0.08),
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backdropFilter: 'blur(10px)',
          backgroundColor: alpha(BACKGROUND.default, 0.8),
          boxShadow: `0 4px 20px ${alpha('#000', 0.1)}`,
          borderBottom: `1px solid ${alpha(PRIMARY.main, 0.1)}`,
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundImage: 'none',
          backgroundColor: BACKGROUND.paper,
          border: 'none',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: alpha(BACKGROUND.dark, 0.9),
          backdropFilter: 'blur(4px)',
          border: `1px solid ${alpha(PRIMARY.main, 0.2)}`,
          borderRadius: 8,
          padding: '8px 12px',
          fontSize: '0.75rem',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        },
        arrow: {
          color: alpha(BACKGROUND.dark, 0.9),
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 16,
          boxShadow: '0 24px 48px rgba(0, 0, 0, 0.2)',
        },
      },
    },
    MuiDialogTitle: {
      styleOverrides: {
        root: {
          padding: '24px 24px 16px',
          fontSize: '1.25rem',
          fontWeight: 600,
        },
      },
    },
    MuiDialogContent: {
      styleOverrides: {
        root: {
          padding: '16px 24px',
        },
      },
    },
    MuiDialogActions: {
      styleOverrides: {
        root: {
          padding: '16px 24px 24px',
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          backgroundColor: PRIMARY.main,
          color: PRIMARY.contrastText,
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 42,
          height: 26,
          padding: 0,
          '& .MuiSwitch-switchBase': {
            padding: 0,
            margin: 2,
            transitionDuration: '300ms',
            '&.Mui-checked': {
              transform: 'translateX(16px)',
              color: '#fff',
              '& + .MuiSwitch-track': {
                backgroundColor: PRIMARY.main,
                opacity: 1,
                border: 0,
              },
              '&.Mui-disabled + .MuiSwitch-track': {
                opacity: 0.5,
              },
            },
            '&.Mui-disabled .MuiSwitch-thumb': {
              color: alpha('#fff', 0.3),
            },
            '&.Mui-disabled + .MuiSwitch-track': {
              opacity: 0.3,
            },
          },
          '& .MuiSwitch-thumb': {
            boxSizing: 'border-box',
            width: 22,
            height: 22,
          },
          '& .MuiSwitch-track': {
            borderRadius: 26 / 2,
            backgroundColor: alpha(TEXT.primary, 0.3),
            opacity: 1,
            transition: 'background-color 500ms',
          },
        },
      },
    },
  },
});

// Apply responsive font sizes
theme = responsiveFontSizes(theme);

export default theme;
