rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Games collection
    match /games/{gameId} {
      // Allow public read access to approved games
      allow read: if resource.data.status == 'approved';
      
      // Allow developers to read their own games regardless of status
      allow read: if request.auth != null && 
                     request.auth.uid == resource.data.developer.uid;
      
      // Allow developers to create and update their own games
      allow create: if request.auth != null && 
                       request.auth.uid == request.resource.data.developer.uid;
      
      allow update: if request.auth != null && 
                       (request.auth.uid == resource.data.developer.uid || 
                        request.auth.token.admin == true);
      
      // Only admins can delete games
      allow delete: if request.auth != null && request.auth.token.admin == true;
    }
    
    // News collection
    match /news/{articleId} {
      // Allow public read access to published articles
      allow read: if resource.data.status == 'published';
      
      // Allow authors to read their own articles regardless of status
      allow read: if request.auth != null && 
                     request.auth.uid == resource.data.author.uid;
      
      // Only admins and editors can write news articles
      allow write: if request.auth != null && 
                      (request.auth.token.admin == true || 
                       request.auth.token.editor == true);
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read and write their own data
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Admins can read all user data
      allow read: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      // Anyone can read reviews
      allow read: if true;
      
      // Authenticated users can create reviews
      allow create: if request.auth != null && 
                       request.auth.uid == request.resource.data.userId;
      
      // Users can update their own reviews
      allow update: if request.auth != null && 
                       (request.auth.uid == resource.data.userId || 
                        request.auth.token.admin == true);
      
      // Users can delete their own reviews, admins can delete any
      allow delete: if request.auth != null && 
                       (request.auth.uid == resource.data.userId || 
                        request.auth.token.admin == true);
    }
    
    // Developers collection
    match /developers/{developerId} {
      // Public read access to verified developers
      allow read: if resource.data.verified == true;
      
      // Developers can read and update their own profile
      allow read, update: if request.auth != null && request.auth.uid == developerId;
      
      // Authenticated users can create developer profiles
      allow create: if request.auth != null && request.auth.uid == developerId;
      
      // Admins have full access
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Categories collection
    match /categories/{categoryId} {
      // Anyone can read categories
      allow read: if true;
      
      // Only admins can manage categories
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Admin collection
    match /admin/{document} {
      // Only admins can access admin documents
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Analytics collection (for tracking)
    match /analytics/{document} {
      // Allow read for authenticated users
      allow read: if request.auth != null;
      
      // Allow write for system operations
      allow write: if true;
    }
  }
}
