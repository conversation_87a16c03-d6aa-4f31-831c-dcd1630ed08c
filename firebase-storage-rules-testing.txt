rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // TEMPORARY TESTING RULES - ALLOW PUBLIC ACCESS
    // WARNING: These rules are for development/testing only!
    // DO NOT USE IN PRODUCTION!
    
    // Allow public read/write access to all files for testing
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}

// IMPORTANT NOTES:
// 1. These rules allow ANYONE to read/write ALL files
// 2. Use ONLY for development/testing
// 3. Replace with secure rules before going to production
// 4. Never use these rules with sensitive files
