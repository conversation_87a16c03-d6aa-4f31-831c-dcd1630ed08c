# Gamestorme Launcher Installation

## System Requirements
- **Windows**: Windows 10/11 (64-bit)
- **macOS**: macOS 10.15+ (Intel & Apple Silicon)
- **Linux**: Ubuntu 18.04+ or equivalent

## Installation Steps

### Windows
1. Download `Gamestorme-Launcher-Setup-2.0.0.exe`
2. Run the installer as administrator
3. Follow the installation wizard
4. Launch from Desktop shortcut or Start Menu

### macOS
1. Download `Gamestorme-Launcher-2.0.0.dmg`
2. Open the DMG file
3. Drag Gamestorme to Applications folder
4. Launch from Applications or Launchpad

### Linux
1. Download `Gamestorme-Launcher-2.0.0.AppImage`
2. Make it executable: `chmod +x Gamestorme-Launcher-2.0.0.AppImage`
3. Run: `./Gamestorme-Launcher-2.0.0.AppImage`

## Features
- **Enhanced Performance**: Native desktop performance
- **Desktop Notifications**: Real-time game and platform updates
- **Offline Capabilities**: Access dashboards without internet
- **Auto Updates**: Automatic launcher updates
- **System Integration**: Native OS integration

## Support
For installation help, visit: https://support.gamestorme.com
