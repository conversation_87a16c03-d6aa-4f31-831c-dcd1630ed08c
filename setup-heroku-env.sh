#!/bin/bash

# Setup Heroku Environment Variables for Stormie AI
echo "🚀 Setting up Heroku environment variables for Stormie AI..."

# Set Hugging Face API Key
heroku config:set HUGGINGFACE_API_KEY=*************************************

# Set Node Environment
heroku config:set NODE_ENV=production

# Optional: Set other AI-related configs
heroku config:set AI_CACHE_DURATION=3600
heroku config:set AI_RATE_LIMIT=100

echo "✅ Heroku environment variables set successfully!"
echo ""
echo "🔍 Current Heroku config:"
heroku config

echo ""
echo "🎯 Next steps:"
echo "1. Deploy to Heroku: git push heroku main"
echo "2. Test Stormie AI: https://your-app.herokuapp.com/test-stormie"
echo "3. Access dashboard: https://your-app.herokuapp.com/developer/dashboard"
