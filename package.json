{"name": "gamestorme", "version": "1.0.0", "description": "GameStorme - Empowering Game Developers Through Innovative Solutions", "main": "public/electron.js", "homepage": "./", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "copyright": "Copyright © 2025 <PERSON>", "scripts": {"dev": "next dev -p 8000", "build": "next build", "build:electron": "ELECTRON_BUILD=true next build", "start": "next start -p 8005", "start:heroku": "next start -p $PORT", "lint": "next lint", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true next build", "heroku-postbuild": "npm run build", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:8000 && electron .\"", "dist": "npm run build:electron && electron-builder --publish=never", "dist:win": "npm run build:electron && electron-builder --win --x64", "dist:mac": "npm run build:electron && electron-builder --mac", "dist:linux": "npm run build:electron && electron-builder --linux", "pack": "npm run build:electron && electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@huggingface/inference": "^2.8.1", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.17.1", "@mui/system": "^5.15.10", "axios": "^1.6.7", "chart.js": "^4.4.9", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "firebase": "^11.7.1", "firebase-admin": "^13.3.0", "framer-motion": "^12.14.0", "helmet": "^7.2.0", "next": "^15.3.2", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-icons": "^5.5.0", "react-intersection-observer": "^9.8.0", "react-toastify": "^10.0.4", "recharts": "^2.15.3", "sharp": "^0.33.2", "swiper": "^11.0.5"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "concurrently": "^8.2.2", "electron": "^28.2.0", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "typescript": "^5.3.3", "wait-on": "^7.2.0"}, "license": "ISC", "engines": {"node": "20.x"}, "overrides": {"glob": "10.3.10"}, "build": {"appId": "com.gamestorme.app", "productName": "GameStorme", "copyright": "Copyright © 2025 <PERSON>", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "public/assets/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "LICENSE.txt", "to": "LICENSE.txt"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/assets/icon.ico", "publisherName": "<PERSON>"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/assets/icon.icns", "category": "public.app-category.games"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "public/assets/icon.png", "category": "Game"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "GameStorme", "uninstallDisplayName": "GameStorme", "license": "LICENSE.txt", "artifactName": "GameStorme-Setup-${version}.${ext}"}, "dmg": {"title": "GameStorme Installer", "artifactName": "GameStorme-${version}.${ext}"}, "appImage": {"artifactName": "GameStorme-${version}.${ext}"}, "deb": {"artifactName": "GameStorme-${version}.${ext}"}}}