/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  images: {
    domains: ['localhost', 'gamestorme-9c0b68273ab5.herokuapp.com'],
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Always disable image optimization for Heroku compatibility
    unoptimized: true,
    // Support all image formats
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Add output configuration for better Heroku compatibility
  output: process.env.ELECTRON_BUILD ? 'export' : 'standalone',
  trailingSlash: process.env.ELECTRON_BUILD ? true : false,
  distDir: process.env.ELECTRON_BUILD ? 'build' : '.next',
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // Handle Electron in production
    if (process.env.ELECTRON_BUILD) {
      config.target = 'electron-renderer';
    }

    return config;
  },
}

module.exports = nextConfig;
