#!/bin/bash

# 🎮 GameStorme Desktop App Build Script
# Creates DMG installer for macOS with complete Game Engine functionality

echo "🚀 Building GameStorme Desktop App with Game Engine..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS. For other platforms, use:"
    echo "  Windows: npm run dist:win"
    echo "  Linux: npm run dist:linux"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
rm -rf build/
rm -rf .next/

# Install dependencies
print_status "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi

print_success "Dependencies installed successfully"

# Build Next.js application for Electron
print_status "Building Next.js application for Electron..."
npm run build:electron

if [ $? -ne 0 ]; then
    print_error "Failed to build Next.js application"
    exit 1
fi

print_success "Next.js application built successfully"

# Create app icons if they don't exist
print_status "Checking app icons..."

ICON_DIR="public/assets"
mkdir -p "$ICON_DIR"

# Create a simple icon if it doesn't exist
if [ ! -f "$ICON_DIR/icon.icns" ]; then
    print_warning "icon.icns not found. Creating placeholder icon..."
    # You would typically use a proper icon here
    # For now, we'll create a placeholder
    touch "$ICON_DIR/icon.icns"
fi

if [ ! -f "$ICON_DIR/icon.ico" ]; then
    print_warning "icon.ico not found. Creating placeholder icon..."
    touch "$ICON_DIR/icon.ico"
fi

if [ ! -f "$ICON_DIR/icon.png" ]; then
    print_warning "icon.png not found. Creating placeholder icon..."
    touch "$ICON_DIR/icon.png"
fi

# Build Electron app for macOS
print_status "Building Electron app for macOS..."
npm run dist:mac

if [ $? -ne 0 ]; then
    print_error "Failed to build Electron app"
    exit 1
fi

print_success "Electron app built successfully"

# Check if DMG was created
DMG_FILE=$(find dist -name "*.dmg" | head -n 1)

if [ -z "$DMG_FILE" ]; then
    print_error "DMG file not found in dist directory"
    exit 1
fi

print_success "DMG installer created: $DMG_FILE"

# Get file size
FILE_SIZE=$(du -h "$DMG_FILE" | cut -f1)
print_status "DMG file size: $FILE_SIZE"

# Create download directory if it doesn't exist
DOWNLOAD_DIR="public/download"
mkdir -p "$DOWNLOAD_DIR"

# Copy DMG to public download directory
DMG_FILENAME=$(basename "$DMG_FILE")
cp "$DMG_FILE" "$DOWNLOAD_DIR/"

print_success "DMG copied to public download directory: $DOWNLOAD_DIR/$DMG_FILENAME"

# Create download info file
cat > "$DOWNLOAD_DIR/latest.json" << EOF
{
  "version": "1.0.0",
  "releaseDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "platforms": {
    "darwin": {
      "url": "/download/$DMG_FILENAME",
      "size": "$FILE_SIZE",
      "type": "dmg"
    }
  },
  "features": [
    "Complete GameStorme platform access",
    "Integrated Game Engine",
    "Game Library Management",
    "Offline game launching",
    "Developer and Gamer dashboards",
    "Real-time synchronization",
    "Auto-updates",
    "Native desktop performance"
  ],
  "requirements": {
    "macOS": "10.14 or later",
    "memory": "4GB RAM minimum",
    "storage": "2GB available space",
    "internet": "Required for initial setup and sync"
  }
}
EOF

print_success "Download info file created: $DOWNLOAD_DIR/latest.json"

# Create README for the download directory
cat > "$DOWNLOAD_DIR/README.md" << EOF
# GameStorme Desktop App Downloads

## Latest Release

- **Version**: 1.0.0
- **Release Date**: $(date +"%Y-%m-%d")
- **File**: $DMG_FILENAME
- **Size**: $FILE_SIZE

## Features

🎮 **Complete Game Engine**
- Install and launch games locally
- Game library management
- Save game synchronization
- Performance optimization

🚀 **Platform Integration**
- Full GameStorme platform access
- Developer dashboard
- Gamer dashboard
- Real-time data sync

🔧 **Desktop Features**
- Native macOS performance
- Offline game launching
- Auto-updates
- System integration

## Installation

1. Download the DMG file
2. Open the DMG file
3. Drag GameStorme to Applications folder
4. Launch GameStorme from Applications
5. Sign in with your GameStorme account

## System Requirements

- macOS 10.14 or later
- 4GB RAM minimum
- 2GB available storage
- Internet connection for setup and sync

## Support

For support, visit: https://gamestorme.com/support
EOF

print_success "README created: $DOWNLOAD_DIR/README.md"

# Display build summary
echo ""
echo "🎉 Build Summary"
echo "================"
echo "✅ Next.js app built for Electron"
echo "✅ Electron app packaged for macOS"
echo "✅ DMG installer created: $DMG_FILENAME"
echo "✅ File size: $FILE_SIZE"
echo "✅ Available at: /download/$DMG_FILENAME"
echo ""
echo "🚀 Your GameStorme Desktop App is ready!"
echo ""
echo "📋 Next Steps:"
echo "1. Test the DMG installer on a clean macOS system"
echo "2. Update your website's download page"
echo "3. Deploy the updated website with the new DMG"
echo "4. Announce the desktop app release"
echo ""
echo "🔗 Download URL: https://yourdomain.com/download/$DMG_FILENAME"
echo ""

# Optional: Open the dist directory
if command -v open &> /dev/null; then
    print_status "Opening dist directory..."
    open dist/
fi

print_success "Build completed successfully! 🎮"
