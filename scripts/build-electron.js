const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building Gamestorme Electron Application...');

// Set environment variable for Electron build
process.env.ELECTRON_BUILD = 'true';

try {
  // Step 1: Clean previous builds
  console.log('🧹 Cleaning previous builds...');
  if (fs.existsSync('build')) {
    fs.rmSync('build', { recursive: true, force: true });
  }
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // Step 2: Build Next.js app for Electron
  console.log('📦 Building Next.js application for Electron...');
  execSync('npm run build', { stdio: 'inherit' });

  // Step 3: Copy Electron files to build directory
  console.log('📁 Copying Electron files...');
  const buildDir = path.join(__dirname, '../build');
  
  // Copy electron.js and preload.js from public to build
  fs.copyFileSync(
    path.join(__dirname, '../public/electron.js'),
    path.join(buildDir, 'electron.js')
  );
  
  fs.copyFileSync(
    path.join(__dirname, '../public/preload.js'),
    path.join(buildDir, 'preload.js')
  );

  // Step 4: Create package.json for the built app
  console.log('📄 Creating package.json for built app...');
  const mainPackageJson = require('../package.json');
  const buildPackageJson = {
    name: mainPackageJson.name,
    version: mainPackageJson.version,
    description: mainPackageJson.description,
    main: 'electron.js',
    author: mainPackageJson.author,
    license: mainPackageJson.license,
    dependencies: {
      // Only include production dependencies needed for Electron
      'electron-updater': '^6.1.7'
    }
  };

  fs.writeFileSync(
    path.join(buildDir, 'package.json'),
    JSON.stringify(buildPackageJson, null, 2)
  );

  // Step 5: Build Electron executable
  console.log('⚡ Building Electron executable...');
  execSync('npx electron-builder', { stdio: 'inherit' });

  console.log('✅ Electron build completed successfully!');
  console.log('📁 Executable files are in the "dist" directory');

  // List the created files
  const distDir = path.join(__dirname, '../dist');
  if (fs.existsSync(distDir)) {
    console.log('\n📄 Created files:');
    fs.readdirSync(distDir).forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`   ${file} (${sizeInMB} MB)`);
    });
  }

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
