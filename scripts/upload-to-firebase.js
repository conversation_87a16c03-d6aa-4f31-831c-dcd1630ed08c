const { initializeApp } = require('firebase/app');
const { getStorage, ref, uploadBytes, getDownloadURL } = require('firebase/storage');
const fs = require('fs');
const path = require('path');

// Firebase configuration (using your actual config)
const firebaseConfig = {
  apiKey: "AIzaSyBKGGJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ", // Replace with your actual API key
  authDomain: "gamestorme-faf42.firebaseapp.com",
  projectId: "gamestorme-faf42",
  storageBucket: "gamestorme-faf42.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const storage = getStorage(app);

console.log('🚀 Real Firebase Upload Script for Gamestorme Launcher');
console.log('⚠️  This script requires actual Firebase credentials');

const uploadLauncherFiles = async () => {
  const distDir = path.join(__dirname, '../dist');

  // Check for actual built files
  if (!fs.existsSync(distDir)) {
    console.log('❌ Dist directory not found. Run "npm run build:electron" first.');
    return;
  }

  // Find executable files in dist directory
  const files = fs.readdirSync(distDir);
  const executableFiles = files.filter(file =>
    file.endsWith('.exe') ||
    file.endsWith('.dmg') ||
    file.endsWith('.AppImage') ||
    file.endsWith('.deb') ||
    file.endsWith('.rpm')
  );

  if (executableFiles.length === 0) {
    console.log('❌ No executable files found in dist directory.');
    console.log('   Available files:', files);
    return;
  }

  console.log(`📁 Found ${executableFiles.length} executable file(s):`, executableFiles);

  console.log('\n📤 Uploading executable files...');

  for (const filename of executableFiles) {
    const filepath = path.join(distDir, filename);

    // Determine platform and content type
    let platform = 'unknown';
    if (filename.endsWith('.exe')) platform = 'windows';
    else if (filename.endsWith('.dmg')) platform = 'mac';
    else if (filename.endsWith('.AppImage')) platform = 'linux';
    else if (filename.endsWith('.deb')) platform = 'linux-deb';
    else if (filename.endsWith('.rpm')) platform = 'linux-rpm';

    try {
      console.log(`📤 Uploading ${filename}...`);

      // Read the file
      const fileBuffer = fs.readFileSync(filepath);
      const fileStats = fs.statSync(filepath);

      // Create storage reference
      const storageRef = ref(storage, `launchers/${filename}`);

      // Upload file with metadata
      const metadata = {
        contentType: 'application/octet-stream',
        customMetadata: {
          platform: platform,
          version: '2.0.0',
          buildDate: new Date().toISOString(),
          size: fileStats.size.toString()
        }
      };

      // Upload the file
      const snapshot = await uploadBytes(storageRef, fileBuffer, metadata);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      console.log(`✅ Uploaded: ${filename}`);
      console.log(`📊 Size: ${Math.round(fileStats.size / 1024 / 1024 * 100) / 100} MB`);
      console.log(`🔗 URL: ${downloadURL}`);
      console.log('');

      // Save URL for reference
      const urlsFile = path.join(distDir, 'firebase-urls.json');
      let urls = {};

      if (fs.existsSync(urlsFile)) {
        urls = JSON.parse(fs.readFileSync(urlsFile, 'utf8'));
      }

      urls[filename] = {
        url: downloadURL,
        platform: platform,
        uploadDate: new Date().toISOString(),
        size: fileStats.size
      };

      fs.writeFileSync(urlsFile, JSON.stringify(urls, null, 2));

    } catch (error) {
      console.error(`❌ Failed to upload ${filename}:`, error.message);
    }
  }

  console.log('🎉 Upload process completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Update DownloadLauncher.tsx with the new URLs');
  console.log('2. Replace the demo download logic with real Firebase downloads');
  console.log('3. Test the download functionality');
  console.log('4. Deploy the updated website');
};

// Instructions for setting up real Firebase upload
console.log('\n🔧 Setup Instructions:');
console.log('1. Replace the firebaseConfig with your actual Firebase config');
console.log('2. Build real Electron executables:');
console.log('   - cp electron-package.json package.json');
console.log('   - npm install');
console.log('   - npm run dist');
console.log('3. Run this script: node scripts/upload-to-firebase.js');
console.log('4. Update DownloadLauncher.tsx with real URLs');

// Example of how to update DownloadLauncher.tsx
const exampleCode = `
// Replace the demo download logic in DownloadLauncher.tsx with:

const handleDownload = async (launcher: LauncherInfo) => {
  setDownloading(true);
  setDownloadError(null);
  setDownloadProgress(0);

  try {
    // Real Firebase download URLs
    const downloadUrls = {
      'Gamestorme-Launcher-Setup-2.0.0.exe': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FGamestorme-Launcher-Setup-2.0.0.exe?alt=media',
      'Gamestorme-Launcher-2.0.0.dmg': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FGamestorme-Launcher-2.0.0.dmg?alt=media',
      'Gamestorme-Launcher-2.0.0.AppImage': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FGamestorme-Launcher-2.0.0.AppImage?alt=media'
    };

    const downloadUrl = downloadUrls[launcher.filename];

    if (downloadUrl) {
      // Direct download from Firebase
      window.open(downloadUrl, '_blank');
      setDownloadSuccess(true);
    } else {
      setDownloadError('Download URL not found');
    }

    setDownloading(false);
  } catch (error) {
    console.error('Download error:', error);
    setDownloadError('Failed to download the launcher. Please try again.');
    setDownloading(false);
  }
};
`;

console.log('\n📝 Example code for real downloads:');
console.log(exampleCode);

// For demo purposes, don't actually run the upload
if (process.argv.includes('--run')) {
  uploadLauncherFiles().catch(console.error);
} else {
  console.log('\n⚠️  This is a template script. To run actual upload:');
  console.log('   node scripts/upload-to-firebase.js --run');
  console.log('\n🔒 Make sure to:');
  console.log('   - Set up proper Firebase authentication');
  console.log('   - Configure Firebase Storage rules');
  console.log('   - Build real Electron executables first');
}

// Create Firebase Storage rules template
const storageRules = `rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Public read access for launcher downloads
    match /launchers/{filename} {
      allow read: if true;
      allow write: if request.auth != null &&
                      request.auth.token.admin == true;
    }

    // Authenticated access for other files
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}`;

fs.writeFileSync(
  path.join(__dirname, '../firebase-storage.rules'),
  storageRules
);

console.log('\n📄 Created firebase-storage.rules file');
console.log('   Apply these rules in Firebase Console > Storage > Rules');
