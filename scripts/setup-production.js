#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up Gamestorme Production Environment...\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.error('❌ Node.js 18 or higher is required. Current version:', nodeVersion);
  process.exit(1);
}

console.log('✅ Node.js version check passed:', nodeVersion);

// Install dependencies
console.log('\n📦 Installing dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Check for .env.local file
console.log('\n🔧 Checking environment configuration...');
const envLocalPath = path.join(process.cwd(), '.env.local');
const envExamplePath = path.join(process.cwd(), '.env.example');

if (!fs.existsSync(envLocalPath)) {
  if (fs.existsSync(envExamplePath)) {
    console.log('📋 Creating .env.local from .env.example...');
    fs.copyFileSync(envExamplePath, envLocalPath);
    console.log('✅ .env.local created');
    console.log('⚠️  Please update .env.local with your actual Firebase credentials');
  } else {
    console.log('⚠️  .env.example not found, creating basic .env.local...');
    const basicEnv = `# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDHz0X-6x78FuzQyGgIeKRy1nfNMw1-_F4
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=gamestorme-faf42.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=gamestorme-faf42
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=gamestorme-faf42.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:01704242f816095f4711f7
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://gamestorme-faf42-default-rtdb.firebaseio.com
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-CBM026VFVM

# Firebase Admin (Server-side) - Get these from Firebase Console > Project Settings > Service Accounts
FIREBASE_ADMIN_PROJECT_ID=gamestorme-faf42
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_HERE\\n-----END PRIVATE KEY-----\\n"

# Application Settings
NODE_ENV=development
PORT=8000
`;
    fs.writeFileSync(envLocalPath, basicEnv);
    console.log('✅ Basic .env.local created');
  }
} else {
  console.log('✅ .env.local already exists');
}

// Check Firebase configuration
console.log('\n🔥 Validating Firebase configuration...');
try {
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_APP_ID',
  ];

  const missingVars = requiredVars.filter(varName => 
    !envContent.includes(varName) || envContent.includes(`${varName}=`)
  );

  if (missingVars.length === 0) {
    console.log('✅ Firebase configuration looks good');
  } else {
    console.log('⚠️  Some Firebase configuration may be missing or incomplete');
    console.log('   Please ensure all Firebase variables are properly set in .env.local');
  }
} catch (error) {
  console.log('⚠️  Could not validate Firebase configuration');
}

// Check if Firebase rules files exist
console.log('\n🔒 Checking Firebase security rules...');
const ruleFiles = [
  'firebase-firestore-rules.txt',
  'firebase-storage-rules.txt',
  'firebase-realtime-rules.txt'
];

ruleFiles.forEach(file => {
  if (fs.existsSync(path.join(process.cwd(), file))) {
    console.log(`✅ ${file} found`);
  } else {
    console.log(`⚠️  ${file} not found`);
  }
});

// Offer to seed database
console.log('\n🌱 Database seeding...');
const seedScript = path.join(process.cwd(), 'scripts', 'seed-database.js');

if (fs.existsSync(seedScript)) {
  console.log('📊 Database seeding script found');
  console.log('   Run "node scripts/seed-database.js" to populate with sample data');
} else {
  console.log('⚠️  Database seeding script not found');
}

// Build the project to check for errors
console.log('\n🔨 Building project to check for errors...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Project builds successfully');
} catch (error) {
  console.log('⚠️  Build encountered issues - check the output above');
  console.log('   This is normal if Firebase credentials are not yet configured');
}

// Final setup summary
console.log('\n🎉 Setup Complete!\n');

console.log('📋 Next Steps:');
console.log('1. 🔧 Update .env.local with your actual Firebase credentials');
console.log('2. 🔒 Apply Firebase security rules from the rule files to your Firebase Console');
console.log('3. 🌱 Run "node scripts/seed-database.js" to populate with sample data (optional)');
console.log('4. 🚀 Run "npm run dev" to start the development server');
console.log('5. 🎮 Visit http://localhost:3000/developer/dashboard to see the production dashboard');

console.log('\n📚 Documentation:');
console.log('- 📖 Developer Dashboard: DEVELOPER_DASHBOARD.md');
console.log('- 🔥 Firebase Setup: FIREBASE_SETUP.md');
console.log('- 🎯 Project Overview: README.md');

console.log('\n🔗 Important URLs:');
console.log('- 🏠 Homepage: http://localhost:3000');
console.log('- 🎮 Games: http://localhost:3000/games');
console.log('- 📰 News: http://localhost:3000/news');
console.log('- 👨‍💻 Developer Dashboard: http://localhost:3000/developer/dashboard');
console.log('- 🤖 Marketing AI: http://localhost:3000/marketing-ai');

console.log('\n🆘 Support:');
console.log('- 📧 Issues: Create a GitHub issue');
console.log('- 📖 Docs: Check the documentation files');
console.log('- 🔧 Config: Verify .env.local settings');

console.log('\n✨ Features Ready:');
console.log('- ✅ Real-time Firebase integration');
console.log('- ✅ Production-ready developer dashboard');
console.log('- ✅ AI-powered marketing insights');
console.log('- ✅ Comprehensive analytics tracking');
console.log('- ✅ Support ticket system');
console.log('- ✅ Financial tracking and reporting');
console.log('- ✅ Community management tools');
console.log('- ✅ Mobile-responsive design');

console.log('\n🚀 Ready to launch! Run "npm run dev" to get started.');
