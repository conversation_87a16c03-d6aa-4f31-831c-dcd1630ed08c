const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix color issues in files
function fixColorIssues() {
  console.log('🎨 Fixing MUI color issues...');

  // Find all TypeScript and TSX files
  const files = glob.sync('{pages,components}/**/*.{ts,tsx}', {
    ignore: ['node_modules/**', '.next/**', 'dist/**']
  });

  let totalFixes = 0;

  files.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;

      // Fix alpha('white', ...) issues
      const alphaWhiteRegex = /alpha\(\s*['"`]white['"`]\s*,/g;
      if (alphaWhiteRegex.test(content)) {
        content = content.replace(alphaWhiteRegex, "alpha('#ffffff',");
        modified = true;
        console.log(`✅ Fixed alpha('white') in: ${filePath}`);
        totalFixes++;
      }

      // Fix bgcolor: 'white' in sx props (only when used with alpha)
      const bgColorWhiteRegex = /bgcolor:\s*['"`]white['"`]/g;
      if (bgColorWhiteRegex.test(content)) {
        // Only replace if it's in a context where it might cause issues
        const lines = content.split('\n');
        let needsReplace = false;
        
        lines.forEach((line, index) => {
          if (line.includes("bgcolor: 'white'") || line.includes('bgcolor: "white"')) {
            // Check if this line or nearby lines use alpha function
            const contextStart = Math.max(0, index - 3);
            const contextEnd = Math.min(lines.length, index + 3);
            const context = lines.slice(contextStart, contextEnd).join('\n');
            
            if (context.includes('alpha(') || context.includes('&:hover')) {
              needsReplace = true;
            }
          }
        });

        if (needsReplace) {
          content = content.replace(bgColorWhiteRegex, "bgcolor: '#ffffff'");
          modified = true;
          console.log(`✅ Fixed bgcolor: 'white' in: ${filePath}`);
          totalFixes++;
        }
      }

      // Fix borderColor: 'white' in similar contexts
      const borderColorWhiteRegex = /borderColor:\s*['"`]white['"`]/g;
      if (borderColorWhiteRegex.test(content)) {
        const lines = content.split('\n');
        let needsReplace = false;
        
        lines.forEach((line, index) => {
          if (line.includes("borderColor: 'white'") || line.includes('borderColor: "white"')) {
            const contextStart = Math.max(0, index - 3);
            const contextEnd = Math.min(lines.length, index + 3);
            const context = lines.slice(contextStart, contextEnd).join('\n');
            
            if (context.includes('alpha(') || context.includes('&:hover')) {
              needsReplace = true;
            }
          }
        });

        if (needsReplace) {
          content = content.replace(borderColorWhiteRegex, "borderColor: '#ffffff'");
          modified = true;
          console.log(`✅ Fixed borderColor: 'white' in: ${filePath}`);
          totalFixes++;
        }
      }

      // Write back the modified content
      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
      }

    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });

  console.log(`\n🎉 Color fix complete! Fixed ${totalFixes} issues.`);
  
  if (totalFixes === 0) {
    console.log('✨ No color issues found - everything looks good!');
  } else {
    console.log('\n📝 Summary of fixes:');
    console.log('- Replaced alpha(\'white\', ...) with alpha(\'#ffffff\', ...)');
    console.log('- Fixed bgcolor and borderColor issues in hover contexts');
    console.log('\n🚀 Your app should now work without MUI color errors!');
  }
}

// Function to validate color usage
function validateColors() {
  console.log('\n🔍 Validating color usage...');
  
  const files = glob.sync('{pages,components}/**/*.{ts,tsx}', {
    ignore: ['node_modules/**', '.next/**', 'dist/**']
  });

  const issues = [];

  files.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for potential issues
      const problematicPatterns = [
        { pattern: /alpha\(\s*['"`]white['"`]\s*,/g, issue: "alpha('white') usage" },
        { pattern: /alpha\(\s*['"`]black['"`]\s*,/g, issue: "alpha('black') usage" },
        { pattern: /alpha\(\s*['"`]red['"`]\s*,/g, issue: "alpha('red') usage" },
        { pattern: /alpha\(\s*['"`]blue['"`]\s*,/g, issue: "alpha('blue') usage" },
        { pattern: /alpha\(\s*['"`]green['"`]\s*,/g, issue: "alpha('green') usage" },
      ];

      problematicPatterns.forEach(({ pattern, issue }) => {
        const matches = content.match(pattern);
        if (matches) {
          issues.push({
            file: filePath,
            issue: issue,
            count: matches.length
          });
        }
      });

    } catch (error) {
      console.error(`❌ Error validating ${filePath}:`, error.message);
    }
  });

  if (issues.length === 0) {
    console.log('✅ No color issues found!');
  } else {
    console.log('⚠️  Found potential color issues:');
    issues.forEach(({ file, issue, count }) => {
      console.log(`   ${file}: ${count}x ${issue}`);
    });
  }

  return issues.length === 0;
}

// Main execution
if (require.main === module) {
  fixColorIssues();
  
  // Validate after fixing
  setTimeout(() => {
    const isValid = validateColors();
    process.exit(isValid ? 0 : 1);
  }, 1000);
}

module.exports = { fixColorIssues, validateColors };
