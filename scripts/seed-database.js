const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: 'gamestorme-faf42',
        clientEmail: '<EMAIL>',
        privateKey: '-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n',
      }),
      databaseURL: 'https://gamestorme-faf42-default-rtdb.firebaseio.com',
      storageBucket: 'gamestorme-faf42.appspot.com',
    });
  } catch (error) {
    console.log('Firebase admin initialization error', error);
  }
}

const db = admin.firestore();

// Sample games data
const sampleGames = [
  {
    title: 'Agueybana',
    description: 'An epic adventure inspired by Puerto Rican history and mythology, featuring the legendary Taíno cacique.',
    longDescription: 'Embark on an epic journey through the rich history and mythology of Puerto Rico in this immersive adventure game. Play as Agueybana, the legendary Taíno cacique, and experience the struggles and triumphs of the indigenous people of Borinquén. Featuring stunning visuals, authentic cultural elements, and engaging gameplay that brings history to life.',
    developer: {
      name: 'Gamestorme Studios',
      email: '<EMAIL>',
      website: 'https://gamestorme.com',
      uid: 'dev_001',
    },
    images: {
      thumbnail: '/game1.jpg',
      screenshots: ['/game1.jpg', '/game2.jpg', '/game3.jpg'],
      banner: '/game1.jpg',
      logo: '/game1.jpg',
    },
    details: {
      genre: ['Adventure', 'Historical', 'Mythology'],
      platforms: ['PC', 'Mobile'],
      releaseDate: '2023-05-15',
      version: '1.2.0',
      size: '2.5 GB',
      rating: 4.8,
      ageRating: 'T',
      languages: ['English', 'Spanish'],
    },
    pricing: {
      price: 29.99,
      currency: 'USD',
      discount: 20,
      isFree: false,
    },
    features: [
      'Rich historical narrative',
      'Authentic cultural elements',
      'Stunning 3D graphics',
      'Multiple language support',
      'Achievement system',
    ],
    systemRequirements: {
      minimum: {
        os: 'Windows 10',
        processor: 'Intel i5-4590 / AMD FX 8350',
        memory: '8 GB RAM',
        graphics: 'NVIDIA GTX 970 / AMD R9 280',
        storage: '3 GB available space',
      },
      recommended: {
        os: 'Windows 11',
        processor: 'Intel i7-8700K / AMD Ryzen 5 3600',
        memory: '16 GB RAM',
        graphics: 'NVIDIA GTX 1070 / AMD RX 580',
        storage: '3 GB available space',
      },
    },
    status: 'approved',
    featured: true,
    tags: ['adventure', 'history', 'mythology', 'puerto-rico'],
    createdAt: new Date('2023-05-01'),
    updatedAt: new Date(),
    approvedAt: new Date('2023-05-10'),
    approvedBy: 'admin_001',
    stats: {
      downloads: 15420,
      views: 45230,
      likes: 1250,
      reviews: 89,
    },
  },
  {
    title: 'Guaramania',
    description: 'Experience the vibrant culture and music of Puerto Rico in this rhythm-based adventure game.',
    longDescription: 'Dive into the heart of Puerto Rican culture with Guaramania, a unique rhythm-based adventure that celebrates the island\'s rich musical heritage. Dance to traditional rhythms, explore colorful environments, and discover the stories behind the music that defines Puerto Rico.',
    developer: {
      name: 'Rhythm Studios',
      email: '<EMAIL>',
      website: 'https://rhythmstudios.com',
      uid: 'dev_002',
    },
    images: {
      thumbnail: '/game2.jpg',
      screenshots: ['/game2.jpg', '/game3.jpg', '/game4.jpg'],
      banner: '/game2.jpg',
      logo: '/game2.jpg',
    },
    details: {
      genre: ['Rhythm', 'Cultural', 'Music'],
      platforms: ['PC', 'Mobile', 'Console'],
      releaseDate: '2023-08-20',
      version: '1.0.5',
      size: '1.8 GB',
      rating: 4.7,
      ageRating: 'E',
      languages: ['English', 'Spanish'],
    },
    pricing: {
      price: 19.99,
      currency: 'USD',
      discount: 0,
      isFree: false,
    },
    features: [
      'Authentic Puerto Rican music',
      'Rhythm-based gameplay',
      'Cultural storytelling',
      'Multiplayer modes',
      'Custom song creation',
    ],
    systemRequirements: {
      minimum: {
        os: 'Windows 10',
        processor: 'Intel i3-6100 / AMD FX 6300',
        memory: '4 GB RAM',
        graphics: 'NVIDIA GTX 750 Ti / AMD R7 260X',
        storage: '2 GB available space',
      },
    },
    status: 'approved',
    featured: true,
    tags: ['rhythm', 'music', 'culture', 'puerto-rico'],
    createdAt: new Date('2023-08-01'),
    updatedAt: new Date(),
    approvedAt: new Date('2023-08-15'),
    approvedBy: 'admin_001',
    stats: {
      downloads: 8750,
      views: 23100,
      likes: 890,
      reviews: 67,
    },
  },
  {
    title: 'Z-Tea Garden',
    description: 'A relaxing simulation game where you manage your own tea garden and create the perfect blends.',
    longDescription: 'Find peace and tranquility in Z-Tea Garden, a meditative simulation game that lets you cultivate your own tea garden. Plant different tea varieties, harvest leaves at the perfect time, and experiment with blending to create unique flavors. Perfect for unwinding after a long day.',
    developer: {
      name: 'Zen Games',
      email: '<EMAIL>',
      website: 'https://zengames.com',
      uid: 'dev_003',
    },
    images: {
      thumbnail: '/game3.jpg',
      screenshots: ['/game3.jpg', '/game4.jpg', '/game5.jpg'],
      banner: '/game3.jpg',
      logo: '/game3.jpg',
    },
    details: {
      genre: ['Simulation', 'Relaxing', 'Management'],
      platforms: ['PC', 'Mobile'],
      releaseDate: '2023-02-28',
      version: '2.1.0',
      size: '1.2 GB',
      rating: 4.4,
      ageRating: 'E',
      languages: ['English', 'Japanese', 'Chinese'],
    },
    pricing: {
      price: 14.99,
      currency: 'USD',
      discount: 25,
      isFree: false,
    },
    features: [
      'Peaceful tea garden simulation',
      'Dozens of tea varieties',
      'Relaxing soundtrack',
      'Seasonal changes',
      'Tea blending mechanics',
    ],
    systemRequirements: {
      minimum: {
        os: 'Windows 10',
        processor: 'Intel i3-4130 / AMD FX 4300',
        memory: '4 GB RAM',
        graphics: 'Intel HD 4000 / AMD R5 Graphics',
        storage: '1.5 GB available space',
      },
    },
    status: 'approved',
    featured: false,
    tags: ['simulation', 'relaxing', 'tea', 'zen'],
    createdAt: new Date('2023-02-01'),
    updatedAt: new Date(),
    approvedAt: new Date('2023-02-20'),
    approvedBy: 'admin_001',
    stats: {
      downloads: 12300,
      views: 18900,
      likes: 670,
      reviews: 45,
    },
  },
];

// Sample news articles
const sampleNews = [
  {
    title: 'Gamestorme Launches Revolutionary AI Marketing Platform',
    content: '<p>Today marks a significant milestone in gaming history as Gamestorme officially launches its revolutionary AI marketing platform, designed to empower game developers with unprecedented insights and automation capabilities.</p><p>The platform, powered by our proprietary Stormie AI technology, offers developers a comprehensive suite of tools including market analysis, ASO optimization, social media content generation, and pricing strategy recommendations.</p><p>"This is just the beginning of what we believe will be a transformative era for game marketing," said Joel Rodriguez, CEO of Gamestorme. "Our AI doesn\'t just analyze data – it learns from every interaction and continuously improves its recommendations."</p>',
    excerpt: 'Gamestorme unveils its groundbreaking AI marketing platform, featuring Stormie AI technology that learns and adapts to provide developers with intelligent marketing insights.',
    author: {
      name: 'Sarah Chen',
      email: '<EMAIL>',
      avatar: '/team-culture.jpg',
    },
    images: {
      featured: '/marketing-ai.jpg',
      gallery: ['/marketing-ai.jpg'],
    },
    category: 'Technology',
    tags: ['AI', 'Marketing', 'Platform', 'Launch'],
    status: 'published',
    featured: true,
    publishedAt: new Date('2024-12-20'),
    createdAt: new Date('2024-12-19'),
    updatedAt: new Date(),
    seo: {
      metaTitle: 'Gamestorme Launches AI Marketing Platform | Gaming News',
      metaDescription: 'Discover how Gamestorme\'s new AI marketing platform is revolutionizing game development and marketing.',
      keywords: ['AI marketing', 'game development', 'Stormie AI', 'gaming platform'],
    },
    stats: {
      views: 5420,
      likes: 234,
      shares: 89,
    },
  },
  {
    title: 'The Future of Gaming: Blockchain Integration and Digital Ownership',
    content: '<p>The gaming industry is on the brink of a revolutionary transformation as blockchain technology begins to reshape how we think about digital ownership, in-game assets, and player experiences.</p><p>At Gamestorme, we\'re at the forefront of this evolution, exploring innovative ways to integrate blockchain technology into our platform while maintaining the seamless user experience our community expects.</p><p>Digital ownership represents more than just owning virtual items – it\'s about giving players true control over their gaming investments and creating new economic opportunities within gaming ecosystems.</p>',
    excerpt: 'Exploring how blockchain technology is transforming gaming through true digital ownership and new economic models for players and developers.',
    author: {
      name: 'Alex Rodriguez',
      email: '<EMAIL>',
      avatar: '/team-culture.jpg',
    },
    images: {
      featured: '/gaming-experience.jpg',
      gallery: ['/gaming-experience.jpg'],
    },
    category: 'Blockchain',
    tags: ['Blockchain', 'NFT', 'Digital Ownership', 'Future'],
    status: 'published',
    featured: true,
    publishedAt: new Date('2024-12-15'),
    createdAt: new Date('2024-12-14'),
    updatedAt: new Date(),
    seo: {
      metaTitle: 'Blockchain Gaming: The Future of Digital Ownership',
      metaDescription: 'Learn about the future of gaming with blockchain technology and digital ownership.',
      keywords: ['blockchain gaming', 'digital ownership', 'NFT games', 'gaming future'],
    },
    stats: {
      views: 3890,
      likes: 156,
      shares: 67,
    },
  },
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Seed games
    console.log('📱 Seeding games...');
    for (const game of sampleGames) {
      await db.collection('games').add(game);
      console.log(`✅ Added game: ${game.title}`);
    }

    // Seed news articles
    console.log('📰 Seeding news articles...');
    for (const article of sampleNews) {
      await db.collection('news').add(article);
      console.log(`✅ Added article: ${article.title}`);
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Games added: ${sampleGames.length}`);
    console.log(`- News articles added: ${sampleNews.length}`);
    console.log('\n🔗 Next steps:');
    console.log('1. Update your .env file with actual Firebase credentials');
    console.log('2. Run the development server: npm run dev');
    console.log('3. Visit /games and /news to see the seeded data');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

// Run the seeding function
if (require.main === module) {
  seedDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedDatabase };
