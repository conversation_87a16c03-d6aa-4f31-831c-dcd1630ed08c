const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: 'gamestorme-faf42',
        clientEmail: '<EMAIL>',
        privateKey: '-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n',
      }),
      databaseURL: 'https://gamestorme-faf42-default-rtdb.firebaseio.com',
      storageBucket: 'gamestorme-faf42.appspot.com',
    });
  } catch (error) {
    console.log('Firebase admin initialization error', error);
  }
}

const db = admin.firestore();

// Sample games data
const sampleGames = [
  {
    title: 'Agueybana',
    description: 'An epic adventure inspired by Puerto Rican history and mythology, featuring the legendary Taíno cacique.',
    longDescription: 'Embark on an epic journey through the rich history and mythology of Puerto Rico in this immersive adventure game. Play as Agueybana, the legendary Taíno cacique, and experience the struggles and triumphs of the indigenous people of Borinquén. Featuring stunning visuals, authentic cultural elements, and engaging gameplay that brings history to life.',
    developer: {
      name: 'Gamestorme Studios',
      email: '<EMAIL>',
      website: 'https://gamestorme.com',
      uid: 'dev_001',
    },
    images: {
      thumbnail: '/game1.jpg',
      screenshots: ['/game1.jpg', '/game2.jpg', '/game3.jpg'],
      banner: '/game1.jpg',
      logo: '/game1.jpg',
    },
    details: {
      genre: ['Adventure', 'Historical', 'Mythology'],
      platforms: ['PC', 'Mobile'],
      releaseDate: '2023-05-15',
      version: '1.2.0',
      size: '2.5 GB',
      rating: 4.8,
      ageRating: 'T',
      languages: ['English', 'Spanish'],
    },
    pricing: {
      price: 29.99,
      currency: 'USD',
      discount: 20,
      isFree: false,
    },
    features: [
      'Rich historical narrative',
      'Authentic cultural elements',
      'Stunning 3D graphics',
      'Multiple language support',
      'Achievement system',
    ],
    systemRequirements: {
      minimum: {
        os: 'Windows 10',
        processor: 'Intel i5-4590 / AMD FX 8350',
        memory: '8 GB RAM',
        graphics: 'NVIDIA GTX 970 / AMD R9 280',
        storage: '3 GB available space',
      },
      recommended: {
        os: 'Windows 11',
        processor: 'Intel i7-8700K / AMD Ryzen 5 3600',
        memory: '16 GB RAM',
        graphics: 'NVIDIA GTX 1070 / AMD RX 580',
        storage: '3 GB available space',
      },
    },
    status: 'approved',
    featured: true,
    tags: ['adventure', 'history', 'mythology', 'puerto-rico'],
    createdAt: new Date('2023-05-01'),
    updatedAt: new Date(),
    approvedAt: new Date('2023-05-10'),
    approvedBy: 'admin_001',
    stats: {
      downloads: 15420,
      views: 45230,
      likes: 1250,
      reviews: 89,
    },
  },
  {
    title: 'Guaramania',
    description: 'Experience the vibrant culture and music of Puerto Rico in this rhythm-based adventure game.',
    longDescription: 'Dive into the heart of Puerto Rican culture with Guaramania, a unique rhythm-based adventure that celebrates the island\'s rich musical heritage. Dance to traditional rhythms, explore colorful environments, and discover the stories behind the music that defines Puerto Rico.',
    developer: {
      name: 'Rhythm Studios',
      email: '<EMAIL>',
      website: 'https://rhythmstudios.com',
      uid: 'dev_002',
    },
    images: {
      thumbnail: '/game2.jpg',
      screenshots: ['/game2.jpg', '/game3.jpg', '/game4.jpg'],
      banner: '/game2.jpg',
      logo: '/game2.jpg',
    },
    details: {
      genre: ['Rhythm', 'Cultural', 'Music'],
      platforms: ['PC', 'Mobile', 'Console'],
      releaseDate: '2023-08-20',
      version: '1.0.5',
      size: '1.8 GB',
      rating: 4.7,
      ageRating: 'E',
      languages: ['English', 'Spanish'],
    },
    pricing: {
      price: 19.99,
      currency: 'USD',
      discount: 0,
      isFree: false,
    },
    features: [
      'Authentic Puerto Rican music',
      'Rhythm-based gameplay',
      'Cultural storytelling',
      'Multiplayer modes',
      'Custom song creation',
    ],
    systemRequirements: {
      minimum: {
        os: 'Windows 10',
        processor: 'Intel i3-6100 / AMD FX 6300',
        memory: '4 GB RAM',
        graphics: 'NVIDIA GTX 750 Ti / AMD R7 260X',
        storage: '2 GB available space',
      },
    },
    status: 'approved',
    featured: true,
    tags: ['rhythm', 'music', 'culture', 'puerto-rico'],
    createdAt: new Date('2023-08-01'),
    updatedAt: new Date(),
    approvedAt: new Date('2023-08-15'),
    approvedBy: 'admin_001',
    stats: {
      downloads: 8750,
      views: 23100,
      likes: 890,
      reviews: 67,
    },
  },
  {
    title: 'Z-Tea Garden',
    description: 'A relaxing simulation game where you manage your own tea garden and create the perfect blends.',
    longDescription: 'Find peace and tranquility in Z-Tea Garden, a meditative simulation game that lets you cultivate your own tea garden. Plant different tea varieties, harvest leaves at the perfect time, and experiment with blending to create unique flavors. Perfect for unwinding after a long day.',
    developer: {
      name: 'Zen Games',
      email: '<EMAIL>',
      website: 'https://zengames.com',
      uid: 'dev_003',
    },
    images: {
      thumbnail: '/game3.jpg',
      screenshots: ['/game3.jpg', '/game4.jpg', '/game5.jpg'],
      banner: '/game3.jpg',
      logo: '/game3.jpg',
    },
    details: {
      genre: ['Simulation', 'Relaxing', 'Management'],
      platforms: ['PC', 'Mobile'],
      releaseDate: '2023-02-28',
      version: '2.1.0',
      size: '1.2 GB',
      rating: 4.4,
      ageRating: 'E',
      languages: ['English', 'Japanese', 'Chinese'],
    },
    pricing: {
      price: 14.99,
      currency: 'USD',
      discount: 25,
      isFree: false,
    },
    features: [
      'Peaceful tea garden simulation',
      'Dozens of tea varieties',
      'Relaxing soundtrack',
      'Seasonal changes',
      'Tea blending mechanics',
    ],
    systemRequirements: {
      minimum: {
        os: 'Windows 10',
        processor: 'Intel i3-4130 / AMD FX 4300',
        memory: '4 GB RAM',
        graphics: 'Intel HD 4000 / AMD R5 Graphics',
        storage: '1.5 GB available space',
      },
    },
    status: 'approved',
    featured: false,
    tags: ['simulation', 'relaxing', 'tea', 'zen'],
    createdAt: new Date('2023-02-01'),
    updatedAt: new Date(),
    approvedAt: new Date('2023-02-20'),
    approvedBy: 'admin_001',
    stats: {
      downloads: 12300,
      views: 18900,
      likes: 670,
      reviews: 45,
    },
  },
];

// Sample news articles
const sampleNews = [
  {
    title: 'Gamestorme Launches Revolutionary AI Marketing Platform',
    content: '<p>Today marks a significant milestone in gaming history as Gamestorme officially launches its revolutionary AI marketing platform, designed to empower game developers with unprecedented insights and automation capabilities.</p><p>The platform, powered by our proprietary Stormie AI technology, offers developers a comprehensive suite of tools including market analysis, ASO optimization, social media content generation, and pricing strategy recommendations.</p><p>"This is just the beginning of what we believe will be a transformative era for game marketing," said Joel Rodriguez, CEO of Gamestorme. "Our AI doesn\'t just analyze data – it learns from every interaction and continuously improves its recommendations."</p>',
    excerpt: 'Gamestorme unveils its groundbreaking AI marketing platform, featuring Stormie AI technology that learns and adapts to provide developers with intelligent marketing insights.',
    author: {
      name: 'Sarah Chen',
      email: '<EMAIL>',
      avatar: '/team-culture.jpg',
    },
    images: {
      featured: '/marketing-ai.jpg',
      gallery: ['/marketing-ai.jpg'],
    },
    category: 'Technology',
    tags: ['AI', 'Marketing', 'Platform', 'Launch'],
    status: 'published',
    featured: true,
    publishedAt: new Date('2024-12-20'),
    createdAt: new Date('2024-12-19'),
    updatedAt: new Date(),
    seo: {
      metaTitle: 'Gamestorme Launches AI Marketing Platform | Gaming News',
      metaDescription: 'Discover how Gamestorme\'s new AI marketing platform is revolutionizing game development and marketing.',
      keywords: ['AI marketing', 'game development', 'Stormie AI', 'gaming platform'],
    },
    stats: {
      views: 5420,
      likes: 234,
      shares: 89,
    },
  },
  {
    title: 'The Future of Gaming: Blockchain Integration and Digital Ownership',
    content: '<p>The gaming industry is on the brink of a revolutionary transformation as blockchain technology begins to reshape how we think about digital ownership, in-game assets, and player experiences.</p><p>At Gamestorme, we\'re at the forefront of this evolution, exploring innovative ways to integrate blockchain technology into our platform while maintaining the seamless user experience our community expects.</p><p>Digital ownership represents more than just owning virtual items – it\'s about giving players true control over their gaming investments and creating new economic opportunities within gaming ecosystems.</p>',
    excerpt: 'Exploring how blockchain technology is transforming gaming through true digital ownership and new economic models for players and developers.',
    author: {
      name: 'Alex Rodriguez',
      email: '<EMAIL>',
      avatar: '/team-culture.jpg',
    },
    images: {
      featured: '/gaming-experience.jpg',
      gallery: ['/gaming-experience.jpg'],
    },
    category: 'Blockchain',
    tags: ['Blockchain', 'NFT', 'Digital Ownership', 'Future'],
    status: 'published',
    featured: true,
    publishedAt: new Date('2024-12-15'),
    createdAt: new Date('2024-12-14'),
    updatedAt: new Date(),
    seo: {
      metaTitle: 'Blockchain Gaming: The Future of Digital Ownership',
      metaDescription: 'Learn about the future of gaming with blockchain technology and digital ownership.',
      keywords: ['blockchain gaming', 'digital ownership', 'NFT games', 'gaming future'],
    },
    stats: {
      views: 3890,
      likes: 156,
      shares: 67,
    },
  },
];

// Sample support tickets
const sampleSupportTickets = [
  {
    title: 'Game Upload Issue',
    description: 'I\'m having trouble uploading my game files. The upload keeps failing at 80%.',
    status: 'open',
    priority: 'high',
    category: 'technical',
    developerId: 'dev_001',
    developerName: 'Gamestorme Studios',
    developerEmail: '<EMAIL>',
    tags: ['upload', 'technical'],
    escalated: false,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
  },
  {
    title: 'Payment Not Received',
    description: 'I haven\'t received my payment for last month\'s sales. Can you please check?',
    status: 'in-progress',
    priority: 'medium',
    category: 'billing',
    developerId: 'dev_002',
    developerName: 'Rhythm Studios',
    developerEmail: '<EMAIL>',
    adminResponse: 'We\'re looking into your payment issue. Our finance team is reviewing your account.',
    adminId: 'admin_001',
    tags: ['payment', 'billing'],
    escalated: false,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
  },
  {
    title: 'Feature Request: Analytics Export',
    description: 'It would be great to have an option to export analytics data to CSV format.',
    status: 'resolved',
    priority: 'low',
    category: 'feature-request',
    developerId: 'dev_003',
    developerName: 'Zen Games',
    developerEmail: '<EMAIL>',
    adminResponse: 'Great suggestion! We\'ve added this to our roadmap and will implement it in the next quarter.',
    adminId: 'admin_001',
    tags: ['feature-request', 'analytics'],
    escalated: false,
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    resolvedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
  },
];

// Sample analytics events
const sampleAnalyticsEvents = [
  {
    type: 'game_view',
    gameId: 'game_001',
    developerId: 'dev_001',
    sessionId: 'session_001',
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    metadata: {
      page: '/games/game_001',
      referrer: 'https://google.com',
      device: { type: 'desktop', os: 'Windows', browser: 'Chrome' },
    },
  },
  {
    type: 'game_download',
    gameId: 'game_001',
    developerId: 'dev_001',
    sessionId: 'session_001',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    metadata: {
      page: '/games/game_001',
      device: { type: 'desktop', os: 'Windows', browser: 'Chrome' },
    },
  },
  {
    type: 'purchase',
    gameId: 'game_001',
    developerId: 'dev_001',
    sessionId: 'session_002',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    metadata: {
      purchaseAmount: 29.99,
      device: { type: 'mobile', os: 'iOS', browser: 'Safari' },
    },
  },
];

// Sample platform metrics
const samplePlatformMetrics = [
  {
    date: new Date().toISOString().split('T')[0],
    totalViews: 15420,
    totalDownloads: 3240,
    totalUsers: 8750,
    totalSessions: 12300,
    totalRevenue: 45600,
    uniqueUsers: 7200,
    activeSessions: 450,
    updatedAt: new Date(),
  },
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting comprehensive database seeding...');

    // Seed games
    console.log('📱 Seeding games...');
    for (const game of sampleGames) {
      await db.collection('games').add(game);
      console.log(`✅ Added game: ${game.title}`);
    }

    // Seed news articles
    console.log('📰 Seeding news articles...');
    for (const article of sampleNews) {
      await db.collection('news').add(article);
      console.log(`✅ Added article: ${article.title}`);
    }

    // Seed support tickets
    console.log('🎫 Seeding support tickets...');
    for (const ticket of sampleSupportTickets) {
      await db.collection('supportTickets').add(ticket);
      console.log(`✅ Added support ticket: ${ticket.title}`);
    }

    // Seed analytics events
    console.log('📊 Seeding analytics events...');
    for (const event of sampleAnalyticsEvents) {
      await db.collection('analyticsEvents').add(event);
      console.log(`✅ Added analytics event: ${event.type}`);
    }

    // Seed platform metrics
    console.log('📈 Seeding platform metrics...');
    for (const metrics of samplePlatformMetrics) {
      await db.collection('platformMetrics').add(metrics);
      console.log(`✅ Added platform metrics for: ${metrics.date}`);
    }

    // Create developer profiles
    console.log('👨‍💻 Creating developer profiles...');
    const developers = [
      {
        uid: 'dev_001',
        email: '<EMAIL>',
        name: 'Gamestorme Studios',
        verified: true,
        games: ['game_001'],
        stats: {
          totalGames: 1,
          totalDownloads: 15420,
          averageRating: 4.8,
        },
        createdAt: new Date('2023-05-01'),
        updatedAt: new Date(),
      },
      {
        uid: 'dev_002',
        email: '<EMAIL>',
        name: 'Rhythm Studios',
        verified: true,
        games: ['game_002'],
        stats: {
          totalGames: 1,
          totalDownloads: 8750,
          averageRating: 4.7,
        },
        createdAt: new Date('2023-08-01'),
        updatedAt: new Date(),
      },
      {
        uid: 'dev_003',
        email: '<EMAIL>',
        name: 'Zen Games',
        verified: true,
        games: ['game_003'],
        stats: {
          totalGames: 1,
          totalDownloads: 12300,
          averageRating: 4.4,
        },
        createdAt: new Date('2023-02-01'),
        updatedAt: new Date(),
      },
    ];

    for (const developer of developers) {
      await db.collection('developers').doc(developer.uid).set(developer);
      console.log(`✅ Added developer: ${developer.name}`);
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Games added: ${sampleGames.length}`);
    console.log(`- News articles added: ${sampleNews.length}`);
    console.log(`- Support tickets added: ${sampleSupportTickets.length}`);
    console.log(`- Analytics events added: ${sampleAnalyticsEvents.length}`);
    console.log(`- Platform metrics added: ${samplePlatformMetrics.length}`);
    console.log(`- Developer profiles added: ${developers.length}`);
    console.log('\n🔗 Next steps:');
    console.log('1. Update your .env file with actual Firebase credentials');
    console.log('2. Apply Firebase security rules from the rule files');
    console.log('3. Run the development server: npm run dev');
    console.log('4. Visit /developer/dashboard to see the production-ready dashboard');
    console.log('5. Test real-time analytics and support features');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

// Run the seeding function
if (require.main === module) {
  seedDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedDatabase };
