const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: 'gamestorme-faf42',
      clientEmail: '<EMAIL>',
      privateKey: '-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n',
    }),
  });
}

async function setAdminClaims(email) {
  try {
    // Get user by email
    const user = await admin.auth().getUserByEmail(email);
    
    // Set custom claims
    await admin.auth().setCustomUserClaims(user.uid, {
      admin: true,
      editor: true,
      developer: true,
      system: true
    });
    
    console.log(`✅ Admin claims set for user: ${email}`);
    console.log(`User UID: ${user.uid}`);
    
  } catch (error) {
    console.error('❌ Error setting admin claims:', error);
  }
}

// Usage: node scripts/set-admin-claims.js
// Replace with your admin email
const adminEmail = '<EMAIL>';
setAdminClaims(adminEmail).then(() => {
  process.exit(0);
});

module.exports = { setAdminClaims };
