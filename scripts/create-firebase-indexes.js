#!/usr/bin/env node

// 🔥 Firebase Index Creation Script
// Automatically creates required composite indexes for Gamestorme

const { exec } = require('child_process');
const path = require('path');

console.log('🔥 Creating Firebase Composite Indexes for Gamestorme...\n');

// Required indexes for the application
const indexes = [
  {
    collection: 'games',
    fields: [
      { field: 'developer.uid', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Games by developer with creation date sorting'
  },
  {
    collection: 'supportTickets',
    fields: [
      { field: 'developerId', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Support tickets by developer with date sorting'
  },
  {
    collection: 'notifications',
    fields: [
      { field: 'userId', order: 'ASCENDING' },
      { field: 'read', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Notifications by user and read status with date sorting'
  },
  {
    collection: 'games',
    fields: [
      { field: 'status', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Games by status with creation date sorting'
  },
  {
    collection: 'games',
    fields: [
      { field: 'featured', order: 'ASCENDING' },
      { field: 'createdAt', order: 'DESCENDING' }
    ],
    description: 'Featured games with creation date sorting'
  }
];

// Function to create a single index
function createIndex(index) {
  return new Promise((resolve, reject) => {
    const fieldsStr = index.fields.map(f => `${f.field}:${f.order.toLowerCase()}`).join(',');
    const command = `firebase firestore:indexes:create --collection-group=${index.collection} --field-config=${fieldsStr}`;
    
    console.log(`📝 Creating index for ${index.collection}: ${index.description}`);
    console.log(`   Command: ${command}\n`);
    
    exec(command, { cwd: path.join(__dirname, '..') }, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error creating index for ${index.collection}:`, error.message);
        reject(error);
        return;
      }
      
      if (stderr) {
        console.warn(`⚠️ Warning for ${index.collection}:`, stderr);
      }
      
      console.log(`✅ Index created for ${index.collection}`);
      console.log(stdout);
      resolve();
    });
  });
}

// Function to check if Firebase CLI is available
function checkFirebaseCLI() {
  return new Promise((resolve, reject) => {
    exec('firebase --version', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Firebase CLI not found. Please install it first:');
        console.error('   npm install -g firebase-tools');
        reject(error);
        return;
      }
      
      console.log(`✅ Firebase CLI version: ${stdout.trim()}`);
      resolve();
    });
  });
}

// Function to check if user is logged in
function checkFirebaseAuth() {
  return new Promise((resolve, reject) => {
    exec('firebase projects:list', { cwd: path.join(__dirname, '..') }, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Not logged in to Firebase. Please run:');
        console.error('   firebase login');
        reject(error);
        return;
      }
      
      if (stdout.includes('gamestorme-faf42')) {
        console.log('✅ Connected to Gamestorme project');
        resolve();
      } else {
        console.error('❌ Gamestorme project not found. Please run:');
        console.error('   firebase use gamestorme-faf42');
        reject(new Error('Project not found'));
      }
    });
  });
}

// Main execution function
async function main() {
  try {
    // Check prerequisites
    await checkFirebaseCLI();
    await checkFirebaseAuth();
    
    console.log('\n🚀 Starting index creation...\n');
    
    // Create indexes sequentially to avoid conflicts
    for (const index of indexes) {
      try {
        await createIndex(index);
        // Wait a bit between index creations
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`❌ Failed to create index for ${index.collection}:`, error.message);
        // Continue with other indexes
      }
    }
    
    console.log('\n🎉 Index creation process completed!');
    console.log('\n📝 Note: Indexes may take a few minutes to build in Firebase.');
    console.log('   You can check their status in the Firebase Console:');
    console.log('   https://console.firebase.google.com/project/gamestorme-faf42/firestore/indexes');
    
  } catch (error) {
    console.error('\n❌ Failed to create indexes:', error.message);
    process.exit(1);
  }
}

// Alternative: Direct Firebase Console URLs for manual creation
function printManualInstructions() {
  console.log('\n🔗 Manual Index Creation URLs:');
  console.log('   If the automatic creation fails, you can create indexes manually:');
  console.log('');
  
  indexes.forEach((index, i) => {
    console.log(`${i + 1}. ${index.description}:`);
    console.log(`   Collection: ${index.collection}`);
    console.log(`   Fields: ${index.fields.map(f => `${f.field} (${f.order})`).join(', ')}`);
    console.log('');
  });
  
  console.log('   Firebase Console: https://console.firebase.google.com/project/gamestorme-faf42/firestore/indexes');
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('\n❌ Script failed:', error.message);
    printManualInstructions();
    process.exit(1);
  });
}

module.exports = { createIndex, indexes };
