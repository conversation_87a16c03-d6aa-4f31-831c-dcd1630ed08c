/**
 * This script generates placeholder images for the dashboard
 */

const fs = require('fs');
const path = require('path');

// Create directory if it doesn't exist
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    console.log(`Created directory: ${directory}`);
  }
}

// Generate a simple SVG placeholder image
function generatePlaceholderSVG(width, height, text, backgroundColor) {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${backgroundColor}" />
    <text x="50%" y="50%" font-family="Arial" font-size="24" fill="white" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`;
}

// Main function to generate placeholder images
function generatePlaceholders() {
  const publicDir = path.join(process.cwd(), 'public');
  ensureDirectoryExists(publicDir);

  // Generate game placeholder images
  const placeholders = [
    { filename: 'game1.jpg', text: 'Cosmic Odyssey', color: '#4229BC' },
    { filename: 'game2.jpg', text: 'Neon Racer', color: '#E91E63' },
    { filename: 'game3.jpg', text: 'Mystic Realms', color: '#009688' },
  ];

  placeholders.forEach(({ filename, text, color }) => {
    const filePath = path.join(publicDir, filename);
    const svgContent = generatePlaceholderSVG(300, 200, text, color);
    
    fs.writeFileSync(filePath, svgContent);
    console.log(`Generated placeholder image: ${filename}`);
  });

  console.log('All placeholder images generated successfully!');
}

// Run the function
generatePlaceholders();
