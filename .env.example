# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDHz0X-6x78FuzQyGgIeKRy1nfNMw1-_F4
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=gamestorme-faf42.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=gamestorme-faf42
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=gamestorme-faf42.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=206243766870
NEXT_PUBLIC_FIREBASE_APP_ID=1:206243766870:web:BH_jAxT4SxYWYH7E5gM27vhopdr_J7Hvq5xpVL1Pyuf1hHicDkpo8gs_fegcoAQSKHMudrodk0GrzeLbupRchls
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://gamestorme-faf42-default-rtdb.firebaseio.com

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PROJECT_ID=gamestorme-faf42
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"

# Stormie AI Configuration
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Optional: Alternative AI APIs
OPENAI_API_KEY=your_openai_api_key
GROQ_API_KEY=your_groq_api_key

# Application Settings
NODE_ENV=development
PORT=8000
