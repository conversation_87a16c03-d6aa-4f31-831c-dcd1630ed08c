<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="careerBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="50%" style="stop-color:#0f3460;stop-opacity:0.8" />
      <stop offset="75%" style="stop-color:#533483;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="careerAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.6" />
    </linearGradient>
    <filter id="careerGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="techPattern" width="60" height="60" patternUnits="userSpaceOnUse">
      <circle cx="30" cy="30" r="1" fill="#4ecdc4" opacity="0.1"/>
      <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#45b7d1" stroke-width="0.5" opacity="0.05"/>
    </pattern>
  </defs>
  
  <!-- Base background -->
  <rect width="1200" height="800" fill="url(#careerBgGradient)"/>
  
  <!-- Tech pattern overlay -->
  <rect width="1200" height="800" fill="url(#techPattern)"/>
  
  <!-- Career/professional themed elements -->
  <g opacity="0.4">
    <!-- Office building silhouettes -->
    <rect x="50" y="300" width="80" height="200" fill="url(#careerAccent)" opacity="0.3"/>
    <rect x="60" y="320" width="10" height="15" fill="#4ecdc4" opacity="0.6"/>
    <rect x="80" y="320" width="10" height="15" fill="#4ecdc4" opacity="0.6"/>
    <rect x="100" y="320" width="10" height="15" fill="#4ecdc4" opacity="0.6"/>
    <rect x="60" y="350" width="10" height="15" fill="#45b7d1" opacity="0.6"/>
    <rect x="80" y="350" width="10" height="15" fill="#45b7d1" opacity="0.6"/>
    <rect x="100" y="350" width="10" height="15" fill="#45b7d1" opacity="0.6"/>
    
    <rect x="1000" y="250" width="100" height="250" fill="url(#careerAccent)" opacity="0.3"/>
    <rect x="1010" y="270" width="12" height="18" fill="#ff6b6b" opacity="0.6"/>
    <rect x="1030" y="270" width="12" height="18" fill="#ff6b6b" opacity="0.6"/>
    <rect x="1050" y="270" width="12" height="18" fill="#ff6b6b" opacity="0.6"/>
    <rect x="1070" y="270" width="12" height="18" fill="#ff6b6b" opacity="0.6"/>
    
    <!-- Network/connection lines -->
    <g stroke="#4ecdc4" stroke-width="1" fill="none" opacity="0.3">
      <path d="M 200 200 Q 400 150 600 200"/>
      <path d="M 300 400 Q 500 350 700 400"/>
      <path d="M 400 600 Q 600 550 800 600"/>
      <circle cx="200" cy="200" r="3" fill="#4ecdc4"/>
      <circle cx="600" cy="200" r="3" fill="#4ecdc4"/>
      <circle cx="300" cy="400" r="3" fill="#45b7d1"/>
      <circle cx="700" cy="400" r="3" fill="#45b7d1"/>
      <circle cx="400" cy="600" r="3" fill="#ff6b6b"/>
      <circle cx="800" cy="600" r="3" fill="#ff6b6b"/>
    </g>
  </g>
  
  <!-- Central focus area -->
  <g transform="translate(600, 400)" opacity="0.5">
    <!-- Large circular design -->
    <circle cx="0" cy="0" r="150" fill="none" stroke="url(#careerAccent)" stroke-width="2" opacity="0.4"/>
    <circle cx="0" cy="0" r="100" fill="none" stroke="url(#careerAccent)" stroke-width="1.5" opacity="0.3"/>
    <circle cx="0" cy="0" r="50" fill="none" stroke="url(#careerAccent)" stroke-width="1" opacity="0.2"/>
    
    <!-- Career symbols -->
    <g opacity="0.6">
      <!-- Briefcase -->
      <rect x="-15" y="-10" width="30" height="20" rx="3" fill="#4ecdc4" opacity="0.7"/>
      <rect x="-12" y="-8" width="24" height="16" fill="#1a1a2e" opacity="0.8"/>
      <rect x="-5" y="-12" width="10" height="4" rx="2" fill="#4ecdc4"/>
      
      <!-- Growth chart -->
      <g transform="translate(-60, -60)">
        <rect x="0" y="15" width="4" height="5" fill="#45b7d1"/>
        <rect x="8" y="10" width="4" height="10" fill="#45b7d1"/>
        <rect x="16" y="5" width="4" height="15" fill="#45b7d1"/>
        <rect x="24" y="0" width="4" height="20" fill="#45b7d1"/>
      </g>
      
      <!-- Team/people icons -->
      <g transform="translate(60, 60)">
        <circle cx="0" cy="0" r="6" fill="#ff6b6b" opacity="0.7"/>
        <circle cx="15" cy="0" r="6" fill="#4ecdc4" opacity="0.7"/>
        <circle cx="30" cy="0" r="6" fill="#45b7d1" opacity="0.7"/>
      </g>
    </g>
  </g>
  
  <!-- Floating professional elements -->
  <g opacity="0.3" filter="url(#careerGlow)">
    <!-- Code brackets -->
    <text x="150" y="150" fill="#4ecdc4" font-family="monospace" font-size="24" opacity="0.5">{ }</text>
    <text x="950" y="180" fill="#45b7d1" font-family="monospace" font-size="20" opacity="0.5">&lt;/&gt;</text>
    
    <!-- Gaming elements for gaming careers -->
    <g transform="translate(300, 150)">
      <rect x="0" y="5" width="20" height="10" rx="5" fill="#ff6b6b" opacity="0.5"/>
      <circle cx="5" cy="10" r="2" fill="#1a1a2e"/>
      <circle cx="15" cy="10" r="2" fill="#1a1a2e"/>
    </g>
    
    <!-- Design elements -->
    <g transform="translate(800, 650)">
      <circle cx="0" cy="0" r="8" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.4"/>
      <rect x="-5" y="-5" width="10" height="10" fill="#45b7d1" opacity="0.4"/>
      <polygon points="-8,8 0,0 8,8" fill="#ff6b6b" opacity="0.4"/>
    </g>
  </g>
  
  <!-- Animated particles -->
  <g opacity="0.4">
    <circle cx="200" cy="100" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="100;80;100" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1000" cy="150" r="1.5" fill="#ff6b6b">
      <animate attributeName="cy" values="150;130;150" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="300" cy="700" r="2.5" fill="#45b7d1">
      <animate attributeName="cy" values="700;680;700" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="900" cy="600" r="1.8" fill="#4ecdc4">
      <animate attributeName="cy" values="600;580;600" dur="5.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="5.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="200" r="1.2" fill="#ff6b6b">
      <animate attributeName="cy" values="200;180;200" dur="4.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="700" cy="750" r="2.2" fill="#45b7d1">
      <animate attributeName="cy" values="750;730;750" dur="5.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="5.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Subtle energy waves for dynamic feel -->
  <g opacity="0.1">
    <ellipse cx="600" cy="400" rx="400" ry="100" fill="none" stroke="#4ecdc4" stroke-width="1">
      <animate attributeName="rx" values="400;450;400" dur="8s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="600" cy="400" rx="350" ry="80" fill="none" stroke="#ff6b6b" stroke-width="1">
      <animate attributeName="rx" values="350;400;350" dur="7s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="600" cy="400" rx="300" ry="60" fill="none" stroke="#45b7d1" stroke-width="1">
      <animate attributeName="rx" values="300;350;300" dur="6s" repeatCount="indefinite"/>
    </ellipse>
  </g>
</svg>
