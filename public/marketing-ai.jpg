<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="aiAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="aiGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#aiGradient)"/>
  
  <!-- AI Brain/Neural Network -->
  <g transform="translate(300, 200)" filter="url(#aiGlow)">
    <!-- Central AI core -->
    <circle cx="0" cy="0" r="40" fill="url(#aiAccent)" opacity="0.8">
      <animate attributeName="r" values="40;45;40" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="25" fill="#4ecdc4" opacity="0.9">
      <animate attributeName="r" values="25;30;25" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- AI symbol -->
    <text x="0" y="5" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="20" font-weight="bold">AI</text>
  </g>
  
  <!-- Neural network connections -->
  <g opacity="0.7">
    <!-- Network nodes -->
    <circle cx="150" cy="120" r="15" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="450" cy="120" r="15" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="280" r="15" fill="#45b7d1" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="450" cy="280" r="15" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Connection lines -->
    <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.5">
      <path d="M 165 130 Q 230 160 260 180"/>
      <path d="M 340 180 Q 380 160 435 130"/>
      <path d="M 165 270 Q 230 240 260 220"/>
      <path d="M 340 220 Q 380 240 435 270"/>
      <path d="M 150 135 Q 200 200 150 265"/>
      <path d="M 450 135 Q 400 200 450 265"/>
    </g>
  </g>
  
  <!-- Marketing elements -->
  <g opacity="0.8">
    <!-- Analytics charts -->
    <g transform="translate(80, 80)">
      <rect x="0" y="20" width="6" height="15" fill="#4ecdc4"/>
      <rect x="10" y="15" width="6" height="20" fill="#4ecdc4"/>
      <rect x="20" y="10" width="6" height="25" fill="#4ecdc4"/>
      <rect x="30" y="5" width="6" height="30" fill="#4ecdc4"/>
      <text x="20" y="50" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10">ANALYTICS</text>
    </g>
    
    <!-- Social media icons -->
    <g transform="translate(480, 80)">
      <circle cx="0" cy="0" r="8" fill="#ff6b6b" opacity="0.8"/>
      <circle cx="20" cy="0" r="8" fill="#45b7d1" opacity="0.8"/>
      <circle cx="40" cy="0" r="8" fill="#4ecdc4" opacity="0.8"/>
      <text x="20" y="25" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10">SOCIAL</text>
    </g>
    
    <!-- Content creation -->
    <g transform="translate(80, 300)">
      <rect x="0" y="0" width="40" height="25" rx="3" fill="none" stroke="#45b7d1" stroke-width="2"/>
      <rect x="5" y="5" width="12" height="2" fill="#45b7d1"/>
      <rect x="5" y="10" width="18" height="2" fill="#45b7d1"/>
      <rect x="5" y="15" width="15" height="2" fill="#45b7d1"/>
      <text x="20" y="40" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10">CONTENT</text>
    </g>
    
    <!-- Revenue optimization -->
    <g transform="translate(480, 300)">
      <circle cx="20" cy="12" r="12" fill="none" stroke="#ff6b6b" stroke-width="2"/>
      <text x="20" y="17" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="12" font-weight="bold">$</text>
      <text x="20" y="40" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10">REVENUE</text>
    </g>
  </g>
  
  <!-- Data flow indicators -->
  <g opacity="0.6">
    <!-- Data particles flowing -->
    <circle cx="200" cy="150" r="3" fill="#4ecdc4">
      <animate attributeName="cx" values="200;250;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="150;170;190" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="150" r="3" fill="#ff6b6b">
      <animate attributeName="cx" values="400;350;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="150;170;190" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="250" r="3" fill="#45b7d1">
      <animate attributeName="cx" values="200;250;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="250;230;210" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="250" r="3" fill="#4ecdc4">
      <animate attributeName="cx" values="400;350;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="250;230;210" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- AI processing indicators -->
  <g transform="translate(300, 100)" opacity="0.5">
    <!-- Processing waves -->
    <ellipse cx="0" cy="0" rx="60" ry="15" fill="none" stroke="#4ecdc4" stroke-width="2">
      <animate attributeName="rx" values="60;80;60" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.2;0.5" dur="4s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="0" cy="0" rx="40" ry="10" fill="none" stroke="#ff6b6b" stroke-width="2">
      <animate attributeName="rx" values="40;60;40" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.2;0.5" dur="3s" repeatCount="indefinite"/>
    </ellipse>
  </g>
  
  <g transform="translate(300, 300)" opacity="0.5">
    <!-- Processing waves -->
    <ellipse cx="0" cy="0" rx="60" ry="15" fill="none" stroke="#45b7d1" stroke-width="2">
      <animate attributeName="rx" values="60;80;60" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.2;0.5" dur="4s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="0" cy="0" rx="40" ry="10" fill="none" stroke="#4ecdc4" stroke-width="2">
      <animate attributeName="rx" values="40;60;40" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.2;0.5" dur="3s" repeatCount="indefinite"/>
    </ellipse>
  </g>
  
  <!-- Floating AI elements -->
  <g opacity="0.4">
    <circle cx="100" cy="200" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="200;180;200" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="200" r="2" fill="#ff6b6b">
      <animate attributeName="cy" values="200;180;200" dur="4s" repeatCount="indefinite"/>
    </circle>
    <rect x="50" y="50" width="4" height="4" fill="#45b7d1">
      <animate attributeName="y" values="50;30;50" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="550" y="350" width="4" height="4" fill="#4ecdc4">
      <animate attributeName="y" values="350;330;350" dur="3.5s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Title -->
  <text x="300" y="50" text-anchor="middle" fill="url(#aiAccent)" font-family="Arial, sans-serif" font-size="24" font-weight="bold" filter="url(#aiGlow)">
    MARKETING AI
  </text>
  <text x="300" y="370" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
    Intelligent Game Marketing Assistant
  </text>
</svg>
