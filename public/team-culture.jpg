<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cultureGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="teamGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="cultureGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#cultureGradient)"/>
  
  <!-- Team collaboration space -->
  <g opacity="0.8">
    <!-- Conference table -->
    <ellipse cx="300" cy="250" rx="150" ry="50" fill="url(#teamGlow)" opacity="0.3"/>
    
    <!-- Team members around table -->
    <g filter="url(#cultureGlow)">
      <!-- Team member 1 -->
      <circle cx="200" cy="200" r="25" fill="#4ecdc4" opacity="0.8"/>
      <rect x="190" y="200" width="20" height="35" rx="5" fill="#45b7d1" opacity="0.8"/>
      <circle cx="195" cy="190" r="3" fill="#1a1a2e"/>
      <circle cx="205" cy="190" r="3" fill="#1a1a2e"/>
      
      <!-- Team member 2 -->
      <circle cx="300" cy="180" r="25" fill="#ff6b6b" opacity="0.8"/>
      <rect x="290" y="180" width="20" height="35" rx="5" fill="#4ecdc4" opacity="0.8"/>
      <circle cx="295" cy="170" r="3" fill="#1a1a2e"/>
      <circle cx="305" cy="170" r="3" fill="#1a1a2e"/>
      
      <!-- Team member 3 -->
      <circle cx="400" cy="200" r="25" fill="#45b7d1" opacity="0.8"/>
      <rect x="390" y="200" width="20" height="35" rx="5" fill="#ff6b6b" opacity="0.8"/>
      <circle cx="395" cy="190" r="3" fill="#1a1a2e"/>
      <circle cx="405" cy="190" r="3" fill="#1a1a2e"/>
      
      <!-- Team member 4 -->
      <circle cx="250" cy="280" r="25" fill="#4ecdc4" opacity="0.8"/>
      <rect x="240" y="280" width="20" height="35" rx="5" fill="#45b7d1" opacity="0.8"/>
      <circle cx="245" cy="270" r="3" fill="#1a1a2e"/>
      <circle cx="255" cy="270" r="3" fill="#1a1a2e"/>
      
      <!-- Team member 5 -->
      <circle cx="350" cy="280" r="25" fill="#ff6b6b" opacity="0.8"/>
      <rect x="340" y="280" width="20" height="35" rx="5" fill="#4ecdc4" opacity="0.8"/>
      <circle cx="345" cy="270" r="3" fill="#1a1a2e"/>
      <circle cx="355" cy="270" r="3" fill="#1a1a2e"/>
    </g>
  </g>
  
  <!-- Collaboration elements -->
  <g opacity="0.6">
    <!-- Ideas/lightbulbs floating -->
    <g transform="translate(150, 100)">
      <circle cx="0" cy="0" r="8" fill="none" stroke="#ff6b6b" stroke-width="2"/>
      <rect x="-3" y="6" width="6" height="5" rx="1" fill="#ff6b6b"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
    </g>
    
    <g transform="translate(450, 120)">
      <circle cx="0" cy="0" r="8" fill="none" stroke="#4ecdc4" stroke-width="2"/>
      <rect x="-3" y="6" width="6" height="5" rx="1" fill="#4ecdc4"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
    </g>
    
    <!-- Communication lines -->
    <g stroke="#45b7d1" stroke-width="2" fill="none" opacity="0.4">
      <path d="M 225 215 Q 250 200 275 215"/>
      <path d="M 325 195 Q 350 180 375 195"/>
      <path d="M 275 295 Q 300 280 325 295"/>
      <circle cx="225" cy="215" r="2" fill="#45b7d1"/>
      <circle cx="275" cy="215" r="2" fill="#45b7d1"/>
      <circle cx="325" cy="195" r="2" fill="#4ecdc4"/>
      <circle cx="375" cy="195" r="2" fill="#4ecdc4"/>
    </g>
  </g>
  
  <!-- Culture values -->
  <g transform="translate(50, 50)" opacity="0.7">
    <!-- Innovation -->
    <rect x="0" y="0" width="80" height="40" rx="5" fill="url(#teamGlow)" opacity="0.4"/>
    <text x="40" y="25" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Innovation</text>
  </g>
  
  <g transform="translate(470, 50)" opacity="0.7">
    <!-- Collaboration -->
    <rect x="0" y="0" width="80" height="40" rx="5" fill="url(#teamGlow)" opacity="0.4"/>
    <text x="40" y="25" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Teamwork</text>
  </g>
  
  <g transform="translate(50, 320)" opacity="0.7">
    <!-- Growth -->
    <rect x="0" y="0" width="80" height="40" rx="5" fill="url(#teamGlow)" opacity="0.4"/>
    <text x="40" y="25" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Growth</text>
  </g>
  
  <g transform="translate(470, 320)" opacity="0.7">
    <!-- Excellence -->
    <rect x="0" y="0" width="80" height="40" rx="5" fill="url(#teamGlow)" opacity="0.4"/>
    <text x="40" y="25" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Excellence</text>
  </g>
  
  <!-- Remote work elements -->
  <g transform="translate(100, 150)" opacity="0.5">
    <!-- Laptop -->
    <rect x="0" y="0" width="40" height="25" rx="3" fill="#1a1a2e"/>
    <rect x="2" y="2" width="36" height="21" rx="2" fill="url(#teamGlow)" opacity="0.4"/>
    <rect x="15" y="25" width="10" height="3" rx="1" fill="#1a1a2e"/>
  </g>
  
  <g transform="translate(460, 150)" opacity="0.5">
    <!-- Mobile device -->
    <rect x="0" y="0" width="20" height="35" rx="5" fill="#1a1a2e"/>
    <rect x="2" y="5" width="16" height="25" rx="2" fill="url(#teamGlow)" opacity="0.4"/>
    <circle cx="10" cy="32" r="2" fill="#4ecdc4"/>
  </g>
  
  <!-- Central culture symbol -->
  <g transform="translate(300, 250)" filter="url(#cultureGlow)">
    <circle cx="0" cy="0" r="30" fill="none" stroke="url(#teamGlow)" stroke-width="3" opacity="0.8">
      <animate attributeName="r" values="30;35;30" dur="4s" repeatCount="indefinite"/>
    </circle>
    <polygon points="-10,-10 10,-10 10,10 -10,10" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.8"/>
    <circle cx="0" cy="0" r="5" fill="#45b7d1"/>
  </g>
  
  <!-- Floating culture elements -->
  <g opacity="0.4">
    <circle cx="80" cy="80" r="3" fill="#4ecdc4">
      <animate attributeName="cy" values="80;60;80" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="520" cy="100" r="2" fill="#ff6b6b">
      <animate attributeName="cy" values="100;80;100" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="100" cy="350" r="2.5" fill="#45b7d1">
      <animate attributeName="cy" values="350;330;350" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="320" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="320;300;320" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Title -->
  <text x="300" y="380" text-anchor="middle" fill="url(#teamGlow)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#cultureGlow)">
    TEAM CULTURE
  </text>
</svg>
