<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="artGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="neonGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#artGradient)"/>
  
  <!-- Digital art elements -->
  <g opacity="0.8">
    <!-- Geometric shapes -->
    <polygon points="50,50 100,30 150,50 130,100 70,100" fill="url(#neonGlow)" opacity="0.6" filter="url(#glow)"/>
    <polygon points="250,80 300,60 350,80 330,130 270,130" fill="url(#neonGlow)" opacity="0.4" filter="url(#glow)"/>
    
    <!-- Circuit-like patterns -->
    <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.7">
      <path d="M 50 150 L 100 150 L 100 200 L 150 200"/>
      <path d="M 250 180 L 300 180 L 300 230 L 350 230"/>
      <circle cx="100" cy="150" r="3" fill="#4ecdc4"/>
      <circle cx="100" cy="200" r="3" fill="#4ecdc4"/>
      <circle cx="300" cy="180" r="3" fill="#ff6b6b"/>
      <circle cx="300" cy="230" r="3" fill="#ff6b6b"/>
    </g>
    
    <!-- Floating particles -->
    <circle cx="80" cy="80" r="2" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="cy" values="80;60;80" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="320" cy="120" r="1.5" fill="#ff6b6b" opacity="0.7">
      <animate attributeName="cy" values="120;100;120" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="180" cy="250" r="2.5" fill="#45b7d1" opacity="0.6">
      <animate attributeName="cy" values="250;230;250" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Central artwork -->
  <g transform="translate(150, 100)">
    <!-- Main art piece -->
    <rect x="0" y="0" width="100" height="80" rx="10" fill="none" stroke="url(#neonGlow)" stroke-width="3" filter="url(#glow)"/>
    <rect x="10" y="10" width="80" height="60" rx="5" fill="url(#neonGlow)" opacity="0.3"/>
    
    <!-- Digital art pattern -->
    <g opacity="0.8">
      <rect x="20" y="20" width="8" height="8" fill="#4ecdc4"/>
      <rect x="32" y="20" width="8" height="8" fill="#ff6b6b"/>
      <rect x="44" y="20" width="8" height="8" fill="#45b7d1"/>
      <rect x="56" y="20" width="8" height="8" fill="#4ecdc4"/>
      <rect x="68" y="20" width="8" height="8" fill="#ff6b6b"/>
      
      <rect x="20" y="32" width="8" height="8" fill="#45b7d1"/>
      <rect x="32" y="32" width="8" height="8" fill="#4ecdc4"/>
      <rect x="44" y="32" width="8" height="8" fill="#ff6b6b"/>
      <rect x="56" y="32" width="8" height="8" fill="#45b7d1"/>
      <rect x="68" y="32" width="8" height="8" fill="#4ecdc4"/>
      
      <rect x="20" y="44" width="8" height="8" fill="#ff6b6b"/>
      <rect x="32" y="44" width="8" height="8" fill="#45b7d1"/>
      <rect x="44" y="44" width="8" height="8" fill="#4ecdc4"/>
      <rect x="56" y="44" width="8" height="8" fill="#ff6b6b"/>
      <rect x="68" y="44" width="8" height="8" fill="#45b7d1"/>
    </g>
  </g>
  
  <!-- Title -->
  <text x="200" y="250" text-anchor="middle" fill="url(#neonGlow)" font-family="Arial, sans-serif" font-size="18" font-weight="bold" filter="url(#glow)">
    DIGITAL ARTWORK
  </text>
  <text x="200" y="270" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Gaming Collection
  </text>
</svg>
