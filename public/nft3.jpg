<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nft3Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0f3460;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cyberGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#45b7d1;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="nft3Glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="400" fill="url(#nft3Gradient)"/>
  
  <!-- Cyber Samurai -->
  <g transform="translate(150, 200)" filter="url(#nft3Glow)">
    <!-- Samurai body -->
    <rect x="-20" y="0" width="40" height="70" rx="8" fill="url(#cyberGlow)" opacity="0.8"/>
    
    <!-- Samurai head -->
    <circle cx="0" cy="-25" r="20" fill="#45b7d1" opacity="0.9"/>
    
    <!-- Cyber helmet -->
    <rect x="-18" y="-40" width="36" height="25" rx="8" fill="#1a1a2e" opacity="0.8"/>
    <rect x="-15" y="-37" width="30" height="19" rx="5" fill="url(#cyberGlow)" opacity="0.4"/>
    
    <!-- Cyber eyes -->
    <rect x="-10" y="-32" width="6" height="3" rx="1" fill="#4ecdc4" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="3s" repeatCount="indefinite"/>
    </rect>
    <rect x="4" y="-32" width="6" height="3" rx="1" fill="#4ecdc4" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="3s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Cyber armor details -->
    <rect x="-15" y="10" width="30" height="6" rx="3" fill="#4ecdc4" opacity="0.8"/>
    <rect x="-12" y="25" width="24" height="4" rx="2" fill="#45b7d1" opacity="0.8"/>
    <rect x="-8" y="40" width="16" height="3" rx="1" fill="#ff6b6b" opacity="0.8"/>
    
    <!-- Cyber katana -->
    <rect x="25" y="-20" width="4" height="60" rx="2" fill="#1a1a2e"/>
    <rect x="23" y="-25" width="8" height="8" rx="2" fill="#4ecdc4" opacity="0.8"/>
    <rect x="26" y="-22" width="2" height="55" fill="#45b7d1" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.5;0.9" dur="2s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Cyber enhancements -->
    <circle cx="-25" cy="0" r="4" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="25" cy="10" r="4" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Samurai stance legs -->
    <rect x="-15" y="70" width="12" height="30" rx="6" fill="#45b7d1" opacity="0.8"/>
    <rect x="3" y="70" width="12" height="30" rx="6" fill="#45b7d1" opacity="0.8"/>
  </g>
  
  <!-- Cyber effects -->
  <g opacity="0.6">
    <!-- Digital particles -->
    <rect x="80" y="100" width="3" height="3" fill="#4ecdc4">
      <animate attributeName="y" values="100;80;100" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </rect>
    <rect x="220" y="150" width="2" height="2" fill="#ff6b6b">
      <animate attributeName="y" values="150;130;150" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
    </rect>
    <rect x="100" y="300" width="4" height="4" fill="#45b7d1">
      <animate attributeName="y" values="300;280;300" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="200" y="280" width="3" height="3" fill="#4ecdc4">
      <animate attributeName="y" values="280;260;280" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3.5s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Cyber grid -->
    <g stroke="#45b7d1" stroke-width="1" fill="none" opacity="0.3">
      <path d="M 50 120 L 100 120 L 100 170 L 150 170"/>
      <path d="M 200 180 L 250 180 L 250 230 L 200 230"/>
      <circle cx="100" cy="120" r="2" fill="#45b7d1"/>
      <circle cx="100" cy="170" r="2" fill="#45b7d1"/>
      <circle cx="250" cy="180" r="2" fill="#4ecdc4"/>
      <circle cx="250" cy="230" r="2" fill="#4ecdc4"/>
    </g>
  </g>
  
  <!-- Cyber energy field -->
  <g transform="translate(150, 200)" opacity="0.4">
    <circle cx="0" cy="0" r="60" fill="none" stroke="#4ecdc4" stroke-width="2">
      <animate attributeName="r" values="60;70;60" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="45" fill="none" stroke="#ff6b6b" stroke-width="1">
      <animate attributeName="r" values="45;55;45" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Cyber HUD elements -->
  <g transform="translate(50, 80)" opacity="0.5">
    <rect x="0" y="0" width="40" height="20" rx="3" fill="none" stroke="#4ecdc4" stroke-width="1"/>
    <rect x="2" y="2" width="12" height="2" fill="#4ecdc4"/>
    <rect x="2" y="6" width="18" height="2" fill="#45b7d1"/>
    <rect x="2" y="10" width="15" height="2" fill="#4ecdc4"/>
    <rect x="2" y="14" width="20" height="2" fill="#45b7d1"/>
  </g>
  
  <g transform="translate(210, 320)" opacity="0.5">
    <rect x="0" y="0" width="40" height="20" rx="3" fill="none" stroke="#ff6b6b" stroke-width="1"/>
    <rect x="2" y="2" width="15" height="2" fill="#ff6b6b"/>
    <rect x="2" y="6" width="12" height="2" fill="#4ecdc4"/>
    <rect x="2" y="10" width="18" height="2" fill="#ff6b6b"/>
    <rect x="2" y="14" width="14" height="2" fill="#4ecdc4"/>
  </g>
  
  <!-- Epic rarity indicator -->
  <g transform="translate(150, 350)" opacity="0.9">
    <rect x="-25" y="0" width="50" height="25" rx="12" fill="url(#cyberGlow)" opacity="0.6"/>
    <text x="0" y="16" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="14" font-weight="bold">EPIC</text>
  </g>
  
  <!-- Title -->
  <text x="150" y="50" text-anchor="middle" fill="url(#cyberGlow)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#nft3Glow)">
    CYBER SAMURAI
  </text>
</svg>
