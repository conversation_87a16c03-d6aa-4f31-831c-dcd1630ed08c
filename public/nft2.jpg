<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nft2Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dragonGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="nft2Glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="400" fill="url(#nft2Gradient)"/>
  
  <!-- Mystic Dragon -->
  <g transform="translate(150, 200)" filter="url(#nft2Glow)">
    <!-- Dragon body -->
    <ellipse cx="0" cy="0" rx="50" ry="30" fill="url(#dragonGlow)" opacity="0.8"/>
    
    <!-- Dragon head -->
    <ellipse cx="-30" cy="-20" rx="25" ry="20" fill="#ff6b6b" opacity="0.9"/>
    
    <!-- Dragon eyes -->
    <circle cx="-40" cy="-25" r="5" fill="#4ecdc4" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-25" cy="-25" r="5" fill="#4ecdc4" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Dragon horns -->
    <polygon points="-45,-35 -40,-45 -35,-35" fill="#45b7d1" opacity="0.8"/>
    <polygon points="-30,-35 -25,-45 -20,-35" fill="#45b7d1" opacity="0.8"/>
    
    <!-- Dragon wings -->
    <path d="M -10 -10 Q -60 -40 -80 -10 Q -60 20 -10 10" fill="#533483" opacity="0.7"/>
    <path d="M 10 -10 Q 60 -40 80 -10 Q 60 20 10 10" fill="#533483" opacity="0.7"/>
    
    <!-- Wing details -->
    <path d="M -20 -5 Q -50 -25 -60 -5" stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 20 -5 Q 50 -25 60 -5" stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.6"/>
    
    <!-- Dragon tail -->
    <path d="M 40 0 Q 70 -10 90 10 Q 80 20 60 15" fill="#ff6b6b" opacity="0.8"/>
    
    <!-- Dragon scales -->
    <circle cx="-10" cy="-5" r="3" fill="#4ecdc4" opacity="0.7"/>
    <circle cx="5" cy="0" r="3" fill="#45b7d1" opacity="0.7"/>
    <circle cx="20" cy="-5" r="3" fill="#ff6b6b" opacity="0.7"/>
    <circle cx="-5" cy="10" r="3" fill="#4ecdc4" opacity="0.7"/>
    <circle cx="15" cy="10" r="3" fill="#45b7d1" opacity="0.7"/>
  </g>
  
  <!-- Mystical effects -->
  <g opacity="0.6">
    <!-- Magic particles -->
    <circle cx="80" cy="100" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="100;80;100" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="220" cy="150" r="1.5" fill="#ff6b6b">
      <animate attributeName="cy" values="150;130;150" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="100" cy="300" r="2.5" fill="#45b7d1">
      <animate attributeName="cy" values="300;280;300" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="280" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="280;260;280" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Magic circles -->
    <circle cx="150" cy="120" r="40" fill="none" stroke="#ff6b6b" stroke-width="2" opacity="0.3">
      <animate attributeName="r" values="40;50;40" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="280" r="35" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.3">
      <animate attributeName="r" values="35;45;35" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Dragon breath/fire -->
  <g transform="translate(120, 180)" opacity="0.7">
    <!-- Fire breath -->
    <ellipse cx="0" cy="0" rx="15" ry="8" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="rx" values="15;25;15" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="2s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="-10" cy="0" rx="10" ry="6" fill="#4ecdc4" opacity="0.6">
      <animate attributeName="rx" values="10;20;10" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
    </ellipse>
  </g>
  
  <!-- Mystical runes -->
  <g transform="translate(50, 80)" opacity="0.5">
    <circle cx="0" cy="0" r="8" fill="none" stroke="#4ecdc4" stroke-width="2"/>
    <polygon points="0,-5 5,0 0,5 -5,0" fill="#4ecdc4"/>
  </g>
  
  <g transform="translate(250, 320)" opacity="0.5">
    <circle cx="0" cy="0" r="8" fill="none" stroke="#ff6b6b" stroke-width="2"/>
    <polygon points="0,-5 5,0 0,5 -5,0" fill="#ff6b6b"/>
  </g>
  
  <!-- Rare rarity indicator -->
  <g transform="translate(150, 350)" opacity="0.9">
    <rect x="-30" y="0" width="60" height="25" rx="12" fill="url(#dragonGlow)" opacity="0.6"/>
    <text x="0" y="16" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="14" font-weight="bold">RARE</text>
  </g>
  
  <!-- Title -->
  <text x="150" y="50" text-anchor="middle" fill="url(#dragonGlow)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#nft2Glow)">
    MYSTIC DRAGON
  </text>
</svg>
