<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="pnftGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pnftAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="pnftGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#pnftGradient)"/>
  
  <!-- Central pNFT showcase -->
  <g transform="translate(300, 200)" filter="url(#pnftGlow)">
    <!-- Main NFT frame -->
    <rect x="-80" y="-60" width="160" height="120" rx="15" fill="none" stroke="url(#pnftAccent)" stroke-width="4" opacity="0.8"/>
    <rect x="-70" y="-50" width="140" height="100" rx="10" fill="url(#pnftAccent)" opacity="0.3"/>
    
    <!-- NFT character/asset -->
    <g opacity="0.9">
      <!-- Character body -->
      <circle cx="0" cy="-10" r="20" fill="#4ecdc4"/>
      <rect x="-12" y="10" width="24" height="30" rx="5" fill="#45b7d1"/>
      
      <!-- Character details -->
      <circle cx="-6" cy="-15" r="3" fill="#1a1a2e"/>
      <circle cx="6" cy="-15" r="3" fill="#1a1a2e"/>
      <rect x="-4" y="-5" width="8" height="3" fill="#ff6b6b"/>
      
      <!-- Weapon/tool -->
      <rect x="20" y="-5" width="25" height="5" rx="2" fill="#ff6b6b" opacity="0.8"/>
      
      <!-- Special effects -->
      <circle cx="-30" cy="-20" r="3" fill="#4ecdc4" opacity="0.8">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="30" cy="-20" r="3" fill="#ff6b6b" opacity="0.8">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- NFT metadata -->
    <text x="0" y="70" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" font-weight="bold">LEGENDARY WARRIOR</text>
    <text x="0" y="85" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10">#001 - Rare</text>
  </g>
  
  <!-- Blockchain elements -->
  <g opacity="0.7">
    <!-- Blockchain blocks -->
    <g transform="translate(100, 150)">
      <rect x="0" y="0" width="30" height="30" rx="5" fill="url(#pnftAccent)" opacity="0.6"/>
      <rect x="40" y="0" width="30" height="30" rx="5" fill="url(#pnftAccent)" opacity="0.6"/>
      <rect x="80" y="0" width="30" height="30" rx="5" fill="url(#pnftAccent)" opacity="0.6"/>
      
      <!-- Chain connections -->
      <rect x="30" y="12" width="10" height="6" rx="3" fill="#4ecdc4"/>
      <rect x="70" y="12" width="10" height="6" rx="3" fill="#4ecdc4"/>
      
      <!-- Block content -->
      <circle cx="15" cy="15" r="5" fill="#4ecdc4" opacity="0.8"/>
      <circle cx="55" cy="15" r="5" fill="#45b7d1" opacity="0.8"/>
      <circle cx="95" cy="15" r="5" fill="#ff6b6b" opacity="0.8"/>
    </g>
    
    <!-- Mirror on right side -->
    <g transform="translate(470, 150)">
      <rect x="0" y="0" width="30" height="30" rx="5" fill="url(#pnftAccent)" opacity="0.6"/>
      <rect x="-40" y="0" width="30" height="30" rx="5" fill="url(#pnftAccent)" opacity="0.6"/>
      <rect x="-80" y="0" width="30" height="30" rx="5" fill="url(#pnftAccent)" opacity="0.6"/>
      
      <!-- Chain connections -->
      <rect x="-10" y="12" width="10" height="6" rx="3" fill="#4ecdc4"/>
      <rect x="-50" y="12" width="10" height="6" rx="3" fill="#4ecdc4"/>
      
      <!-- Block content -->
      <circle cx="15" cy="15" r="5" fill="#ff6b6b" opacity="0.8"/>
      <circle cx="-25" cy="15" r="5" fill="#45b7d1" opacity="0.8"/>
      <circle cx="-65" cy="15" r="5" fill="#4ecdc4" opacity="0.8"/>
    </g>
  </g>
  
  <!-- Gaming utility indicators -->
  <g transform="translate(150, 300)" opacity="0.6">
    <!-- Multi-game compatibility -->
    <rect x="0" y="0" width="80" height="25" rx="3" fill="url(#pnftAccent)" opacity="0.4"/>
    <text x="40" y="16" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">MULTI-GAME</text>
    
    <rect x="100" y="0" width="80" height="25" rx="3" fill="url(#pnftAccent)" opacity="0.4"/>
    <text x="140" y="16" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10" font-weight="bold">TRADEABLE</text>
    
    <rect x="200" y="0" width="80" height="25" rx="3" fill="url(#pnftAccent)" opacity="0.4"/>
    <text x="240" y="16" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10" font-weight="bold">UPGRADEABLE</text>
  </g>
  
  <!-- Ownership verification -->
  <g transform="translate(450, 100)" opacity="0.8">
    <!-- Verification badge -->
    <circle cx="0" cy="0" r="20" fill="none" stroke="#4ecdc4" stroke-width="3"/>
    <polygon points="-8,0 -3,5 8,-5" fill="#4ecdc4"/>
    <text x="0" y="30" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="8" font-weight="bold">VERIFIED</text>
  </g>
  
  <!-- Value indicators -->
  <g transform="translate(50, 100)" opacity="0.8">
    <!-- Price/value -->
    <circle cx="0" cy="0" r="25" fill="none" stroke="#ff6b6b" stroke-width="3"/>
    <text x="0" y="-5" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="12" font-weight="bold">0.5</text>
    <text x="0" y="8" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="8">ETH</text>
    <text x="0" y="35" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="8" font-weight="bold">VALUE</text>
  </g>
  
  <!-- Floating NFT elements -->
  <g opacity="0.5">
    <circle cx="80" cy="80" r="3" fill="#4ecdc4">
      <animate attributeName="cy" values="80;60;80" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="520" cy="320" r="2" fill="#ff6b6b">
      <animate attributeName="cy" values="320;300;320" dur="4s" repeatCount="indefinite"/>
    </circle>
    <polygon points="550,80 555,75 560,80 555,85" fill="#45b7d1">
      <animateTransform attributeName="transform" type="rotate" values="0 555 80;360 555 80" dur="5s" repeatCount="indefinite"/>
    </polygon>
    <rect x="40" y="350" width="6" height="6" fill="#4ecdc4">
      <animate attributeName="y" values="350;330;350" dur="2.5s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Connection lines -->
  <g stroke="#45b7d1" stroke-width="2" fill="none" opacity="0.4">
    <path d="M 220 200 Q 250 180 280 200"/>
    <path d="M 320 200 Q 350 180 380 200"/>
    <circle cx="220" cy="200" r="2" fill="#45b7d1"/>
    <circle cx="280" cy="200" r="2" fill="#45b7d1"/>
    <circle cx="320" cy="200" r="2" fill="#4ecdc4"/>
    <circle cx="380" cy="200" r="2" fill="#4ecdc4"/>
  </g>
  
  <!-- Title -->
  <text x="300" y="50" text-anchor="middle" fill="url(#pnftAccent)" font-family="Arial, sans-serif" font-size="24" font-weight="bold" filter="url(#pnftGlow)">
    pNFT CONCEPT
  </text>
  <text x="300" y="370" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
    Programmable NFTs for Gaming
  </text>
</svg>
