<svg width="36" height="36" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nextGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#333333;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="18" cy="18" r="18" fill="url(#nextGradient)"/>
  
  <!-- Next.js logo -->
  <g transform="translate(6, 6)">
    <!-- Triangle part -->
    <path d="M12 2 L22 20 L2 20 Z" fill="#ffffff" opacity="0.9"/>
    
    <!-- Inner triangle -->
    <path d="M12 6 L18 16 L6 16 Z" fill="#000000" opacity="0.3"/>
    
    <!-- "N" shape -->
    <path d="M8 8 L8 16 M8 8 L16 16 M16 8 L16 16" stroke="#ffffff" stroke-width="1.5" fill="none" opacity="0.8"/>
  </g>
  
  <!-- Subtle glow effect -->
  <circle cx="18" cy="18" r="17" fill="none" stroke="#4ecdc4" stroke-width="0.5" opacity="0.3"/>
</svg>
