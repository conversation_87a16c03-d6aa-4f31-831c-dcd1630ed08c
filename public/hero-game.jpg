<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gameGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#heroGradient)"/>
  
  <!-- Floating particles -->
  <circle cx="100" cy="100" r="2" fill="#4ecdc4" opacity="0.6">
    <animate attributeName="cy" values="100;80;100" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="150" r="1.5" fill="#ff6b6b" opacity="0.7">
    <animate attributeName="cy" values="150;130;150" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="700" cy="200" r="2.5" fill="#45b7d1" opacity="0.5">
    <animate attributeName="cy" values="200;180;200" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="650" cy="400" r="1.8" fill="#4ecdc4" opacity="0.6">
    <animate attributeName="cy" values="400;380;400" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Gaming elements -->
  <!-- Controller shape -->
  <g transform="translate(150, 200)" filter="url(#glow)">
    <rect x="0" y="20" width="80" height="40" rx="20" fill="url(#gameGlow)" opacity="0.3"/>
    <circle cx="20" cy="40" r="8" fill="#4ecdc4" opacity="0.8"/>
    <circle cx="60" cy="40" r="8" fill="#ff6b6b" opacity="0.8"/>
    <rect x="35" y="10" width="10" height="15" rx="5" fill="#45b7d1" opacity="0.7"/>
    <rect x="35" y="45" width="10" height="15" rx="5" fill="#45b7d1" opacity="0.7"/>
  </g>
  
  <!-- Game screen/monitor -->
  <g transform="translate(500, 150)" filter="url(#glow)">
    <rect x="0" y="0" width="120" height="80" rx="8" fill="#1a1a2e" stroke="url(#gameGlow)" stroke-width="2"/>
    <rect x="10" y="10" width="100" height="60" rx="4" fill="url(#gameGlow)" opacity="0.4"/>
    <!-- Pixel art style elements -->
    <rect x="20" y="20" width="8" height="8" fill="#4ecdc4"/>
    <rect x="32" y="20" width="8" height="8" fill="#ff6b6b"/>
    <rect x="44" y="20" width="8" height="8" fill="#45b7d1"/>
    <rect x="20" y="32" width="8" height="8" fill="#45b7d1"/>
    <rect x="32" y="32" width="8" height="8" fill="#4ecdc4"/>
    <rect x="44" y="32" width="8" height="8" fill="#ff6b6b"/>
  </g>
  
  <!-- Geometric gaming shapes -->
  <polygon points="300,350 320,320 340,350 320,380" fill="#4ecdc4" opacity="0.6" filter="url(#glow)">
    <animateTransform attributeName="transform" type="rotate" values="0 320 350;360 320 350" dur="8s" repeatCount="indefinite"/>
  </polygon>
  
  <polygon points="550,400 570,370 590,400 570,430" fill="#ff6b6b" opacity="0.6" filter="url(#glow)">
    <animateTransform attributeName="transform" type="rotate" values="0 570 400;-360 570 400" dur="6s" repeatCount="indefinite"/>
  </polygon>
  
  <!-- Digital grid pattern -->
  <g opacity="0.1">
    <defs>
      <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4ecdc4" stroke-width="1"/>
      </pattern>
    </defs>
    <rect width="800" height="600" fill="url(#grid)"/>
  </g>
  
  <!-- Central gaming logo/emblem -->
  <g transform="translate(350, 250)" filter="url(#glow)">
    <circle cx="50" cy="50" r="40" fill="none" stroke="url(#gameGlow)" stroke-width="3" opacity="0.8">
      <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="50" cy="50" r="25" fill="url(#gameGlow)" opacity="0.3">
      <animate attributeName="r" values="25;30;25" dur="2s" repeatCount="indefinite"/>
    </circle>
    <!-- Gaming symbol -->
    <polygon points="50,35 60,50 50,65 40,50" fill="#4ecdc4" opacity="0.9"/>
    <circle cx="50" cy="50" r="8" fill="#1a1a2e"/>
  </g>
  
  <!-- Energy waves -->
  <g opacity="0.4">
    <ellipse cx="400" cy="300" rx="200" ry="50" fill="none" stroke="#4ecdc4" stroke-width="2">
      <animate attributeName="rx" values="200;250;200" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="4s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="400" cy="300" rx="150" ry="30" fill="none" stroke="#ff6b6b" stroke-width="2">
      <animate attributeName="rx" values="150;200;150" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="3s" repeatCount="indefinite"/>
    </ellipse>
  </g>
  
  <!-- Futuristic text overlay -->
  <text x="400" y="500" text-anchor="middle" fill="url(#gameGlow)" font-family="Arial, sans-serif" font-size="24" font-weight="bold" opacity="0.7">
    GAMESTORME
  </text>
  <text x="400" y="530" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
    THE FUTURE OF GAMING
  </text>
</svg>
