const { app, BrowserWindow, Menu, shell, ipcMain, dialog, screen, autoUpdater, nativeTheme } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { spawn, exec } = require('child_process');
const isDev = process.env.NODE_ENV === 'development';

// Game Engine Configuration
const GAME_ENGINE_CONFIG = {
  name: 'GameStorme Engine',
  version: '1.0.0',
  gamesDirectory: path.join(os.homedir(), 'GameStorme', 'Games'),
  savesDirectory: path.join(os.homedir(), 'GameStorme', 'Saves'),
  configDirectory: path.join(os.homedir(), 'GameStorme', 'Config'),
  logsDirectory: path.join(os.homedir(), 'GameStorme', 'Logs'),
  tempDirectory: path.join(os.homedir(), 'GameStorme', 'Temp'),
};

let mainWindow;
let splashWindow;
let gameProcesses = new Map(); // Track running games

// Initialize Game Engine directories
function initializeGameEngine() {
  const directories = [
    GAME_ENGINE_CONFIG.gamesDirectory,
    GAME_ENGINE_CONFIG.savesDirectory,
    GAME_ENGINE_CONFIG.configDirectory,
    GAME_ENGINE_CONFIG.logsDirectory,
    GAME_ENGINE_CONFIG.tempDirectory,
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });

  // Create game engine config file
  const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
  if (!fs.existsSync(configFile)) {
    const defaultConfig = {
      version: GAME_ENGINE_CONFIG.version,
      theme: 'dark',
      autoLaunch: false,
      notifications: true,
      gameSettings: {
        defaultResolution: '1920x1080',
        fullscreen: false,
        vsync: true,
        antiAliasing: true,
      },
      installedGames: [],
      recentGames: [],
    };
    fs.writeFileSync(configFile, JSON.stringify(defaultConfig, null, 2));
    console.log('⚙️ Created game engine configuration');
  }
}

function createSplashWindow() {
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;

  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    x: Math.round((width - 400) / 2),
    y: Math.round((height - 300) / 2)
  });

  // Create splash screen HTML
  const splashHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          margin: 0;
          padding: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          color: white;
          border-radius: 10px;
          overflow: hidden;
        }
        .logo {
          width: 80px;
          height: 80px;
          background: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: bold;
          color: #667eea;
          margin-bottom: 20px;
          animation: pulse 2s infinite;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .subtitle {
          font-size: 14px;
          opacity: 0.8;
          margin-bottom: 30px;
        }
        .loading {
          width: 200px;
          height: 4px;
          background: rgba(255,255,255,0.3);
          border-radius: 2px;
          overflow: hidden;
        }
        .loading-bar {
          width: 0%;
          height: 100%;
          background: white;
          border-radius: 2px;
          animation: loading 3s ease-in-out;
        }
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
        @keyframes loading {
          0% { width: 0%; }
          100% { width: 100%; }
        }
      </style>
    </head>
    <body>
      <div class="logo">GS</div>
      <div class="title">GameStorme</div>
      <div class="subtitle">Empowering Game Developers</div>
      <div class="loading">
        <div class="loading-bar"></div>
      </div>
    </body>
    </html>
  `;

  splashWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(splashHTML)}`);

  // Close splash after 3 seconds
  setTimeout(() => {
    if (splashWindow) {
      splashWindow.close();
      splashWindow = null;
    }
    createMainWindow();
  }, 3000);
}

// Game Management Functions
function getInstalledGames() {
  try {
    const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
    if (fs.existsSync(configFile)) {
      const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      return config.installedGames || [];
    }
  } catch (error) {
    console.error('Error reading installed games:', error);
  }
  return [];
}

function addInstalledGame(gameData) {
  try {
    const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
    const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));

    if (!config.installedGames) {
      config.installedGames = [];
    }

    // Check if game already exists
    const existingIndex = config.installedGames.findIndex(g => g.id === gameData.id);
    if (existingIndex >= 0) {
      config.installedGames[existingIndex] = gameData;
    } else {
      config.installedGames.push(gameData);
    }

    fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error adding installed game:', error);
    return false;
  }
}

function launchGame(gameId) {
  try {
    const games = getInstalledGames();
    const game = games.find(g => g.id === gameId);

    if (!game) {
      throw new Error('Game not found');
    }

    const gamePath = path.join(GAME_ENGINE_CONFIG.gamesDirectory, game.directory, game.executable);

    if (!fs.existsSync(gamePath)) {
      throw new Error('Game executable not found');
    }

    // Launch the game
    const gameProcess = spawn(gamePath, [], {
      cwd: path.dirname(gamePath),
      detached: true,
      stdio: 'ignore'
    });

    gameProcesses.set(gameId, gameProcess);

    // Update recent games
    updateRecentGames(game);

    console.log(`🎮 Launched game: ${game.title}`);
    return { success: true, message: `Launched ${game.title}` };

  } catch (error) {
    console.error('Error launching game:', error);
    return { success: false, error: error.message };
  }
}

function updateRecentGames(game) {
  try {
    const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
    const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));

    if (!config.recentGames) {
      config.recentGames = [];
    }

    // Remove if already exists
    config.recentGames = config.recentGames.filter(g => g.id !== game.id);

    // Add to beginning
    config.recentGames.unshift({
      ...game,
      lastPlayed: new Date().toISOString()
    });

    // Keep only last 10
    config.recentGames = config.recentGames.slice(0, 10);

    fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Error updating recent games:', error);
  }
}

function createMainWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    titleBarStyle: 'default',
    show: false,
    backgroundColor: '#1a1a2e',
    webSecurity: true,
    title: 'GameStorme'
  });

  // Load the app directly to login page
  const startUrl = isDev
    ? 'http://localhost:8000/auth/login'
    : `file://${path.join(__dirname, '../build/auth/login.html')}`;

  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.focus();

    // Open dev tools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Prevent navigation to external websites
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    if (parsedUrl.origin !== 'http://localhost:8000' && !isDev) {
      event.preventDefault();
    }
  });
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'Gamestorme',
      submenu: [
        {
          label: 'About Gamestorme',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Gamestorme',
              message: 'Gamestorme Launcher',
              detail: 'Version 2.0.0\nEmpowering Game Developers Through Innovative Solutions\n\nDeveloped by Gamestorme Team\n© 2024 Gamestorme. All rights reserved.'
            });
          }
        },
        { type: 'separator' },
        {
          label: 'Check for Updates',
          click: () => {
            autoUpdater.checkForUpdatesAndNotify();
          }
        },
        { type: 'separator' },
        {
          label: 'Preferences',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.hash = '#settings';
            `);
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Home',
          accelerator: 'CmdOrCtrl+H',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.href = '/';
            `);
          }
        },
        {
          label: 'Developer Dashboard',
          accelerator: 'CmdOrCtrl+D',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.href = '/developer/dashboard';
            `);
          }
        },
        {
          label: 'Gamer Dashboard',
          accelerator: 'CmdOrCtrl+G',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.href = '/gamer/dashboard';
            `);
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'Gamestorme Website',
          click: () => {
            shell.openExternal('https://gamestorme-9c0b68273ab5.herokuapp.com');
          }
        },
        {
          label: 'Support Center',
          click: () => {
            shell.openExternal('https://support.gamestorme.com');
          }
        },
        {
          label: 'Developer Documentation',
          click: () => {
            shell.openExternal('https://docs.gamestorme.com');
          }
        },
        { type: 'separator' },
        {
          label: 'Report Issue',
          click: () => {
            shell.openExternal('https://github.com/joelgriiyo/gamestorme/issues');
          }
        }
      ]
    }
  ];

  if (process.platform === 'darwin') {
    template[0].label = app.getName();
    template[0].submenu.unshift({
      label: 'About ' + app.getName(),
      role: 'about'
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event listeners
app.whenReady().then(() => {
  // Initialize Game Engine
  initializeGameEngine();

  createSplashWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createSplashWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC handlers for communication with renderer process
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// Handle app updates
ipcMain.handle('check-for-updates', () => {
  autoUpdater.checkForUpdatesAndNotify();
  return { updateAvailable: false };
});

// Game Engine IPC Handlers
ipcMain.handle('get-installed-games', () => {
  return getInstalledGames();
});

ipcMain.handle('add-installed-game', (event, gameData) => {
  return addInstalledGame(gameData);
});

ipcMain.handle('launch-game', (event, gameId) => {
  return launchGame(gameId);
});

ipcMain.handle('get-game-engine-config', () => {
  try {
    const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
    if (fs.existsSync(configFile)) {
      return JSON.parse(fs.readFileSync(configFile, 'utf8'));
    }
  } catch (error) {
    console.error('Error reading config:', error);
  }
  return null;
});

ipcMain.handle('update-game-engine-config', (event, config) => {
  try {
    const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
    fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error updating config:', error);
    return false;
  }
});

ipcMain.handle('download-game', async (event, gameData) => {
  try {
    // Create game directory
    const gameDir = path.join(GAME_ENGINE_CONFIG.gamesDirectory, gameData.id);
    if (!fs.existsSync(gameDir)) {
      fs.mkdirSync(gameDir, { recursive: true });
    }

    // For now, we'll simulate a download by creating a placeholder
    // In a real implementation, you'd download the actual game files
    const gameInfo = {
      id: gameData.id,
      title: gameData.title,
      version: gameData.version || '1.0.0',
      directory: gameData.id,
      executable: `${gameData.id}.exe`, // This would be the actual game executable
      installDate: new Date().toISOString(),
      size: gameData.size || '100MB',
      developer: gameData.developer,
      description: gameData.description,
      thumbnail: gameData.thumbnail,
    };

    // Create a placeholder executable (in real implementation, this would be the actual game)
    const executablePath = path.join(gameDir, gameInfo.executable);
    fs.writeFileSync(executablePath, '// Game executable placeholder');

    // Add to installed games
    addInstalledGame(gameInfo);

    return { success: true, game: gameInfo };
  } catch (error) {
    console.error('Error downloading game:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('uninstall-game', (event, gameId) => {
  try {
    // Remove game directory
    const gameDir = path.join(GAME_ENGINE_CONFIG.gamesDirectory, gameId);
    if (fs.existsSync(gameDir)) {
      fs.rmSync(gameDir, { recursive: true, force: true });
    }

    // Remove from installed games
    const configFile = path.join(GAME_ENGINE_CONFIG.configDirectory, 'engine.json');
    const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
    config.installedGames = config.installedGames.filter(g => g.id !== gameId);
    config.recentGames = config.recentGames.filter(g => g.id !== gameId);
    fs.writeFileSync(configFile, JSON.stringify(config, null, 2));

    return { success: true };
  } catch (error) {
    console.error('Error uninstalling game:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-system-info', () => {
  return {
    platform: os.platform(),
    arch: os.arch(),
    release: os.release(),
    totalMemory: os.totalmem(),
    freeMemory: os.freemem(),
    cpus: os.cpus().length,
    homeDir: os.homedir(),
    gameEngineDir: GAME_ENGINE_CONFIG.gamesDirectory,
  };
});

// Auto-updater events
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
  console.log('Update available.');
});

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available.');
});

autoUpdater.on('error', (err) => {
  console.log('Error in auto-updater. ' + err);
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "Download speed: " + progressObj.bytesPerSecond;
  log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
  console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
  console.log('Update downloaded');
  autoUpdater.quitAndInstall();
});

// Handle deep links (for future implementation)
app.setAsDefaultProtocolClient('gamestorme');

// Handle protocol for deep linking
app.on('open-url', (event, url) => {
  event.preventDefault();
  console.log('Deep link:', url);
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
