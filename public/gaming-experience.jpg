<svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="experienceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gameplayGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="experienceGlow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="techGrid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#4ecdc4" stroke-width="0.5" opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="url(#experienceGradient)"/>
  
  <!-- Tech grid overlay -->
  <rect width="800" height="500" fill="url(#techGrid)"/>
  
  <!-- Gaming setup/environment -->
  <g opacity="0.8">
    <!-- Gaming monitor/screen -->
    <g transform="translate(300, 150)">
      <rect x="0" y="0" width="200" height="120" rx="10" fill="#1a1a2e" stroke="url(#gameplayGlow)" stroke-width="3" filter="url(#experienceGlow)"/>
      <rect x="10" y="10" width="180" height="100" rx="5" fill="url(#gameplayGlow)" opacity="0.4"/>
      
      <!-- Game content on screen -->
      <g opacity="0.9">
        <!-- Game character -->
        <circle cx="60" cy="60" r="15" fill="#4ecdc4"/>
        <rect x="52" y="60" width="16" height="25" rx="3" fill="#45b7d1"/>
        
        <!-- Game environment -->
        <rect x="120" y="40" width="20" height="30" fill="#ff6b6b" opacity="0.7"/>
        <rect x="150" y="50" width="15" height="20" fill="#4ecdc4" opacity="0.7"/>
        
        <!-- UI elements -->
        <rect x="15" y="15" width="40" height="4" rx="2" fill="#4ecdc4" opacity="0.8"/>
        <text x="20" y="30" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="8">HP: 100</text>
        <text x="140" y="25" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="8">SCORE: 2,450</text>
      </g>
    </g>
    
    <!-- Gaming controller -->
    <g transform="translate(200, 300)" filter="url(#experienceGlow)">
      <rect x="0" y="0" width="80" height="40" rx="20" fill="url(#gameplayGlow)" opacity="0.8"/>
      <circle cx="20" cy="20" r="6" fill="#4ecdc4"/>
      <circle cx="60" cy="20" r="6" fill="#ff6b6b"/>
      <rect x="35" y="10" width="10" height="6" rx="3" fill="#45b7d1"/>
      <rect x="35" y="24" width="10" height="6" rx="3" fill="#45b7d1"/>
    </g>
    
    <!-- VR headset -->
    <g transform="translate(520, 280)" filter="url(#experienceGlow)">
      <ellipse cx="40" cy="25" rx="40" ry="25" fill="url(#gameplayGlow)" opacity="0.7"/>
      <rect x="10" y="15" width="60" height="20" rx="10" fill="#1a1a2e" opacity="0.8"/>
      <circle cx="25" cy="25" r="8" fill="#4ecdc4" opacity="0.8"/>
      <circle cx="55" cy="25" r="8" fill="#4ecdc4" opacity="0.8"/>
    </g>
  </g>
  
  <!-- Floating gaming elements -->
  <g opacity="0.7">
    <!-- Power-ups and collectibles -->
    <circle cx="100" cy="100" r="8" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="cy" values="100;80;100" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="3s" repeatCount="indefinite"/>
    </circle>
    <polygon points="700,120 710,110 720,120 710,130" fill="#ff6b6b" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 710 120;360 710 120" dur="4s" repeatCount="indefinite"/>
    </polygon>
    <rect x="150" y="400" width="12" height="12" fill="#45b7d1" opacity="0.8">
      <animate attributeName="y" values="400;380;400" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <circle cx="650" cy="400" r="6" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="cy" values="400;380;400" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Gaming network connections -->
  <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.4">
    <path d="M 100 250 Q 200 200 300 250"/>
    <path d="M 500 250 Q 600 200 700 250"/>
    <circle cx="100" cy="250" r="4" fill="#4ecdc4"/>
    <circle cx="300" cy="250" r="4" fill="#4ecdc4"/>
    <circle cx="500" cy="250" r="4" fill="#45b7d1"/>
    <circle cx="700" cy="250" r="4" fill="#45b7d1"/>
  </g>
  
  <!-- Central gaming hub -->
  <g transform="translate(400, 250)" filter="url(#experienceGlow)">
    <circle cx="0" cy="0" r="50" fill="none" stroke="url(#gameplayGlow)" stroke-width="3" opacity="0.6">
      <animate attributeName="r" values="50;60;50" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="30" fill="url(#gameplayGlow)" opacity="0.3">
      <animate attributeName="r" values="30;35;30" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Gaming symbol -->
    <polygon points="0,-15 15,0 0,15 -15,0" fill="#4ecdc4" opacity="0.9"/>
    <circle cx="0" cy="0" r="8" fill="#1a1a2e"/>
    <text x="0" y="3" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="8" font-weight="bold">G</text>
  </g>
  
  <!-- Energy waves -->
  <g opacity="0.3">
    <ellipse cx="400" cy="250" rx="300" ry="80" fill="none" stroke="#4ecdc4" stroke-width="2">
      <animate attributeName="rx" values="300;350;300" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.1;0.3" dur="6s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="400" cy="250" rx="250" ry="60" fill="none" stroke="#ff6b6b" stroke-width="2">
      <animate attributeName="rx" values="250;300;250" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.1;0.3" dur="5s" repeatCount="indefinite"/>
    </ellipse>
  </g>
  
  <!-- Title overlay -->
  <text x="400" y="450" text-anchor="middle" fill="url(#gameplayGlow)" font-family="Arial, sans-serif" font-size="28" font-weight="bold" filter="url(#experienceGlow)">
    GAMING EXPERIENCE
  </text>
  <text x="400" y="480" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="16" opacity="0.8">
    Next-Generation Gaming Platform
  </text>
</svg>
