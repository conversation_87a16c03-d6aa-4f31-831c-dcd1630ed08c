<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="news2Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#533483;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="news2Accent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="news2Glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#news2Gradient)"/>
  
  <!-- Investor relations elements -->
  <g opacity="0.8">
    <!-- Growth chart -->
    <g transform="translate(80, 120)">
      <rect x="0" y="60" width="15" height="40" fill="#ff6b6b" opacity="0.7"/>
      <rect x="20" y="50" width="15" height="50" fill="#ff6b6b" opacity="0.7"/>
      <rect x="40" y="35" width="15" height="65" fill="#ff6b6b" opacity="0.7"/>
      <rect x="60" y="25" width="15" height="75" fill="#ff6b6b" opacity="0.7"/>
      <rect x="80" y="15" width="15" height="85" fill="#ff6b6b" opacity="0.7"/>
      <rect x="100" y="5" width="15" height="95" fill="#ff6b6b" opacity="0.7"/>
      
      <!-- Growth arrow -->
      <polygon points="120,10 130,0 140,10 130,5" fill="#4ecdc4" filter="url(#news2Glow)"/>
    </g>
    
    <!-- Financial metrics -->
    <g transform="translate(250, 100)" filter="url(#news2Glow)">
      <!-- Revenue circle -->
      <circle cx="0" cy="0" r="25" fill="none" stroke="url(#news2Accent)" stroke-width="3"/>
      <text x="0" y="-5" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="12" font-weight="bold">+45%</text>
      <text x="0" y="8" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="8">REVENUE</text>
      
      <!-- User growth -->
      <circle cx="60" cy="0" r="25" fill="none" stroke="url(#news2Accent)" stroke-width="3"/>
      <text x="60" y="-5" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" font-weight="bold">+78%</text>
      <text x="60" y="8" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="8">USERS</text>
    </g>
  </g>
  
  <!-- Investment elements -->
  <g transform="translate(100, 200)" opacity="0.7">
    <!-- Investment bags -->
    <rect x="0" y="0" width="20" height="25" rx="3" fill="url(#news2Accent)" opacity="0.6"/>
    <text x="10" y="15" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">$</text>
    
    <rect x="40" y="0" width="20" height="25" rx="3" fill="url(#news2Accent)" opacity="0.6"/>
    <text x="50" y="15" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">$</text>
    
    <rect x="80" y="0" width="20" height="25" rx="3" fill="url(#news2Accent)" opacity="0.6"/>
    <text x="90" y="15" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">$</text>
    
    <rect x="120" y="0" width="20" height="25" rx="3" fill="url(#news2Accent)" opacity="0.6"/>
    <text x="130" y="15" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">$</text>
    
    <rect x="160" y="0" width="20" height="25" rx="3" fill="url(#news2Accent)" opacity="0.6"/>
    <text x="170" y="15" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="12" font-weight="bold">$</text>
  </g>
  
  <!-- Market indicators -->
  <g transform="translate(50, 80)" opacity="0.6">
    <rect x="0" y="0" width="80" height="20" rx="3" fill="url(#news2Accent)" opacity="0.4"/>
    <text x="40" y="13" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10" font-weight="bold">MARKET GROWTH</text>
    
    <rect x="100" y="0" width="80" height="20" rx="3" fill="url(#news2Accent)" opacity="0.4"/>
    <text x="140" y="13" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">STRONG METRICS</text>
    
    <rect x="200" y="0" width="80" height="20" rx="3" fill="url(#news2Accent)" opacity="0.4"/>
    <text x="240" y="13" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10" font-weight="bold">EXPANSION</text>
  </g>
  
  <!-- Floating financial elements -->
  <g opacity="0.5">
    <circle cx="60" cy="60" r="2" fill="#ff6b6b">
      <animate attributeName="cy" values="60;40;60" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="340" cy="80" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="80;60;80" dur="4s" repeatCount="indefinite"/>
    </circle>
    <polygon points="320,250 325,245 330,250 325,255" fill="#45b7d1">
      <animateTransform attributeName="transform" type="rotate" values="0 325 250;360 325 250" dur="5s" repeatCount="indefinite"/>
    </polygon>
  </g>
  
  <!-- Connection lines -->
  <g stroke="#45b7d1" stroke-width="2" fill="none" opacity="0.4">
    <path d="M 220 130 Q 250 110 280 130"/>
    <circle cx="220" cy="130" r="2" fill="#45b7d1"/>
    <circle cx="280" cy="130" r="2" fill="#45b7d1"/>
  </g>
  
  <!-- Title -->
  <text x="200" y="40" text-anchor="middle" fill="url(#news2Accent)" font-family="Arial, sans-serif" font-size="18" font-weight="bold" filter="url(#news2Glow)">
    INVESTOR RELATIONS
  </text>
  <text x="200" y="280" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Strong Growth & Performance
  </text>
</svg>
