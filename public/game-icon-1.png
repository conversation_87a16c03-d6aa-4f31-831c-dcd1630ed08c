<svg width="120" height="120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="icon1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:1" />
    </linearGradient>
    <filter id="icon1Glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="60" cy="60" r="55" fill="url(#icon1Gradient)" filter="url(#icon1Glow)"/>
  
  <!-- Inner circle -->
  <circle cx="60" cy="60" r="40" fill="none" stroke="#1a1a2e" stroke-width="3" opacity="0.8"/>
  
  <!-- Game sword/weapon -->
  <g transform="translate(60, 60)">
    <!-- Sword blade -->
    <rect x="-3" y="-30" width="6" height="40" fill="#1a1a2e"/>
    <polygon points="-3,-30 0,-35 3,-30" fill="#1a1a2e"/>
    
    <!-- Sword handle -->
    <rect x="-5" y="10" width="10" height="15" rx="2" fill="#533483"/>
    <rect x="-8" y="5" width="16" height="4" rx="2" fill="#4ecdc4"/>
    
    <!-- Decorative elements -->
    <circle cx="-15" cy="-15" r="2" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="-15" r="2" fill="#45b7d1" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-15" cy="15" r="2" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="15" r="2" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Orbiting elements -->
  <circle cx="105" cy="60" r="3" fill="#4ecdc4" opacity="0.8">
    <animateTransform attributeName="transform" type="rotate" values="0 60 60;360 60 60" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="15" cy="60" r="2" fill="#ff6b6b" opacity="0.8">
    <animateTransform attributeName="transform" type="rotate" values="0 60 60;-360 60 60" dur="6s" repeatCount="indefinite"/>
  </circle>
</svg>
