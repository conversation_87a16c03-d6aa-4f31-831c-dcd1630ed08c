/* Enhanced Global styles */
@import url('https://fonts.googleapis.com/css2?family=Exo:wght@300;400;500;600;700;800;900&display=swap');

html,
body {
  padding: 0;
  margin: 0;
  font-family: 'Exo', -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  scroll-behavior: smooth;
  background: linear-gradient(135deg, #0A0A1A 0%, #1D1429 50%, #2A1B3D 100%);
  background-attachment: fixed;
  color: #FFFFFF;
  overflow-x: hidden;
  font-feature-settings: 'liga' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Enhanced Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(10, 10, 26, 0.4);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4229BC 0%, #7B65ED 100%);
  border-radius: 8px;
  border: 2px solid rgba(10, 10, 26, 0.4);
  box-shadow: 0 0 10px rgba(66, 41, 188, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #7B65ED 0%, #4229BC 100%);
  box-shadow: 0 0 15px rgba(66, 41, 188, 0.5);
}

::-webkit-scrollbar-corner {
  background: rgba(10, 10, 26, 0.4);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes gradientBg {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced Utility classes */
.text-gradient {
  background: linear-gradient(135deg, #4229BC 0%, #7B65ED 50%, #9B7EF0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, #F0BC2B 0%, #F7D36E 50%, #FFE082 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.bg-gradient {
  background: linear-gradient(135deg, #0A0A1A 0%, #1D1429 50%, #2A1B3D 100%);
}

.bg-glass {
  background: rgba(29, 20, 41, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-hover {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4), 0 0 40px rgba(66, 41, 188, 0.3);
}

.glow-effect {
  box-shadow: 0 0 20px rgba(66, 41, 188, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 40px rgba(66, 41, 188, 0.6);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(135deg, rgba(29, 20, 41, 0.8), rgba(29, 20, 41, 0.4)) padding-box,
              linear-gradient(135deg, #4229BC, #7B65ED) border-box;
}

/* Swiper custom styles */
.swiper-pagination-bullet {
  width: 10px !important;
  height: 10px !important;
  background: rgba(255, 255, 255, 0.3) !important;
  opacity: 1 !important;
}

.swiper-pagination-bullet-active {
  background: #4229BC !important;
}

.swiper-button-next,
.swiper-button-prev {
  color: #4229BC !important;
  background: rgba(255, 255, 255, 0.1);
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 18px !important;
}

/* Toast custom styles */
.Toastify__toast {
  border-radius: 12px !important;
  background: rgba(29, 20, 41, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(66, 41, 188, 0.2) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
}

.Toastify__toast-body {
  font-family: 'Exo', sans-serif !important;
  color: #FFFFFF !important;
}

.Toastify__progress-bar {
  background: linear-gradient(90deg, #4229BC, #7B65ED) !important;
}
