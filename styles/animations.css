/* Enhanced Animation keyframes */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  70% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(66, 41, 188, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(66, 41, 188, 0.6), 0 0 60px rgba(123, 101, 237, 0.4);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes morphBackground {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideRight {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Enhanced Animation classes */
.shake-animation {
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

.glow-animation {
  animation: glow 2s ease-in-out infinite alternate;
}

.shimmer-animation {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.morph-animation {
  animation: morphBackground 8s ease-in-out infinite;
}

.particle-animation {
  animation: particleFloat 10s linear infinite;
}

.gradient-shift {
  background-size: 400% 400%;
  animation: gradientShift 4s ease infinite;
}

.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down {
  animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left {
  animation: slideLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right {
  animation: slideRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
  animation: slideInFromLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-right {
  animation: slideInFromRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Transition classes */
.transition-all {
  transition: all 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

.transition-opacity {
  transition: opacity 0.3s ease;
}

.transition-colors {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Enhanced Hover effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale-lg:hover {
  transform: scale(1.1);
}

.hover-up:hover {
  transform: translateY(-8px);
}

.hover-up-sm:hover {
  transform: translateY(-4px);
}

.hover-shadow:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(66, 41, 188, 0.5);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-tilt:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

.hover-blur:hover {
  filter: blur(2px);
}

.hover-brightness:hover {
  filter: brightness(1.2);
}

.hover-saturate:hover {
  filter: saturate(1.5);
}

/* Glass morphism effects */
.glass-card {
  background: rgba(29, 20, 41, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.glass-button {
  background: rgba(66, 41, 188, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(66, 41, 188, 0.3);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(66, 41, 188, 0.3);
  border-color: rgba(66, 41, 188, 0.5);
}

/* Image effects */
.image-border {
  border: 2px solid rgba(66, 41, 188, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.image-border:hover {
  border-color: rgba(66, 41, 188, 0.6);
  box-shadow: 0 0 20px rgba(66, 41, 188, 0.3);
}

.image-glow {
  filter: drop-shadow(0 0 10px rgba(66, 41, 188, 0.3));
  transition: filter 0.3s ease;
}

.image-glow:hover {
  filter: drop-shadow(0 0 20px rgba(66, 41, 188, 0.6));
}

/* Dark mode transition */
.dark-mode-transition {
  transition: background-color 0.5s ease, color 0.5s ease;
}
