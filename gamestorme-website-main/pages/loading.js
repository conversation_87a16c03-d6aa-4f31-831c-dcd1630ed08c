import Layout from "components/layout/Layout";

import { Container, Box } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  loading: {
    backgroundImage: "url('loading-desk.gif')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover, auto",
    backgroundPosition: "center",
    height: "500px"
  }
}));

const Loading = () => {
  const classes = useStyles();
  // use your picture

  return (
    <Layout
      // type your page title and page description.
      title=""
      description=""
    >
      <Container>
        <Box className={classes.loading}></Box>
      </Container>
    </Layout>
  );
};

export default Loading;
