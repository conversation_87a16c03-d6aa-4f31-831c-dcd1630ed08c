import { Typography, Box, Container, Grid } from "@material-ui/core";
import Layout from "components/layout/Layout";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import useMediaQuery from "@material-ui/core/useMediaQuery";

const useStyles = makeStyles((theme) => ({
  privacyTitle: {
    fontFamily: 'NeueMachina',
    fontStyle: 'normal',
    fontWeight: '800',
    fontSize: '80px',
    lineHeight: '130%',
    textAlign: 'center',
    color: '#FFFDFA',
    "& span": {
      color: "#F0BC2B"
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: '36px',
    }
  },
  title: {
    color: "#F0BC2B",
    marginBottom: "50px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "320px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    textTransform: "uppercase",
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "220px",
      marginBottom: "25px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  content: {
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
      lineHeight: "150%",
      marginBottom: "50px",
    }
  },
  highlight: {
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    fontSize: "36px",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "24px",
      marginBottom: "25px",
    }
  },
  subtitle3: {
    paddingTop: '10px',
    fontSize: "18px",
    color: "#FFFDFA",
    fontWeight: "400",
    lineHeight: "150%",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "50px",
    }
  },
}))

const Cookie = () => {
  const classes = useStyles();
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Layout
      // type your page title and page description.
      title="Cookie Policy"
      description="Cookie Policy page"
    >
      <Box pb={!matches ? "100px" : "50px"} borderBottom="1px solid #F0BC2B">
        <Container>
          <Box mt={!matches ? "100px" : "50px"} mb={!matches ? "50px" : '25px'}>
            <Typography className={classes.privacyTitle}>Gamestorme<br></br> <span>Cookie Policy</span></Typography>
          </Box>
          <Box className={classes.title}>Last updated: April 06, 2022</Box>
          <Typography variant="subtitle2" className={classes.content}>
            This Cookies Policy explains what Cookies are and how We use them. You should read this policy so You can understand what type of cookies We use, or the information We collect using Cookies and how that information is used.
            Cookies do not typically contain any information that personally identifies a user, but personal information that we store about You may be linked to the information stored in and obtained from Cookies. For further information on how We use, store and keep your personal data secure, see our Privacy Policy.
            We do not store sensitive personal information, such as mailing addresses, account passwords, etc. in the Cookies We use. We do store your IP Address and Wallet Address.
          </Typography>
          <Grid container>
            <Grid item md={5}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Interpretation:</span>
              </Typography>
            </Grid>
            <Grid item md={7}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                The words of which the initial letter is capitalized have meanings defined under the following conditions. The following definitions shall have the same meaning regardless of whether they appear in singular or in plural.
              </Typography>
            </Grid>
            <Grid item md={5}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Definitions:</span>
              </Typography>
            </Grid>
            <Grid item md={7}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                •	Company (referred to as either "the Company", "We", "Us" or "Our" in this Cookies Policy) refers to Gamestorme.<br></br>
                •	Cookies means small files that are placed on Your computer, mobile device or any other device by a website, containing details of your browsing history on that website among its many uses.
                <br></br>
                •	Website refers to Gamestorme, accessible from https://gamestorme.com/
                <br></br>
                •	You means the individual accessing or using the Website, or a company, or any legal entity on behalf of which such individual is accessing or using the Website, as applicable.
              </Typography>
            </Grid>
            <Grid item md={5}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Type of Cookies We Use:</span>
              </Typography>
            </Grid>
            <Grid item md={7}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                Cookies can be "Persistent" or "Session" Cookies. Persistent Cookies remain on your personal computer or mobile device when You go offline, while Session Cookies are deleted as soon as You close your web browser.
                We use both session and persistent Cookies for the purposes set out below:
                <br></br>
                <br></br>
                <b>- Type: Session Cookies</b>
                <br></br>
                Administered by: Us
                <br></br>
                Purpose: These Cookies are essential to provide You with services available through the Website and to enable You to use some of its features. They help to authenticate users and prevent fraudulent use of user accounts. Without these Cookies, the services that You have asked for cannot be provided, and We only use these Cookies to provide You with those services.
                Functionality Cookies
                <br></br>
                <b>- Type: Persistent Cookies</b>
                <br></br>
                Administered by: Us
                <br></br>
                Purpose: These Cookies allow us to remember choices You make when You use the Website, such as remembering your login details or language preference. The purpose of these Cookies is to provide You with a more personal experience and to avoid You having to re-enter your preferences every time You use the Website.
                Social Media Cookies
                <br></br>
                <b>- Type: Persistent Cookies</b>
                <br></br>
                Administered by: Third-Parties
                <br></br>
                Purpose: In addition to Our own Cookies, We may also use various third party plug-ins from social media networking websites such as Facebook, Instagram, Twitter or Google+ to report usage statistics of the Website and to provide social media features. These third party plug-ins may store Cookies. We do not control these Social Media Cookies. Please refer to the relevant social media networking's website privacy policies for information about their cookies.
              </Typography>
            </Grid>
            <Grid item md={5}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Your Choices Regarding Cookies:</span>
              </Typography>
            </Grid>
            <Grid item md={7}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                If You prefer to avoid the use of Cookies on the Website, first You must disable the use of Cookies in your browser and then delete the Cookies saved in your browser associated with this website. You may use this option for preventing the use of Cookies at any time.
                If You do not accept Our Cookies, You may experience some inconvenience in your use of the Website and some features may not function properly.
                If You'd like to delete Cookies or instruct your web browser to delete or refuse Cookies, please visit the help pages of your web browser.
                <br></br>
                <br></br>
                - For the Chrome web browser, please visit this page from Google: https://support.google.com/accounts/answer/32050
                For the Internet Explorer web browser, please visit this page from Microsoft: http://support.microsoft.com/kb/278835
                <br></br>
                - For the Firefox web browser, please visit this page from Mozilla: https://support.mozilla.org/en-US/kb/delete-cookies-remove-info-websites-stored
                <br></br>
                - For the Safari web browser, please visit this page from Apple: https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/mac
                <br></br>
                - For any other web browser, please visit your web browser's official web pages.
              </Typography>
            </Grid>
            <Grid item md={5}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>More Information about Cookies:</span>
              </Typography>
            </Grid>
            <Grid item md={7}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                You can learn more about cookies here: All About Cookies by TermsFeed.
              </Typography>
            </Grid>
            <Grid item md={5}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Contact Us:</span>
              </Typography>
            </Grid>
            <Grid item md={7}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                If you have any questions about this Cookies Policy, You can contact us:
                •	By email: <EMAIL>
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Layout>
  );
};

export default Cookie;
