import { Typography, Box, Container, Grid } from "@material-ui/core";
import Layout from "components/layout/Layout";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import useMediaQuery from "@material-ui/core/useMediaQuery";

const useStyles = makeStyles((theme) => ({
  privacyTitle: {
    fontFamily: 'NeueMachina',
    fontStyle: 'normal',
    fontWeight: '800',
    fontSize: '80px',
    lineHeight: '130%',
    textAlign: 'center',
    color: '#FFFDFA',
    "& span": {
      color: "#F0BC2B"
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: '36px',
    }
  },
  title: {
    color: "#F0BC2B",
    marginBottom: "50px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "320px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    textTransform: "uppercase",
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "220px",
      marginBottom: "25px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  content: {
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
      lineHeight: "150%",
      marginBottom: "50px",
    }
  },
  highlight: {
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    fontSize: "36px",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "24px",
      marginBottom: "25px",
    }
  },
  subtitle3: {
    fontSize: "18px",
    color: "#FFFDFA",
    fontWeight: "400",
    lineHeight: "150%",
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "50px",
    }
  },
}))

const Privacy = () => {
  const classes = useStyles();
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Layout
      // type your page title and page description.
      title="privacy"
      description="privacy page"
    >
      <Box pb={!matches ? "100px" : "50px"} borderBottom="1px solid #F0BC2B">
        <Container>
          <Box mt={!matches ? "100px" : "50px"} mb={!matches ? "50px" : '25px'}>
            <Typography className={classes.privacyTitle}>Gamestorme<br></br> <span>Privacy Policy</span></Typography>
          </Box>
          <Box className={classes.title}>Last updated: March 10, 2022</Box>
          <Typography variant="subtitle2" className={classes.content}>
            Gamestorme strives to ensure that user interaction with our products and services is safe, secure, reliable,
            and most importantly, fun! Subsequently, this document is meant to illustrate how we collect and use your
            personal information when accessing our products and services through mobile devices or our website.
            After reading this document you should understand:<br></br>
            ∙ What Information we Collect and Where we Collect it From <br></br>
            ∙ The Ways in Which we Use Your Information <br></br>
            ∙ How You Can Take Control of Your Information <br></br>
            ∙ How to Ask Us Questions
          </Typography>
          <Grid container>
            <Grid item md={6}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Information We Collect:</span>
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                At this current point in time, no Gamestorme products or services, including Skybound, collect user data. That’s not to say that they won’t in the future, but should that occur, this privacy policy will be updated accordingly. Additionally, we give you a Gamestorme guarantee that your personal data will never be monetized in any way, shape, or form.
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>How Information is Used:</span>
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                As indicated previously, Gamestorme doesn’t collect personal information at this time. However, should this change, our privacy policy will be appropriately revised. Furthermore, as we’ve mentioned how collected personal data will never be monetized, an example of its unmonetized use would be the creation of a global ranking system for products and services such as Skybound.
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Taking Control:</span>
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                Per the preceding comments, Gamestorme does not currently collect personal information. That being said, should we eventually collect personal information, such as by requiring users to create Gamestorme accounts to access our products and services, then users can easily remove their personal data from our possession by simply deleting their accounts.
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Contact information:</span>
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                For any questions regarding the Privacy Policy outlined above please contact:<br></br>
                Chief Legal Officer - Benjamin Boyd<br></br>
                Email - <span style={{ color: "#F0BC2B" }}><EMAIL></span>
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Layout>
  );
};

export default Privacy;
