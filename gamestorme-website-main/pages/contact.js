import Layout from "components/layout/Layout";
import { Container, Grid, Box, Typography, <PERSON><PERSON>ield, Button } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";

const useStyles = makeStyles((theme) => ({
  container: {
    marginTop: "120px",
    paddingBottom: "200px",
    borderBottom: "1px solid #F0BC2B",
    [theme.breakpoints.down("xs")]: {
      paddingBottom: "100px",
      marginTop: "50px",
    }
  },
  infoBlock: {
    background: "#1D1429",
    border: "1px solid #F0BC2B",
    boxSizing: "border-box",
    borderRadius: "16px",
    padding: "35px 50px",
    display: "flex",
    marginBottom: "25px",
    "&:last-child": {
      marginBottom: "0px",
    },
    [theme.breakpoints.down("xs")]: {
      padding: "24px"
    }
  },
  infoIcon: {
    flexShrink: "0",
    width: "90px",
    height: "90px",
    marginRight: "47px",
    [theme.breakpoints.down("xs")]: {
      marginRight: "20px",
    }
  },
  infoTitle: {
    fontFamily: "NeueMachina",
    fontWeight: 800,
    fontStyle: "normal",
    fontSize: "32px",
    lineHeight: "30px",
    color: "#E5E5E5",
    marginBottom: "15px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
      lineHeight: "15px",
    }
  },
  infoContent: {
    fontWeight: "400",
    fontSize: "18px",
    lineHeight: "150%",
    color: "#A9AFC3",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      lineHeight: "21px",
    }
  },
  title2: {
    color: "#F0BC2B",
    marginBottom: "25px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "175px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "112px",
      marginBottom: "15px",
      marginTop: "0px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "25px",
    lineHeight: "130%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      marginBottom: "25px",
    }
  },
  highlight: {
    paddingRight: "30px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  subtitle2: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "400",
    marginBottom: "45px",
    lineHeight: "27px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "25px",
    }
  },
  input: {
    "& label": {
      fontFamily: "Exo",
      fontStyle: "normal",
      fontWeight: "normal",
      fontSize: "18px",
      lineHeight: "150%",
      color: "#A9AFC3",
    },
    [theme.breakpoints.down("xs")]: {
      "& label": {
        fontSize: "16px"
      }
    },
    "& label + .MuiInput-formControl": {
      marginTop: "30px"
    },
    "& .MuiInput-underline:before": {
      borderBottom: "1px solid #F0BC2B"
    },
    "& .MuiInput-underline:hover:not(.Mui-disabled):before": {
      borderBottom: "1px solid #F0BC2B"
    },
    "& .MuiInputBase-input": {
      color: "#fff"
    },
    "& .MuiInput-underline:after": {
      borderBottom: "1px solid #F0BC2B"
    },
    width: "100%"
  },
  ctaBtn: {
    marginTop: "45px",
    [theme.breakpoints.down("xs")]: {
      "& button": {
        width: "100%",
        marginBottom: "70px"
      }
    },
    "& button": {
      padding: "13px 54px"
    }
  },
  reverseOrder: {
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
}));

const Contact = () => {
  const classes = useStyles();

  return (
    <Layout
      // type your page title and page description.
      title="Contact"
      description="Contact page"
    >
      <Box className={classes.container}>
        <Container>
          <Grid container spacing={7} className={classes.reverseOrder}>
            <Grid item md={6} xs={12}>
              <Box className={classes.infoBlock}>
                <img className={classes.infoIcon} src="icon-map.png"></img>
                <Box display="flex" justifyContent="center" flexDirection="column" >
                  <Typography className={classes.infoTitle}>
                    Address:
                  </Typography>
                  <Typography variant="subtitle2" className={classes.infoContent}>
                    27 Division St, New York, NY 10002, USA
                  </Typography>
                </Box>
              </Box>
              <Box className={classes.infoBlock}>
                <img className={classes.infoIcon} src="icon-chat.png"></img>
                <Box display="flex" justifyContent="center" flexDirection="column" >
                  <Typography className={classes.infoTitle}>
                    Hit us up:
                  </Typography>
                  <Typography variant="subtitle2" className={classes.infoContent}>
                    +1 845 631 78 49 <br></br>
                    <EMAIL>
                  </Typography>
                </Box>
              </Box>
              <Box className={classes.infoBlock}>
                <img className={classes.infoIcon} src="icon-clock.png"></img>
                <Box display="flex" justifyContent="center" flexDirection="column" >
                  <Typography className={classes.infoTitle}>
                    Working Schedule:
                  </Typography>
                  <Typography variant="subtitle2" className={classes.infoContent}>
                    Mon - Fri: 9 am - 6 pm <br></br>
                    Sat, Sun: Holiday
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item md={6} xs={12}>
              <Box>
                <Box className={classes.title2}>CONTACT US</Box>
                <Typography variant="subtitle1" className={classes.slideCaption}>
                  <span className={classes.highlight}>Send a message</span>
                </Typography>
                <Typography variant="subtitle2" className={classes.subtitle2}>
                  We always try to implement our creative ideas at the highest level. Tell us about your project and we will make it work.
                </Typography>
                <Box>
                  <Grid container spacing={4}>
                    <Grid item md={6} xs={12}>
                      <TextField className={classes.input} id="standard-basic" type="text" label="Name" />
                    </Grid>
                    <Grid item md={6} xs={12}>
                      <TextField className={classes.input} id="standard-basic" type="email" label="Email" />
                    </Grid>
                    <Grid item md={12} xs={12}>
                      <TextField
                        className={clsx(classes.input, classes.textarea)}
                        id="standard-multiline-static"
                        label="Message"
                        multiline
                        rows={4}
                        defaultValue=""
                      />
                    </Grid>
                  </Grid>
                </Box>
                <Box className={classes.ctaBtn}>
                  <Button variant="outlined">Send message</Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Layout>
  );
};

export default Contact;
