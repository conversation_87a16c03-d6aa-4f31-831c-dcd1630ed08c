@font-face {
  font-family: 'NeueMachina';
  src: url('./NeueMachina/NeueMachina-Regular.otf') format('opentype'), /* Modern Browsers */
  url('./NeueMachina/NeueMachina-Ultrabold.otf') format('opentype');
  /* <PERSON>fari, Android, iOS */
  font-style: normal;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}

.carousel.carousel-slider .control-arrow:hover {
  background: transparent !important;
}

.carousel .control-dots {
  margin-bottom: 50px !important;
}

.carousel .control-dots .dot {
  width: 15px !important;
  height: 15px !important;
  margin: 0 15px !important;
}

.carousel .control-dots .dot.selected {
  width: 60px !important;
  border-radius: 30px;
}

.slick-center {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  transform: scale(1.1);
}

.slick-dots li.slick-active button:before {
  opacity: 0 !important;
}

.slick-dots li.slick-active {
  width: 60px;
  height: 18px;
  background: #FFFDFA;
  border-radius: 29px;
}

.slick-dots li button:before {
  font-size: 18px !important;
  color: gray !important;
  backdrop-filter: blur(37px) !important;
}

.slick-dots {
  bottom: -50px !important;
  background-color: transparent !important;
}

@media (max-width: 600px) {
  .slick-dots li.slick-active {
    top: 4px;
    width: 32px;
    height: 10px;
    background: #FFFDFA;
    border-radius: 10px;
  }
  .slick-dots li button:before {
    font-size: 10px !important;
    color: gray !important;
    backdrop-filter: blur(37px) !important;
  }
  .slick-dots {
    bottom: -50px !important;
    background-color: transparent !important;
  }
}

.slick-prev {
  left: 3% !important;
  z-index: 1;
  background-image: url('../prev.png') !important;
  background-repeat: no-repeat !important;
  width: 40px !important;
  height: 40px !important;
}

.slick-prev::before,
.slick-next::before {
  content: '' !important;
}

.slick-next {
  right: 3% !important;
  z-index: 1;
  background-image: url('../next.png') !important;
  background-repeat: no-repeat !important;
  width: 40px !important;
  height: 40px !important;
}