import { createTheme } from "@mui/material/styles";

const mainBlack = "#212121";
const mainWhite = "#fafafa";
const blue = "#757ce8";
const yellow = "#F0BC2B";

// Create a theme instance.
const theme = createTheme({
  palette: {
    mode: 'dark',
    common: {
      black: mainBlack,
      white: mainWhite,
      blue: blue,
      yellow: yellow,
    },
    primary: {
      main: mainBlack,
    },
    secondary: {
      main: yellow,
    },
    info: {
      main: blue,
    },
    background: {
      default: '#121212',
      paper: '#1D1429',
    },
    text: {
      primary: '#FFFDFA',
      secondary: '#A9AFC3',
    },
  },
  typography: {
    fontFamily: '"Exo", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontFamily: "Exo",
      fontSize: "2.25rem",
      fontWeight: 500,
    },
    h2: {
      fontFamily: "Exo",
      fontSize: "1.5rem",
      fontWeight: 500,
    },
    h3: {
      fontFamily: "Exo",
      fontSize: "1.25rem",
      fontWeight: "normal",
    },
    subtitle1: {
      fontFamily: "Exo",
      fontWeight: 800,
      fontStyle: "normal",
      fontSize: "56px",
      lineHeight: "130%",
      color: "#FFFDFA"
    },
    subtitle2: {
      fontFamily: "Exo",
      fontStyle: "normal",
      fontWeight: "600",
      fontSize: "24px",
      lineHeight: "32px",
      color: "#FFFDFA"
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          scrollbarColor: "#6b6b6b #2b2b2b",
          "&::-webkit-scrollbar, & *::-webkit-scrollbar": {
            backgroundColor: "#2b2b2b",
            width: "8px",
          },
          "&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb": {
            borderRadius: 8,
            backgroundColor: "#6b6b6b",
            minHeight: 24,
          },
          "&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus": {
            backgroundColor: "#959595",
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        outlined: {
          border: 0,
          fontStyle: "normal",
          fontWeight: 500,
          fontSize: "18px",
          lineHeight: "24px",
          fontFamily: "Exo",
          background: "linear-gradient(270deg, #4229BC 0%, #7B65ED 100%)",
          border: "1px solid #F0BC2B",
          boxSizing: "border-box",
          borderRadius: "0px 16px",
          color: "#fff",
          transition: "all 0.3s ease-in-out",
          "&:hover": {
            transform: "translateY(-2px)",
            boxShadow: "0 5px 10px rgba(0, 0, 0, 0.2)",
          },
        },
        root: {
          textTransform: "none"
        }
      }
    },
    MuiLink: {
      styleOverrides: {
        root: {
          "&:hover": {
            textDecoration: "none"
          }
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        rounded: {
          borderRadius: 0
        }
      }
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: "16px",
          background: "#1D1429",
        },
        notchedOutline: {
          borderColor: "#F0BC2B"
        },
      }
    },
    MuiInputLabel: {
      styleOverrides: {
        outlined: {
          color: "rgba(253, 247, 234, 0.3)",
          fontFamily: "Exo"
        },
        root: {
          "&.Mui-focused": {
            color: "#FDF7EA"
          }
        }
      }
    },
    MuiIconButton: {
      styleOverrides: {
        edgeEnd: {
          color: "#FDF7EA"
        }
      }
    }
  }
});

export default theme;
