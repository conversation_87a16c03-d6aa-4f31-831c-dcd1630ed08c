import { Container, Box, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  slideCaption: {
    fontSize: "120px",
    fontFamily: "NeueMachina",
    [theme.breakpoints.down("xs")]: {
      fontSize: "56px",
      lineHeight: "130%",
      marginBottom: "25px",
    }
  },
  slideContainer: {
    padding: "50px 80px",
    display: "inline-block",
    position: "relative",
    zIndex: 0,
    width: "900px",
    background: "rgba(13, 14, 16, 0.7)",
    backdropFilter: "blur(20px)",
    borderRadius: "0px 100px",
    height: "377px",
    "&:before": {
      content: "''",
      position: "absolute",
      zIndex: -1,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      padding: "5px",
      borderRadius: "0px 100px",
      background: "var(--c, linear-gradient(to right, #745CE7, #F0BC2B))",
      WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
      WebkitMaskComposite: "destination-out",
      MaskComposite: "exclude",
    },
    [theme.breakpoints.down("xs")]: {
      width: "327px",
      height: "410px",
      padding: "50px 25px",
      borderRadius: "0px 50px",
      "&:before": {
        padding: "3px",
        borderRadius: "0px 50px",
      }
    },
  },
  subtitle2: {
    fontSize: "20px",
    lineHeight: "25px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
      lineHeight: "130%"
    }
  },
  mainContainer: {
    backgroundImage: "url('game_banner.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover, auto",
    backgroundPosition: "center",
    paddingTop: "100px",
    paddingBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      paddingTop: "65px",
      paddingBottom: "65px",
    }
  }
}));

const Banner = () => {
  const classes = useStyles();

  return (
    <Box className={classes.mainContainer}>
      <Container style={{ textAlign: "center" }}>
        <Box className={classes.slideContainer}>
          <Typography variant="subtitle1" className={classes.slideCaption}>
            Games
          </Typography>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Learn, Play and Earn is the way to go! Come see all the projects running on the Gamestorme Platform. Learn how blockchain and NFT’s really works. Play fun mobile games right in the palm of your hands. Earn crypto while hanging out with your friends in an open world metaverse game! Enjoy each step of the journey you are about to enter!
          </Typography>
        </Box>
      </Container>
    </Box>
  );
}

export default Banner;
