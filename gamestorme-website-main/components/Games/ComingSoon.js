import { Container, Box, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  mainBg: {
    backgroundImage: "url('coming-soon.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "100%, 100%",
    backgroundPosition: "center",
    height: "1068px",
    mixBlendMode: "screen",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
    borderBottom: "1px solid #F0BC2B",
    [theme.breakpoints.down("xs")]: {
      top: "0",
      height: "375px",
      marginTop: "50px",
      paddingBottom: "50px",
      backgroundImage: "url('coming-soon-m.png')",
    }
  },
  title: {
    fontFamily: 'NeueMachina',
    fontStyle: "normal",
    fontWeight: "800",
    fontSize: "56px",
    lineHeight: "130%",
    textAlign: "center",
    color: "#FFFDFA",
    "& span": {
      color: "#F0BC2B"
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
    }
  }
}))

const ComingSoon = () => {
  const classes = useStyles();

  return (
    <Box className={classes.mainBg}>
      <Typography className={classes.title}>
        More Games<br></br>
        <span>Coming Soon!</span>
      </Typography>
    </Box>
  )
}

export default ComingSoon;