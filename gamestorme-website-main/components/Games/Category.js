import { Contain<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import ReactPlayer from 'react-player/lazy'
import clsx from "clsx";

const useStyles = makeStyles((theme) => ({
  slideCaption: {
    fontSize: "56px",
    fontFamily: "NeueMachina",
    marginBottom: "25px",
    marginTop: "70px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      lineHeight: "130%"
    }
  },
  subtitle2: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    marginBottom: "50px",
    marginTop: "100px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "50px",
      marginTop: "30px",
    }
  },
  ctaBtn: {
    [theme.breakpoints.down("xs")]: {
      "& button": {
        width: "100%",
      }
    },
    "& button": {
      padding: "13px 51px",
    }
  },
  descVideo: {
    top: "-44px",
    position: "relative",
    background: "linear-gradient(#000,#000) padding-box, linear-gradient(to top, #F0BC2B, #F0BC2B3D) border-box",
    border: "5px solid transparent",
    borderRadius: "15px",
    display: "inline-block",
    "& div": {
      height: "360px"
    },
    [theme.breakpoints.down("xs")]: {
      top: "-30px",
      border: "3px solid transparent",
      "& div": {
        height: "190px !important"
      }
    }
  },
  descVideo1: {
    top: "-130px",
    position: "relative",
    background: "linear-gradient(#000,#000) padding-box, linear-gradient(to top, #F0BC2B, #F0BC2B3D) border-box",
    border: "5px solid transparent",
    borderRadius: "15px",
    display: "inline-block",
    "& div": {
      height: "360px"
    },
    [theme.breakpoints.down("xs")]: {
      top: "-30px",
      border: "3px solid transparent",
      "& div": {
        height: "190px !important"
      }
    }
  },
  videoContainer: {
    padding: "0px 60px",
    [theme.breakpoints.down("xs")]: {
      padding: "0px 24px",
    }
  },
  bannerImg: {
    backgroundImage: "url('game-banner1.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "100%, 100%",
    backgroundPosition: "center",
    height: "600px",
    borderRadius: "16px",
    [theme.breakpoints.down("xs")]: {
      height: "270px",
      backgroundImage: "url('m-game-banner1.jpeg')",
    }
  },
  bannerImg1: {
    backgroundImage: "url('game-banner2.jpeg')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "100%, 100%",
    backgroundPosition: "center",
    height: "600px",
    borderRadius: "16px",
    [theme.breakpoints.down("xs")]: {
      height: "270px",
      backgroundSize: "cover",
    }
  },
  marginUp: {
    marginTop: "80px",
    [theme.breakpoints.down("xs")]: {
      marginTop: "20px",
    }
  },
  reverseOrder: {
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
  iconBtn: {
    width: "220px",
    height: "60px",
    marginRight: "20px",
    [theme.breakpoints.down("xs")]: {
      width: "150px",
      height: "40px",
    }
  }
}));

const Category = () => {
  const classes = useStyles();

  return (
    <Box pt="50px">
      <Container>
        <Box mb="100px">
          <Box className={classes.bannerImg} data-aos="fade-zoom-in" data-aos-offset="200" data-aos-easing="ease-in-sine" data-aos-duration="600"></Box>
          <Grid container>
            <Grid item md={6} data-aos="fade-right" className={classes.videoContainer}>
              <Box className={classes.descVideo}>
                <ReactPlayer
                  url='skybound.mov'
                  loop={true}
                  playing={true}
                  controls={false}
                  muted={true}
                  width="100%"
                />
              </Box>
            </Grid>
            <Grid item md={6} data-aos="fade-left">
              <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.marginUp)}>
                Life puts you through different ups and downs and even through the ringer. Fortunately Skye does that for a living! Help Skye in being the best dragon around by being #1 and completing different obstacles. Go head to head against your friends and see who is the true master of the sky!
              </Typography>
              <Box display="flex">
                <Link
                  target="_blank"
                  rel="noopener noreferrer" href="https://apps.apple.com/us/app/skybound-gs/id1604096874">
                  <img className={classes.iconBtn} src="app-store.png"></img>
                </Link>
                <Link
                  target="_blank"
                  rel="noopener noreferrer" href="https://play.google.com/store/apps/details?id=com.Gamestorme.Skybound">
                  <img className={classes.iconBtn} src="google-play.png"></img>
                </Link>
              </Box>
            </Grid>
          </Grid>
        </Box>
        <Box>
          <Box className={classes.bannerImg1} data-aos="fade-zoom-in" data-aos-offset="200" data-aos-easing="ease-in-sine" data-aos-duration="600"></Box>
          <Grid container className={classes.reverseOrder}>
            <Grid item md={6} data-aos="fade-right">
              {/* <Typography variant="subtitle1" className={classes.slideCaption}>
                Z-Tea
              </Typography> */}
              <Typography variant="subtitle2" className={classes.subtitle2}>
                In a post apocalyptic world you are left alone with filled with mindless, flesh eating zombies and you have the one weapon that will protect you through it all and it's... A TEA CAN?! Help Zee smack, spray & douse zombies so he can escape the endless hordes that surround his neighborhood in the hopes of finding survivors, different varieties of tea cans or upgrades to help you along the way!
              </Typography>
              <Link className={classes.ctaBtn}>
                <Button variant="outlined">Coming Soon</Button>
              </Link>
            </Grid>
            <Grid item md={6} data-aos="fade-left" className={classes.videoContainer}>
              <Box className={classes.descVideo1} style={{ height: "99%" }}>
                <img src="zombie.gif" width="100%" height="100%" style={{ borderRadius: "10px" }}></img>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </Box>
  );
}

export default Category;
