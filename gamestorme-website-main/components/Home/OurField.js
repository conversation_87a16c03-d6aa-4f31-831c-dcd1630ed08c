import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";


const useStyles = makeStyles((theme) => ({
  content: {
    height: "900px",
    [theme.breakpoints.down("xs")]: {
      height: "600px",
    }
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      marginBottom: "25px"
    }
  },
  subtitle2: {
    marginBottom: "50px",
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px"
    }
  },
  highlight: {
    paddingRight: "15px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  title: {
    color: "#F0BC2B",
    marginBottom: "25px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "156px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "97px",
      marginBottom: "15px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  ctaBtn: {
    [theme.breakpoints.down("xs")]: {
      width: "100%",
    },
    "& button": {
      padding: "13px 54px",
      width: "100%",
    }
  },
  bgContainer: {
    backgroundImage: "url('main/bg.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "auto, 900px",
    backgroundPosition: "top left",
    borderBottom: "1px solid rgba(240, 188, 43, 0.3)",
    [theme.breakpoints.down("xs")]: {
      background: "none"
    }
  },
  mobileContainer: {
    display: "none",
    [theme.breakpoints.down("xs")]: {
      display: "block",
      height: "500px",
      backgroundImage: "url('main/bg.png')",
      backgroundRepeat: "no-repeat",
      backgroundSize: "cover, 500px",
      backgroundPosition: "top left",
      borderTop: "1px solid rgba(240, 188, 43, 0.3)",
    }
  }
}));

const OurField = () => {
  const classes = useStyles();

  return (
    <Box className={classes.bgContainer}>
      <Container>
        <Grid container >
          <Grid container item xs={12} md={6} />
          <Grid container item xs={12} md={6}>
            <Box alignItems="baseline" justifyContent="center" display="flex" flexDirection="column" className={classes.content} textAlign="left">
              <Box className={classes.title}>ABOUT US</Box>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                We are Experts in<br></br><span className={classes.highlight}>Our Field </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Come get to know the team behind Gamestorme! Follow the powerful journey the team had to go through from hurricanes and earthquakes to become the first Blockchain Gaming company in Puerto Rico.
              </Typography>
              <Link href="/about" className={classes.ctaBtn}>
                <Button variant="outlined">Read more</Button>
              </Link>
            </Box>
          </Grid>
        </Grid>
      </Container>
      <Box className={classes.mobileContainer}>
      </Box>
    </Box>
  );
}

export default OurField;
