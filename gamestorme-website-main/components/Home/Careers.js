import {
  Container,
  <PERSON>ton,
  Card,
  CardMedia,
  Grid,
  Box,
  Typography,
  TextField,
  CircularProgress
} from "@mui/material";
import { styled } from "@mui/material/styles";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useState, useCallback } from "react";
import DropdownZone from "./DropdownZone";
import axios from "axios";
import { toast } from 'react-toastify';

// Styled components for better performance
const StyledCardMedia = styled(CardMedia)(() => ({
  transition: "transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
  '&:hover': {
    transform: 'scale(1.05)',
    cursor: 'pointer'
  }
}));

const StyledCard = styled(Card)(() => ({
  borderRadius: "16px",
  width: "100%",
  position: "relative",
  background: "none",
  boxShadow: "0px 16px 16px rgba(0, 0, 0, 0.26)",
  overflow: 'hidden',
}));

const TitleBox = styled(Box)(() => ({
  color: "#F0BC2B",
  marginBottom: "10px",
  fontSize: "16px",
  fontWeight: "normal",
  fontFamily: "Exo",
  width: "156px",
  display: "flex",
  alignItems: "center",
  "&:before": {
    content: "''",
    flexGrow: 1,
    background: "#F0BC2B",
    height: "1px",
    fontSize: "0px",
    lineHeight: "0px",
    marginRight: "20px",
    marginBottom: "3px",
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: "12px",
    width: "90px",
    marginBottom: "15px",
    marginTop: "50px",
    "&:before": {
      marginRight: "10px",
    }
  }
}));

const HighlightSpan = styled('span')({
  paddingRight: "30px",
  backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
});

const MainContentBox = styled(Box)(({ theme }) => ({
  marginTop: "200px",
  paddingBottom: "200px",
  borderBottom: "1px solid #F0BC2B",
  [theme.breakpoints.down('sm')]: {
    marginTop: "100px",
    paddingBottom: "100px",
  }
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& label": {
    fontFamily: "Exo",
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "18px",
    lineHeight: "150%",
    color: "#A9AFC3",
  },
  [theme.breakpoints.down('sm')]: {
    "& label": {
      fontSize: "16px"
    }
  },
  "& label + .MuiInput-formControl": {
    marginTop: "30px"
  },
  "& .MuiInput-underline:before": {
    borderBottom: "1px solid #F0BC2B"
  },
  "& .MuiInput-underline:hover:not(.Mui-disabled):before": {
    borderBottom: "1px solid #F0BC2B"
  },
  "& .MuiInputBase-input": {
    color: "#fff"
  },
  "& .MuiInput-underline:after": {
    borderBottom: "1px solid #F0BC2B"
  },
  width: "100%",
  marginBottom: "0px"
}));

const DropzoneLabel = styled(Typography)(() => ({
  fontFamily: 'Exo',
  fontStyle: "normal",
  fontWeight: "400",
  fontSize: "18px",
  lineHeight: "150%",
  color: "#A9AFC3",
  marginTop: "20px",
  marginBottom: "15px"
}));

const ConfirmImage = styled('img')({
  filter: "drop-shadow(0px 16px 16px rgba(0, 0, 0, 0.26))",
  marginBottom: "50px",
  maxWidth: '100%',
  height: 'auto',
});

const SubmittedText = styled(Typography)({
  fontFamily: 'NeueMachina',
  fontStyle: "normal",
  fontWeight: "800",
  fontSize: "24px",
  lineHeight: "130%",
  textAlign: "center",
  color: "#FFFDFA",
  marginBottom: "50px"
});

const SendAgainButton = styled(Box)({
  display: "flex",
  alignItems: "center",
  fontFamily: 'Exo',
  justifyContent: "center",
  fontStyle: "normal",
  fontWeight: "500",
  fontSize: "18px",
  lineHeight: "24px",
  color: "#F0BC2B",
  borderBottom: "1px solid #F0BC2B",
  paddingBottom: "5px",
  cursor: "pointer",
  transition: "transform 0.2s ease",
  "&:hover": {
    transform: "translateY(-2px)",
  },
  "& svg": {
    marginRight: "20px"
  }
});

const Careers = () => {
  // State management with React hooks
  const [submitted, setSubmitted] = useState(false);
  const [name, setName] = useState('');
  const [lastname, setLastname] = useState('');
  const [email, setEmail] = useState('');
  const [cv, setCv] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [formErrors, setFormErrors] = useState({
    name: false,
    lastname: false,
    email: false,
    cv: false
  });

  // Handle file drop from DropdownZone component
  const handleDrop = useCallback(file => {
    setCv(file);
    setFormErrors(prev => ({ ...prev, cv: false }));
  }, []);

  // Validate email format
  const validateEmail = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Form submission handler
  const submit = async () => {
    // Form validation
    const errors = {
      name: !name,
      lastname: !lastname,
      email: !email || !validateEmail(email),
      cv: !cv
    };

    setFormErrors(errors);

    if (Object.values(errors).some(error => error)) {
      toast.error(`Please fill all required fields correctly.`);
      return;
    }

    // Create form data for submission
    const formData = new FormData();
    formData.append('name', name);
    formData.append('lastname', lastname);
    formData.append('email', email);
    formData.append('cv', cv);

    setUploading(true);

    try {
      const { data } = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/register`, formData, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          'Content-Type': 'multipart/form-data'
        }
      });

      if (data.success) {
        toast.success(`Your application has been successfully submitted!`);
        setUploading(false);
        setSubmitted(true);
      } else {
        setUploading(false);
        toast.error(data.message || `Submission failed. Please try again.`);
      }
    } catch (err) {
      setUploading(false);
      toast.error(`An error occurred. Please try again later.`);
      console.error("Form submission error:", err);
    }
  };

  // Reset form to submit again
  const sendAgain = () => {
    setSubmitted(false);
    setName('');
    setLastname('');
    setEmail('');
    setCv(null);
    setFormErrors({
      name: false,
      lastname: false,
      email: false,
      cv: false
    });
  };

  return (
    <MainContentBox>
      <Container>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <TitleBox>CAREERS</TitleBox>
            <Typography
              variant="subtitle1"
              sx={{
                fontFamily: "NeueMachina",
                marginBottom: "30px",
                fontSize: { xs: "33px", sm: "56px" }
              }}
            >
              Are you the<br /> <HighlightSpan>chosen one!?</HighlightSpan>
            </Typography>
            <Typography
              variant="body1"
              sx={{
                fontSize: { xs: "14px", sm: "18px" },
                color: "#A9AFC3",
                fontWeight: "normal",
                marginBottom: "10px",
                lineHeight: "150%"
              }}
            >
              Do you have a passion for gaming or blockchain and are ready to join a tight knit team in revolutionizing the gaming space? Think you have some value you can bring to the gamestorme ecosystem? If so, we would love to hear from you! Come and join our team of talented artists, developers, engineers, marketers, writers, and legal experts
            </Typography>

            {submitted ? (
              <Box textAlign='center'>
                <ConfirmImage src="confirm.png" alt="Application submitted" />
                <SubmittedText>Your application has been successfully submitted!</SubmittedText>
                <Box display="flex" justifyContent="center">
                  <SendAgainButton onClick={sendAgain}>
                    <ArrowBackIcon />
                    Send again
                  </SendAgainButton>
                </Box>
              </Box>
            ) : (
              <Box>
                {uploading ? (
                  <Box
                    height="476px"
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <CircularProgress size="100px" color="secondary" />
                  </Box>
                ) : (
                  <>
                    <StyledTextField
                      id="name-input"
                      variant="standard"
                      type="text"
                      label="First name"
                      value={name}
                      onChange={(e) => {
                        setName(e.target.value);
                        setFormErrors(prev => ({ ...prev, name: false }));
                      }}
                      error={formErrors.name}
                      helperText={formErrors.name ? "First name is required" : ""}
                      fullWidth
                      margin="normal"
                    />
                    <StyledTextField
                      id="lastname-input"
                      variant="standard"
                      type="text"
                      label="Last name"
                      value={lastname}
                      onChange={(e) => {
                        setLastname(e.target.value);
                        setFormErrors(prev => ({ ...prev, lastname: false }));
                      }}
                      error={formErrors.lastname}
                      helperText={formErrors.lastname ? "Last name is required" : ""}
                      fullWidth
                      margin="normal"
                    />
                    <StyledTextField
                      id="email-input"
                      variant="standard"
                      type="email"
                      label="E-mail"
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        setFormErrors(prev => ({ ...prev, email: false }));
                      }}
                      error={formErrors.email}
                      helperText={formErrors.email ? "Valid email is required" : ""}
                      fullWidth
                      margin="normal"
                    />
                    <DropzoneLabel variant="subtitle1">
                      Attach your CV / Or Portfolio Files
                    </DropzoneLabel>
                    <DropdownZone
                      handleDrop={handleDrop}
                    />
                    {formErrors.cv && (
                      <Typography
                        color="error"
                        variant="caption"
                        sx={{ display: 'block', mt: -4, mb: 2 }}
                      >
                        Please attach your CV or portfolio
                      </Typography>
                    )}
                    <Button
                      variant="outlined"
                      onClick={submit}
                      sx={{
                        padding: "13px 70px",
                        width: { xs: "100%", sm: "auto" }
                      }}
                    >
                      Submit
                    </Button>
                  </>
                )}
              </Box>
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            <StyledCard>
              <StyledCardMedia
                component="img"
                alt="Gamestorme"
                image="dream-come.png"
                title="Gamestorme"
              />
            </StyledCard>
          </Grid>
        </Grid>
      </Container>
    </MainContentBox>
  );
}

export default Careers;
