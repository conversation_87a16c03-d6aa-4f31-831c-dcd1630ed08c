import { makeStyles } from "@material-ui/core/styles";
import Card from "@material-ui/core/Card";
import CardMedia from "@material-ui/core/CardMedia";
import Typography from "@material-ui/core/Typography";
import { Box } from "@material-ui/core";
import Image from "next/image";

const useStyles = makeStyles({
  media: {
    transition: "transform .6s",
  },
  cardTitle: {
    fontSize: "18px"
  },
  cardContent: {
    position: "absolute",
    bottom: "25px",
    width: "100%",
    textAlign: "center",
    color: "#fff",
    background: "none",
    paddingLeft: "25px",
    paddingRight: "25px"
  },
  cardBody: {
    width: "100%",
    position: "relative",
    background: "none",
    boxShadow: "0px 16px 16px rgba(0, 0, 0, 0.26)",
    "&:hover > img": {
      transform: "scale(1.05)",
      cursor: "pointer"
    }
  }
});

export default (props) => {
  const classes = useStyles();

  return (
    <Card className={classes.cardBody}>
      <CardMedia
        component="img"
        alt="Gamestorme"
        image={props.img}
        title="Gamestorme"
        className={classes.media}
      />
      {
        props.title &&
        (<Box className={classes.cardContent} display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle2" className={classes.cardTitle}>
            {props.title}
          </Typography>
          <Image src="/next.png" width="36px" height="36px" loading="eager"></Image>
        </Box>)
      }
    </Card>
  );
}