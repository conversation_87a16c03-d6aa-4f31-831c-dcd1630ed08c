import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader
import { Carousel } from 'react-responsive-carousel';
import { Con<PERSON>er, Button, Link, Grid, Box, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import Image from "next/image";

const useStyles = makeStyles((theme) => ({
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "25px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      lineHeight: "130%"
    }
  },
  subtitle2: {
    marginBottom: "50px",
    fontSize: "24px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "18px",
      lineHeight: "130%"
    }
  },
  highlight: {
    paddingRight: "15px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  outlinedButton: {
    [theme.breakpoints.down("xs")]: {
      width: "100%",
      "& button": {
        width: "100%"
      },
    },
    "& button": {
      padding: "13px 68px",
    },
  },
  prevBtn: {
    top: "45%",
    cursor: "pointer",
    [theme.breakpoints.up("md")]: {
      left: "5%"
    },
    [theme.breakpoints.down("xs")]: {
      top: "86.5%",
      left: "5%"
    }
  },
  nextBtn: {
    top: "45%",
    right: "0",
    cursor: "pointer",
    [theme.breakpoints.up("md")]: {
      right: "5%"
    },
    [theme.breakpoints.down("xs")]: {
      top: "86.5%",
      right: "5%"
    }
  },
  firstBanner: {
    backgroundImage: "url('main/1.jpeg')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover",
    backgroundPosition: "top",
    height: "800px",
    [theme.breakpoints.down("xs")]: {
      backgroundImage: "url('main/m-1.png')",
      height: "636px",
    }
  },
  secondBanner: {
    backgroundImage: "url('main/2.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover, auto",
    backgroundPosition: "center",
    height: "800px",
    [theme.breakpoints.down("xs")]: {
      height: "636px",
    }
  }
}));

const Slideshow = () => {
  const classes = useStyles();

  return (
    <Carousel
      autoPlay={false}
      autoPlaySpeed={3000}
      statusFormatter={() => ''}
      showThumbs={false}
      swipeable={true}
      emulateTouch={true}
      infiniteLoop={true}
      renderArrowPrev={(onClickHandler, hasPrev, label) => {
        return hasPrev && (
          <Box onClick={onClickHandler} position="absolute" zIndex={1000} className={classes.prevBtn}>
            <Image src="/prev.png" width="40px" height="40px"></Image>
          </Box>
        )
      }}
      renderArrowNext={(onClickHandler, hasNext, label) => {
        return hasNext && (
          <Box onClick={onClickHandler} position="absolute" zIndex={1000} className={classes.nextBtn}>
            <Image src="/next.png" width="40px" height="40px"></Image>
          </Box>
        )
      }}
    >
      {/* <Box className={classes.firstBanner}>
        <Container>
          <Grid container >
            <Grid container item xs={12} md={6}>
              <Box alignItems="baseline" justifyContent="center" display="flex" flexDirection="column" height="650px" textAlign="left">
                <Typography variant="subtitle1" className={classes.slideCaption}>
                  Taking the<br></br><span className={classes.highlight}>Gaming industry </span><br></br> by <span style={{ color: "#F0BC2B" }}>Storme</span>
                </Typography>
                <Typography variant="subtitle2" className={classes.subtitle2}>
                  With the vision of completely changing the gaming industry with blockchain. Gamestorme is here to complete the missing link!
                </Typography>
                <Link href="/" className={classes.outlinedButton}>
                  <Button variant="outlined">Explore</Button>
                </Link>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box> */}
      <Box className={classes.secondBanner}>
        <Container>
          <Grid container >
            <Grid container item xs={12} md={6}>
              <Box alignItems="baseline" justifyContent="center" display="flex" flexDirection="column" height="650px" textAlign="left">
                <Typography variant="subtitle1" className={classes.slideCaption}>
                  <span style={{ color: "#F0BC2B" }}>Our mission</span>
                </Typography>
                <Typography variant="subtitle2" className={classes.subtitle2}>
                  Building a forward-thinking game studio with gamers at its core. The future of monetization, ownership, and content creation by gamers for gamers
                </Typography>
                {/* <Link href="/" className={classes.outlinedButton}>
                  <Button variant="outlined">Explore</Button>
                </Link> */}
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Carousel>
  );
}

export default Slideshow;
