import React, { useMemo, useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, Typography, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

// Styled components for better performance
const DropzoneContainer = styled(Paper)(({ theme, isFocused, isDragAccept, isDragReject }) => ({
  padding: '33px 0',
  borderWidth: "1px",
  borderRadius: "16px",
  borderColor: isDragReject ? '#ff1744' : isDragAccept ? '#00e676' : isFocused ? '#2196f3' : '#F0BC2B',
  borderStyle: 'dashed',
  backgroundColor: '#1D1429',
  color: '#fff',
  transition: 'border .24s ease-in-out, transform 0.2s ease',
  width: "100%",
  marginBottom: "50px",
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 5px 10px rgba(0, 0, 0, 0.2)',
  }
}));

const DropzoneText = styled(Typography)(({ theme, isAttached }) => ({
  fontWeight: isAttached ? "bold" : "bold",
  fontStyle: "normal",
  fontSize: "18px",
  lineHeight: isAttached ? "54px" : "150%",
  color: "#A9AFC3",
  textAlign: "center",
}));

const BrowseText = styled('span')({
  color: "#FFFDFA",
  textDecoration: "underline",
  fontWeight: "500",
});

const DropdownZone = ({ handleDrop }) => {
  const [dropped, setDropped] = useState(false);
  const [fileName, setFileName] = useState('');

  const onDrop = useCallback(acceptedFiles => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      setDropped(true);
      setFileName(acceptedFiles[0].name);
      handleDrop(acceptedFiles[0]);
    }
  }, [handleDrop]);

  const {
    getRootProps,
    getInputProps,
    isFocused,
    isDragAccept,
    isDragReject
  } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png']
    },
    maxSize: 5242880, // 5MB
  });

  return (
    <DropzoneContainer
      elevation={0}
      isFocused={isFocused}
      isDragAccept={isDragAccept}
      isDragReject={isDragReject}
      {...getRootProps()}
    >
      <input {...getInputProps()} />
      <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center">
        {!dropped ? (
          <>
            <CloudUploadIcon sx={{ fontSize: 40, color: '#F0BC2B', mb: 1 }} />
            <DropzoneText isAttached={false}>
              Drag & Drop CV<br />
              <BrowseText>Browse File</BrowseText>
            </DropzoneText>
            <Typography variant="caption" sx={{ color: '#A9AFC3', mt: 1 }}>
              Supported formats: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (Max: 5MB)
            </Typography>
          </>
        ) : (
          <>
            <CheckCircleIcon sx={{ fontSize: 40, color: '#00e676', mb: 1 }} />
            <DropzoneText isAttached={true}>
              Resume has been attached
            </DropzoneText>
            <Typography variant="caption" sx={{ color: '#A9AFC3' }}>
              {fileName}
            </Typography>
          </>
        )}
      </Box>
    </DropzoneContainer>
  );
};

export default React.memo(DropdownZone);