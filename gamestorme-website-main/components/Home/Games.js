import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import ImgMediaCard from "./ImgMediaCard";

const useStyles = makeStyles((theme) => ({
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
    }
  },
  subtitleContent: {
    height: "230px",
    marginLeft: "60px",
    marginTop: "78px",
    [theme.breakpoints.down("xs")]: {
      marginLeft: "0px",
      marginTop: "25px",
    }
  },
  subtitle2: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "50px"
    }
  },
  highlight: {
    paddingRight: "30px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  title: {
    color: "#F0BC2B",
    marginBottom: "25px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "156px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "80px",
      marginBottom: "15px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  cardBlock: {
    [theme.breakpoints.down("md")]: {
      marginBottom: "25px"
    }
  },
  ctaBtn: {
    [theme.breakpoints.down("xs")]: {
      "& button": {
        width: "100%",
      }
    },
    "& button": {
      padding: "13px 36px",
    }
  }
}));

const Games = () => {
  const classes = useStyles();

  return (
    <Box mt="100px">
      <Container>
        <Grid container>
          <Grid container item xs={12} md={6} justifyContent="space-between" style={{ display: "flex", flexDirection: "column" }}>
            <Box textAlign="left">
              <Box className={classes.title}>GAMES</Box>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                Enter the new Realm
                <br></br><span className={classes.highlight}>of mobile Gaming! </span>
              </Typography>
            </Box>
            <Grid container>
              <Grid item md={6} className={classes.cardBlock}>
                <ImgMediaCard img="/assets/images/games/agueybana.jpg"></ImgMediaCard>
              </Grid>
              <Grid item md={6} />
            </Grid>
          </Grid>
          <Grid container item xs={12} md={6} className={classes.cardBlock}>
            <ImgMediaCard img="/assets/images/games/z-tea.png"></ImgMediaCard>
          </Grid>
        </Grid>
        <Grid container>
          <Grid container item xs={12} md={6}>
            <Grid item md={6} />
            <Grid item md={6} className={classes.cardBlock}>
              <ImgMediaCard img="/assets/images/games/hajimari.jpg"></ImgMediaCard>
            </Grid>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" justifyContent="space-between" flexDirection="column" className={classes.subtitleContent}>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Learn, Play and Earn is the way to go! Come see all the projects running on the Gamestorme Platform. Learn how blockchain and NFT’s really works. Play fun mobile games right in the palm of your hands. Earn crypto while hanging out with your friends in an open world metaverse game! Enjoy each step of the journey you are about to enter!
              </Typography>
              <Link href="/games" className={classes.ctaBtn}>
                <Button variant="outlined">See all projects</Button>
              </Link>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box >
  );
}

export default Games;
