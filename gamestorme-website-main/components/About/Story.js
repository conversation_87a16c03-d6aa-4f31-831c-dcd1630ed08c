import { Container, Grid, Box, Typography } from "@material-ui/core";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import clsx from "clsx";

const useStyles = makeStyles((theme) => ({
  mainContent: {
    marginTop: "100px",
    paddingBottom: "100px",
    borderBottom: "1px solid #F0BC2B",
    [theme.breakpoints.down("xs")]: {
      marginTop: "50px",
      paddingBottom: "50px",
    }
  },
  mainTitle: {
    display: "flex",
    justifyContent: "center"
  },
  highlight: {
    paddingLeft: "30px",
    paddingRight: "30px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
    }
  },
  slideCaption1: {
    fontFamily: "NeueMachina",
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
    }
  },
  subtitle3: {
    fontSize: "24px",
    color: "#FFFDFA",
    fontWeight: "600",
    lineHeight: "150%",
    marginBottom: "200px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "100px",
    }
  },
  storyImg: {
    borderRadius: "16px",
    boxShadow: "0 8px 32px rgba(0,0,0,0.15)",
    border: "2px solid rgba(240, 188, 43, 0.2)",
    transition: "all 0.3s ease",
    "&:hover": {
      transform: "translateY(-4px)",
      boxShadow: "0 12px 40px rgba(0,0,0,0.2)",
      border: "2px solid rgba(240, 188, 43, 0.4)",
    },
    [theme.breakpoints.down("xs")]: {
      width: "327px"
    }
  },
  journey: {
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "50px",
    }
  },
  subtitle2: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    marginBottom: "50px",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "25px",
    }
  },
  subtitle1: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    marginBottom: "100px",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "50px",
    }
  },
  introTitle: {
    fontFamily: "NeueMachina",
    fontSize: "38px",
    marginBottom: "15px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "26px",
    }
  },
  introTitle1: {
    fontFamily: "NeueMachina",
    fontSize: "28px",
    color: "#F0BC2B",
    textAlign: "center",
    marginBottom: "25px",
    flex: "none",
    order: "0",
    flexGrow: "0",
    [theme.breakpoints.down("xs")]: {
      fontSize: "22px",
      lineHeight: "21px",
      marginBottom: "15px",
    }
  },
  circle: {
    width: "110px",
    height: "110px",
    background: "linear-gradient(270deg, #4229BC 0%, #7B65ED 100%)",
    borderRadius: "50%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    [theme.breakpoints.down("xs")]: {
      width: "100px",
      height: "100px",
    }
  },
  subtitle4: {
    flex: "none",
    order: "1",
    flexGrow: "0",
    fontWeight: "400",
    fontSize: "18px",
    lineHeight: "27px",
    textAlign: "center",
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      fontWeight: "normal",
      lineHeight: "120%"
    }
  },
  journeyBlock: {
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "50px",
    }
  },
  introCircle: {
    width: "570px",
    height: "570px",
    paddingLeft: "85px",
    paddingRight: "85px",
    background: "linear-gradient(#0B0F1E,#0B0F1E) padding-box, linear-gradient(to top, #F0BC2B, #F0BC2B3D) border-box",
    border: "3px solid transparent",
    borderRadius: "50%",
    boxShadow: "0px 20px 40px rgba(0, 0, 0, 0.3), 0px 8px 16px rgba(240, 188, 43, 0.1)",
    transition: "all 0.3s ease",
    "&:hover": {
      transform: "translateY(-8px)",
      boxShadow: "0px 25px 50px rgba(0, 0, 0, 0.35), 0px 12px 24px rgba(240, 188, 43, 0.2)",
    },
    [theme.breakpoints.down("xs")]: {
      width: "327px",
      height: "327px",
      paddingLeft: "35px",
      paddingRight: "35px",
      marginBottom: "50px",
    }
  },
  profileTitle: {
    fontSize: "36px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "18px",
    }
  },
  profileName: {
    fontSize: "38px",
    fontWeight: "700",
    [theme.breakpoints.down("xs")]: {
      fontSize: "22px",
    }
  },
  profileName1: {
    fontSize: "29px",
    fontWeight: "700",
    [theme.breakpoints.down("xs")]: {
      fontSize: "18px",
    }
  },
  flag: {
    marginLeft: "20px",
    [theme.breakpoints.down("xs")]: {
      height: "28px"
    }
  },
  flag1: {
    marginLeft: "20px",
    [theme.breakpoints.down("xs")]: {
      height: "25px",
      marginLeft: "15px",
    }
  },
  profileContent: {
    paddingLeft: "0px",
    paddingRight: "70px",
    [theme.breakpoints.down("xs")]: {
      paddingLeft: "20px",
      paddingRight: "20px",
    }
  },
  profileArea: {
    top: "-80px",
    position: "relative",
    [theme.breakpoints.down("xs")]: {
      top: "-37px",
    }
  },
  profilePicArea: {
    position: "relative",
    top: "-40px",
    color: "#F0BC2B",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "90%",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      width: "100%",
      top: "-23px",
    }
  },
  profileImg: {
    width: "100px",
    height: "100px",
    borderRadius: "50%",
    [theme.breakpoints.down("xs")]: {
      width: "65px",
      height: "65px",
    }
  },
  picBorder: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "116px",
    height: "116px",
    border: "3px solid #F0BC2B",
    borderRadius: "50%",
    boxShadow: "0 4px 16px rgba(240, 188, 43, 0.3)",
    transition: "all 0.3s ease",
    "&:hover": {
      transform: "scale(1.05)",
      boxShadow: "0 6px 24px rgba(240, 188, 43, 0.4)",
    },
    [theme.breakpoints.down("xs")]: {
      width: "75px",
      height: "75px",
    }
  },
  profileContainer: {
    marginBottom: "50px",
    // "&:first-child": {
    //   marginBottom: "100px",
    // },
    [theme.breakpoints.down("xs")]: {
      marginBottom: "0px",
      "&:first-child": {
        marginBottom: "0px",
      },
    }
  },
  profileContainer1: {
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "0px",
    }
  },
  rightSide: {
    marginTop: "270px",
    [theme.breakpoints.down("xs")]: {
      marginTop: "0px",
    }
  },
  reverseOrder: {
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
  rightBlock: {
    marginBottom: "200px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "0px"
    }
  },
  deco: {
    backgroundImage: "url('line-dec.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "auto, 100%",
    backgroundPosition: "center",
    marginBottom: "200px",
    [theme.breakpoints.down("xs")]: {
      backgroundImage: "url('line-dec2.png')",
      marginBottom: "100px",
    }
  },
  deco1: {
    width: "100%"
  },
  rightFlex: {
    display: "flex",
    justifyContent: "end",
    alignItems: "center",
    [theme.breakpoints.down("xs")]: {
      justifyContent: "center",
    }
  },
  leftFlex: {
    display: "flex",
    alignItems: "center",
    [theme.breakpoints.down("xs")]: {
      justifyContent: "center",
    }
  },
  storyLeft: {
    display: "flex",
    justifyContent: "start",
    [theme.breakpoints.down("xs")]: {
      justifyContent: "center",
    }
  },
  storyRight: {
    display: "flex",
    justifyContent: "end",
    [theme.breakpoints.down("xs")]: {
      justifyContent: "center",
    }
  }
}));


const Story = () => {
  const classes = useStyles();
  const theme = useTheme();
  const iOS = process.browser && /iPad|iPhone|iPod/.test(navigator.userAgent);
  const matches = useMediaQuery(theme.breakpoints.down("sm"));

  const FirstStory = () => (
    <Box className={classes.introCircle} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Typography variant="subtitle1" className={classes.introTitle1}>
        Gamestorme Founded
      </Typography>
      <Typography variant="subtitle2" className={classes.subtitle4}>
        Gamestorme was founded by Joel and Adrian in mid-2017 from their passion in game design and development. After a few weeks of discussion, their first game idea was born: Project Hop. To bring Project Hop to life, they needed talented artists. Thus enters Ercrypto and Jorge, our two beloved artists that have been with us throughout all the ups and downs.
      </Typography>
    </Box>
  )

  const SecondStory = () => (
    <Box className={classes.introCircle} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Typography variant="subtitle1" className={classes.introTitle1}>
        Hurricane Maria
      </Typography>
      <Typography variant="subtitle2" className={classes.subtitle4}>
        Midway through developing Project Hop however, Puerto Rico got hit by Hurricane Irma and the devastating Hurricane Maria, wiping out all development for Project Hop in the process. Months of no power and no water meant that everyone had to find ways to adapt and survive in their new post Maria lifestyle. Some members ended up leaving for the US, while others chose to stay and rebuild what was lost. This unfortunately put a hold on Gamestorme's development.
      </Typography>
    </Box>
  )

  const ThirdStory = () => (
    <Box className={classes.introCircle} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Typography variant="subtitle1" className={classes.introTitle1}>
        Rebuilding
      </Typography>
      <Typography variant="subtitle2" className={classes.subtitle4}>
        In late 2019, Joel contacted Adrian in hopes of restarting Gamestorme, this time as a blockchain gaming company. To reflect this, Ali and Confessor (smart contract developer and game developer respectively) were added to team.
      </Typography>
    </Box>
  )

  const FourthStory = () => (
    <Box className={classes.introCircle} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Typography variant="subtitle1" className={classes.introTitle1}>
        Covid-19
      </Typography>
      <Typography variant="subtitle2" className={classes.subtitle4}>
        Once Covid hit, we found ourselves underfunded and once again had to put development on hold. While the project was not moving, Adrian managed to move his way up from working at as an IT specialist to becoming a software engineer, Joel was using his time outside his day job to hone his skills in investing, smart contract development and business management, and the rest of we were all honing their skills in their perspective fields. In August of 2021, Teagan joined Gamestorme with the funds to start development again.
      </Typography>
    </Box>
  )

  const FifthStory = () => (
    <Box className={classes.introCircle} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Typography variant="subtitle1" className={classes.introTitle1}>
        A New Plan
      </Typography>
      <Typography variant="subtitle2" className={classes.subtitle4}>
        Seeing the potential of the team, Teagan became project manager for the company and worked to fulfill Gamestorme's mission by introducing PNFT's, a mobile specific crypto-wallet, and the NFT marketplace. With the idea of PNFT's, we applied for Solana's hackathon and were blown away by the support of Solana’s developer community. We met some extremely talented individuals, one of which ended up joining our team—Proharvester—whom helped us build our website, mint page functionality, and mint our NFT’s.
      </Typography>
    </Box>
  )

  const SixthStory = () => (
    <Box className={classes.introCircle} display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Typography variant="subtitle1" className={classes.introTitle1}>
        The Journey Begins
      </Typography>
      <Typography variant="subtitle2" className={classes.subtitle4}>
        It has been an amazing journey filled with ups and downs. The people we’ve met along the way have been nothing less than a blessing, and we hope to bring as many along with us as possible through this journey started by a couple of gamers. As the Storme has only just begun.
      </Typography>
    </Box>
  )

  const Member1 = () => (
    <Box className={classes.profileContainer}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}> CEO/Co-Founder</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_085151.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Joel Rodriguez
          </Typography>
          <img className={classes.flag} src="pr.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Joel, or "Grillo" as those close to have fondly named him, is a musician and a composer with a passion for technology. Joel has been pursuing a career in game music composition since 2012 after enrolling in Full Sail University in music production.
            <br></br>
            <br></br>
            The year 2017 was a pivotal point that marked Joel for the rest of his life as he was introduced to the world of cryptocurrencies and blockchain technology. After enduring many challenges among them multiple natural disasters and the pandemic, Joel continues to thrive as the CEO of Gamestorme. Being a pioneer as the first Blockchain Gaming publisher in Puerto Rico.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member2 = () => (
    <Box className={classes.profileContainer}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>CTO/Co-Founder</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_090926.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Adrian Acosta
          </Typography>
          <img className={classes.flag} src="pr.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Adrian came across a programing class while studying music production in Full Sail University which made him think about the career path he was pursuing. At this time he had a vision of contributing to new technologies with the power of coding.
            As of then, Adrian finished his bachelor's degree in Information Technology with a Major in Software Analysis and Development from NUC University. During this time he began the joint venture with "Grillo" which we now know as Gamestorme! Together they have the ambitious idea of creating a tech hub in Puerto Rico, as they begin the first Blockchain Gaming company in the island.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member3 = () => (
    <Box className={classes.profileContainer}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>COO</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_090209.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Teagan Buckley
          </Typography>
          <img className={classes.flag} src="us.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Teagan is finishing up his bachelors in Mechanical Engineering at the University of Washington. While working as an advisor for various blockchain protocols, he was able to cultivate the knowledge about the blockchain space and how the operations of blockchain protocols differ from traditional businesses. The focus around community, transparency, and collaboration in the blockchain space is what drew Teagan in. The blockchain space is constantly evolving, and Teagan's goals is to make sure Gamestorme is always staying at the forefront of these innovations.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member4 = () => (
    <Box className={classes.profileContainer1}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>CBTO</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220627_111800.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Jackson Piao
          </Typography>
          <img className={classes.flag} src="hk.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Jackson graduated from the University of Honk Kong in 2014 and since then has gained hands on experience as full stack/block chain developer.
            <br></br>
            Jackson has been involved with multiple blockchain projects, bringing over 4 years of crypto experience to Gamestorme as Chief Blockchain Officer. Most recently he has been fully immersed in the Solana blockchain, learning how to harvest its power to its maximum potential. In addition, he’s helped shaped Gamestorme’s vision of the pNFT concept and bring it to life.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member5 = () => (
    <Box className={classes.profileContainer1}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        CAO<br></br>
        <span style={{ color: "#F0BC2B" }}>Game Dev</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_090306.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Confessor Rodriguez
          </Typography>
          <img className={classes.flag} src="pr.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Confessor began his studies for programming and networking in 2014 with dreams of someday working in the video game industry. This dream took an unfortunate twist and for some time seemed out of reach. Fast-forward to September 2017, he met Joel whilst working as a telemarketer. Shortly after, Joel asked Confessor to  join him in building Gamestorme.
            <br></br>
            Since then the dream of working in video games has reignited and he is now working alongside of experts in the field of crypto and gaming as CAO for Gamestorme. Confessor is lead in the gaming development team and is endlessly working to put Gamestorme on the map.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member7 = () => (
    <Box className={classes.profileContainer1}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>CHRO</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_083756.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName1}>
            Lizmarie Roldán Valentín
          </Typography>
          <img className={classes.flag1} src="pr.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Lizmarie Roldan Valentin has been working in various roles throughout the Human Resources field. Since 2017, she has helped multiple companies succeed in areas such as talent acquisition, department policies and onboarding.
            <br></br>
            As she courses to finish her bachelors in Business Administration, she plays a pivotal role in Gamestorme's rapid growth. Her expertise brings guidance and helps keep the team tethered all within our virtual environment.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member8 = () => (
    <Box className={classes.profileContainer1}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>CRO</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_082914.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Jermian Román
          </Typography>
          <img className={classes.flag} src="pr.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Jermian began his interest in electronics from an early age, this inspired him to obtain a vocational degree in industrial electronics followed by a technician's degree in biomedical technology.
            <br></br>
            Always achieving high levels of performance, he has excelled as a sales executive for various organizations, allowing him to fine tune his abilities with customer relations. In addition, he brings exceptional research skills well needed for our fast paced company.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member9 = () => (
    <Box className={classes.profileContainer1}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>CCO</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_083610.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Carlos Badillo
          </Typography>
          <img className={classes.flag} src="pr.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Carlos has always been an out-of-the-box thinker from an early age, always looking for ways to “fix what’s not broken”. Naturally his curiosity for knowledge aligned with his passion for technology is what motivated him to pursue a bachelor’s in computer science.
            <br></br>
            With vast experience in managerial roles, he brings ideas and problem-solving skills to Gamestorme. His laser focused tenacity and his experience in fast paced environments makes him a key figure in our company’s growth.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  const Member6 = () => (
    <Box className={classes.profileContainer1}>
      <Typography variant="subtitle1" className={classes.profileTitle}>
        <span style={{ color: "#F0BC2B" }}>CLO</span>
      </Typography>
      <Box className={classes.profilePicArea}>
        <Box p="8px" className={classes.picBorder}>
          <img src="team/PhotoRoom_20220626_092829.png" className={classes.profileImg}></img>
        </Box>
      </Box>
      <Box className={classes.profileArea}>
        <Box display="flex" mb="15px">
          <Typography variant="subtitle1" className={classes.profileName}>
            Benjamin Boyd
          </Typography>
          <img className={classes.flag} src="us.png"></img>
        </Box>
        <Box className={classes.profileContent}>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Benjamin is currently pursuing a Bachelor’s in Business Information Management at the University of California, Irvine. His years of experience in the business world, specifically his time working with federal law and contracts, make him an invaluable asset to the company. Always looking to be a part of emerging technologies, he is thrilled to be a part of Gamestorme, and looks forward to contributing to its future success.
          </Typography>
        </Box>
      </Box>
    </Box>
  )

  return (
    <Box className={classes.mainContent}>
      <Container>
        <Grid container>
          <Grid item xs={12} md={12} className={classes.mainTitle}>
            <Typography variant="subtitle1" className={classes.slideCaption}>
              <span className={classes.highlight}>Our story</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={10} style={{ margin: "auto", textAlign: "left" }}>
            <Typography variant="subtitle2" className={classes.subtitle3}>
              Gamestorme was founded by Joel and Adrian in mid-2017 from their home in Aguada, Puerto Rico, with a vision to revolutionize the gaming industry. What started as an outlet for creativity and game design has evolved into a pioneering digital game store that empowers game developers through innovative solutions.<br></br><br></br>
              Today, Gamestorme stands as a unified platform for distribution and marketing, powered by cutting-edge AI and blockchain technology. Our AI-powered marketing tools help boost game sales, provide adaptive pricing strategies, enhance ASO (App Store Optimization), integrate social media campaigns, deliver content recommendations, and offer predictive analytics. As Puerto Rico's first blockchain gaming company, we're not just challenging industry standards—we're setting new ones for the future of game development and distribution.
            </Typography>
          </Grid>
        </Grid>
      </Container>
      <Box className={classes.deco}>
        <Container>
          <Grid container>
            <Grid item md={12} xs={12} style={{ display: "flex", justifyContent: "center" }}>
              <Typography variant="subtitle1" className={clsx(classes.slideCaption, classes.journey)}>
                <span className={classes.highlight}>Our Journey</span>
              </Typography>
            </Grid>
          </Grid>
          <Grid container className={classes.journeyBlock}>
            <Grid item xs={12} md={6} className={classes.storyLeft}>
              <FirstStory></FirstStory>
            </Grid>
            <Grid item xs={12} md={6} className={classes.rightFlex}>
              <img className={classes.storyImg} src="/main/rect (6).png"></img>
            </Grid>
          </Grid>
          <Grid container className={clsx(classes.journeyBlock, classes.reverseOrder)}>
            <Grid item xs={12} md={6} className={classes.leftFlex}>
              <img className={classes.storyImg} src="/main/rect (3).png"></img>
            </Grid>
            <Grid item xs={12} md={6} className={classes.storyRight}>
              <SecondStory></SecondStory>
            </Grid>
          </Grid>
          <Grid container className={classes.journeyBlock}>
            <Grid item xs={12} md={6} className={classes.storyLeft}>
              <ThirdStory></ThirdStory>
            </Grid>
            <Grid item xs={12} md={6} className={classes.rightFlex}>
              <img className={classes.storyImg} src="/main/rect (4).png"></img>
            </Grid>
          </Grid>
          <Grid container className={clsx(classes.journeyBlock, classes.reverseOrder)}>
            <Grid item xs={12} md={6} className={classes.leftFlex}>
              <img className={classes.storyImg} src="/main/rect (5).png"></img>
            </Grid>
            <Grid item xs={12} md={6} className={classes.storyRight}>
              <FourthStory></FourthStory>
            </Grid>
          </Grid>
          <Grid container className={classes.journeyBlock}>
            <Grid item xs={12} md={6} className={classes.storyLeft}>
              <FifthStory></FifthStory>
            </Grid>
            <Grid item xs={12} md={6} className={classes.rightFlex}>
              <img className={classes.storyImg} src="/main/rect (6).png"></img>
            </Grid>
          </Grid>
          <Grid container className={clsx(classes.journeyBlock, classes.reverseOrder)}>
            <Grid item xs={12} md={6} className={classes.leftFlex}>
              <img className={classes.storyImg} src="/main/rect (1).png"></img>
            </Grid>
            <Grid item xs={12} md={6} className={classes.storyRight}>
              <SixthStory></SixthStory>
            </Grid>
          </Grid>
          <Grid container>
            <img className={classes.deco1} src="line-dec1.png"></img>
          </Grid>
        </Container>
      </Box>
      <Container>
        <Grid container>
          <Grid item xs={12} md={12} className={classes.mainTitle}>
            <Typography variant="subtitle1" className={classes.slideCaption1}>
              <span className={classes.highlight}>Our team</span>
            </Typography>
          </Grid>
        </Grid>
        <Grid container>
          <Grid item xs={12} md={6}>
            {!matches ? (
              <>
                <Member1 />
                <Member3 />
                <Member5 />
                <Member6 />
                <Member8 />
              </>
            ) : (
              <>
                <Member1 />
                <Member2 />
                <Member3 />
                <Member7 />
              </>
            )}
          </Grid>
          <Grid item xs={12} md={6} className={classes.rightSide}>
            {!matches ? (
              <>
                <Member2 />
                <Member7 />
                <Member4 />
                <Member9 />
              </>
            ) : (
              <>
                <Member5 />
                <Member4 />
                <Member6 />
                <Member9 />
                <Member8 />
              </>
            )}
          </Grid>
        </Grid>
      </Container>
    </Box >
  )
}

export default Story;
