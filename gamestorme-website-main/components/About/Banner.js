import { Container, Box, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  slideCaption: {
    fontSize: "120px",
    fontFamily: "NeueMachina",
    marginBottom: "25px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "56px",
      lineHeight: "130%"
    }
  },
  slideContainer: {
    padding: "50px 80px",
    display: "inline-block",
    position: "relative",
    zIndex: 0,
    width: "900px",
    background: "rgba(13, 14, 16, 0.7)",
    backdropFilter: "blur(20px)",
    borderRadius: "0px 100px",
    height: "377px",
    "&:before": {
      content: "''",
      position: "absolute",
      zIndex: -1,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      padding: "5px",
      borderRadius: "0px 100px",
      background: "var(--c, linear-gradient(to right, #745CE7, #F0BC2B))",
      WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
      WebkitMaskComposite: "destination-out",
      MaskComposite: "exclude",
    },
    [theme.breakpoints.down("xs")]: {
      width: "327px",
      height: "324px",
      padding: "50px 25px",
      borderRadius: "0px 50px",
      "&:before": {
        padding: "3px",
        borderRadius: "0px 50px",
      }
    },
  },
  subtitle2: {
    fontSize: "22px",
    lineHeight: "130%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
      lineHeight: "130%"
    }
  },
  mainContainer: {
    backgroundImage: "url('main/2.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover, auto",
    backgroundPosition: "center",
    paddingTop: "100px",
    paddingBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      paddingTop: "65px",
      paddingBottom: "65px",
    }
  }
}));

const Banner = () => {
  const classes = useStyles();

  return (
    <Box className={classes.mainContainer}>
      <Container style={{ textAlign: "center" }}>
        <Box className={classes.slideContainer}>
          <Typography variant="subtitle1" className={classes.slideCaption}>
            About
          </Typography>
          <Typography variant="subtitle2" className={classes.subtitle2}>
            Empowering Game Developers Through Innovative Solutions. Discover how Gamestorme became a pioneering digital game store, leveraging AI and blockchain technology to revolutionize game development and distribution.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
}

export default Banner;
