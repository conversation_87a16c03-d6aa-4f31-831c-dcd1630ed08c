import { Con<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import ImgMediaCard from "../Home/ImgMediaCard";

const useStyles = makeStyles((theme) => ({
  container: {
    paddingTop: "100px",
    paddingBottom: "200px",
    [theme.breakpoints.down("xs")]: {
      paddingTop: "50px",
      paddingBottom: "100px",
    }
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      marginBottom: "25px",
    }
  },
  subtitle2: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    marginBottom: "50px",
    lineHeight: "27px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "25px",
    }
  },
  highlight: {
    paddingRight: "30px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  points: {
    fontSize: "32px",
    marginBottom: "16px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "24px"
    }
  },
  pointsContent: {
    marginBottom: "0",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px"
    }
  },
  ctaBtn: {
    [theme.breakpoints.down("xs")]: {
      "& button": {
        marginTop: "25px",
        marginBottom: "25px",
        width: "100%",
      }
    },
    "& button": {
      padding: "13px 54px"
    }
  },
  decorationArea: {
    backgroundImage: "url('decoration.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "100% 100%",
    backgroundPosition: "center",
    padding: "100px",
    paddingTop: "10px",
    paddingBottom: "80px",
    [theme.breakpoints.down("xs")]: {
      backgroundImage: "url('decoration-m.png')",
      padding: "24px",
      paddingTop: "30px",
      paddingBottom: "20px",
    }
  },
  decoText: {
    fontSize: "24px",
    fontWeight: "600",
    lineHeight: "180%",
    marginBottom: "0",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
    }
  },
  decoTitle: {
    textAlign: "center",
    marginBottom: "0",
    top: "-40px",
    position: "relative",
    [theme.breakpoints.down("xs")]: {
      fontSize: "26px",
      padding: "0 50px",
    }
  },
  reverseOrder: {
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
  pointsMargin: {
    marginRight: "50px",
    [theme.breakpoints.down("xs")]: {
      marginRight: "30px",
    }
  }
}));

const Ecosystem = () => {
  const classes = useStyles();

  return (
    <Box className={classes.container}>
      <Container>
        <Grid container spacing={5} style={{ marginBottom: "100px" }}>
          <Grid item xl={12} md={6} style={{ display: "flex", flexDirection: "column", justifyContent: "center" }}>
            <Box>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                Lorem ipsum dolor <br></br><span className={classes.highlight}>Sit amet </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam leo ligula, tincidunt quis neque in, pharetra congue turpis. Fusce porttitor facilisis tempus. Aenean sagittis, orci et scelerisque tincidunt, nisi eros condimentum arcu, ut convallis nisl lectus sed nulla. Cras tempus eget neque eget dignissim.
              </Typography>
              <Box display="flex">
                <Box className={classes.pointsMargin}>
                  <Typography variant="subtitle2" className={clsx(classes.slideCaption, classes.points)}>
                    50M+
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.pointsContent)}>
                    Lorem ipsum
                  </Typography>
                </Box>
                <Box className={classes.pointsMargin}>
                  <Typography variant="subtitle2" className={clsx(classes.slideCaption, classes.points)}>
                    350K+
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.pointsContent)}>
                    Lorem ipsum
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="subtitle2" className={clsx(classes.slideCaption, classes.points)}>
                    100+
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.pointsContent)}>
                    Lorem ipsum
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Grid>
          <Grid item xl={12} md={6} data-aos="fade-left">
            <ImgMediaCard img="main/game1.jpg" ></ImgMediaCard>
          </Grid>
        </Grid>
        <Grid container spacing={5} className={classes.reverseOrder} >
          <Grid item xl={12} md={6} data-aos="fade-right">
            <ImgMediaCard img="main/game1.jpg" ></ImgMediaCard>
          </Grid>
          <Grid item xl={12} md={6} style={{ display: "flex", flexDirection: "column", justifyContent: "center" }}>
            <Box>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                Lorem ipsum dolor <br></br><span className={classes.highlight}>Sit amet </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam leo ligula, tincidunt quis neque in, pharetra congue turpis. Fusce porttitor facilisis tempus. Aenean sagittis, orci et scelerisque tincidunt, nisi eros condimentum arcu, ut convallis nisl lectus sed nulla. Cras tempus eget neque eget dignissim.
              </Typography>
              <Link href="#" className={classes.ctaBtn}>
                <Button variant="outlined">Read more</Button>
              </Link>
            </Box>
          </Grid>
        </Grid>
        <Box className={classes.decorationArea}
          data-aos="fade-up"
          data-aos-anchor-placement="top-center"
        >
          <Typography variant="subtitle1" className={clsx(classes.slideCaption, classes.decoTitle)}>
            Lorem ipsum dolor
          </Typography>
          <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.decoText)}>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam leo ligula, tincidunt quis neque in, pharetra congue turpis. Fusce porttitor facilisis tempus. Aenean sagittis, orci et scelerisque tincidunt, nisi eros condimentum arcu, ut convallis nisl lectus sed nulla. Cras tempus eget neque eget dignissim. Morbi ornare leo at augue porttitor imperdiet. In non neque mi. Cras sed dolor convallis, euismod orci maximus, pharetra nibh. Nunc cursus nisi sapien, et scelerisque eros finibus at. Vestibulum tincidunt ante nec orci tristique, non rutrum ante rhoncus. Aliquam lacus lacus, pharetra sit amet ornare vel, maximus efficitur ipsum.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
}

export default Ecosystem;
