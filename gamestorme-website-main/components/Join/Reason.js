import { Con<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import ReactPlayer from 'react-player/lazy'
import clsx from "clsx";

const useStyles = makeStyles((theme) => ({
  video: {
    marginBottom: "220px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "100px",
    }
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "35px",
    lineHeight: "130%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      marginBottom: "25px",
    }
  },
  subtitle2: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    marginBottom: "50px",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "25px",
    }
  },
  highlight: {
    paddingRight: "30px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  title: {
    color: "#F0BC2B",
    marginBottom: "25px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "207px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "135px",
      marginBottom: "15px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  title2: {
    color: "#F0BC2B",
    marginBottom: "25px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "175px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "112px",
      marginBottom: "15px",
      marginTop: "0px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  icon: {
    background: "#1D1429",
    width: "48px",
    height: "48px",
    background: "linear-gradient(#0B0F1E,#0B0F1E) padding-box, linear-gradient(to top, #F0BC2B, #F0BC2B3D) border-box",
    border: "2px solid transparent",
    borderRadius: "50%",
    boxShadow: "0px 16px 16px rgba(0, 0, 0, 0.26)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginRight: "20px",
    flexShrink: "0",
    [theme.breakpoints.down("xs")]: {
      width: "32px",
      height: "32px",
    }
  },
  icon1: {
    marginRight: "20px",
  },
  iconPic: {
    width: "22px",
    height: "22px",
    [theme.breakpoints.down("xs")]: {
      width: "16px",
      height: "16px",
    }
  },
  iconPic1: {
    width: "24px",
    height: "24px"
  },
  title1: {
    fontFamily: "NeueMachina",
    fontSize: "32px",
    lineHeight: "31px",
    marginBottom: "15px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "20px",
      lineHeight: "19px",
    }
  },
  map: {
    width: "570px",
    height: "570px",
    [theme.breakpoints.down("xs")]: {
      width: "327px",
      height: "327px",
    }
  },
  content1: {
    marginBottom: "35px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "25px",
    }
  },
  mapBlock: {
    marginBottom: "150px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "100px",
    }
  },
  pointBlock: {
    background: "#1D1429",
    border: "1px solid #F0BC2B",
    boxSizing: "border-box",
    borderRadius: "16px",
    padding: "78px 52px",
    textAlign: "center",
    marginBottom: "40px",
    "&:last-child": {
      marginBottom: "0"
    },
    [theme.breakpoints.down("xs")]: {
      padding: "38px 13px",
    }
  },
  pointPlus: {
    display: "flex",
    justifyContent: "center",
    "& span": {
      color: "#F0BC2B",
      fontSize: "34px",
      fontFamily: "NeueMachina",
      lineHeight: "57px",
      fontWeight: "800",
      position: "relative",
      top: "-9px",
      left: "0px"
    },
    [theme.breakpoints.down("xs")]: {
      "& span": {
        fontSize: "24px",
        lineHeight: "40px",
        top: "-18px",
        left: "0px"
      }
    }
  },
  pointTitle: {
    fontSize: "52px",
    fontFamily: "NeueMachina",
    lineHeight: "130%",
    marginBottom: "15px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "32px",
      lineHeight: "30px",
      marginBottom: "10px"
    }
  },
  rightSidePoint: {
    marginTop: "70px",
    [theme.breakpoints.down("xs")]: {
      marginTop: "30px",
    }
  },
  ctaTitle: {
    fontFamily: "NeueMachina",
    fontSize: "32px",
    fontWeight: "800",
    marginRight: "40px",
    paddingBottom: "10px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "20px",
    }
  },
  ctaBlock: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    margin: "200px 0",
    [theme.breakpoints.down("xs")]: {
      margin: "100px 0",
      "& img": {
        width: "100px",
        height: "40px"
      }
    },
  },
  reverseOrder: {
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
  subtitleBottom: {
    marginBottom: "0px"
  },
  zoom: {
    cursor: "pointer",
    transition: "transform .6s",
    "&:hover": {
      transform: "scale(1.05)"
    }
  },
  mapContainer: {
    display: "flex",
    justifyContent: "start"
  }
}));

const Reason = () => {
  const classes = useStyles();

  return (
    <Box style={{ borderBottom: "1px solid #F0BC2B" }}>
      <Box className={classes.video}>
        <ReactPlayer
          url='sample.mp4'
          loop={true}
          playing={true}
          controls={false}
          muted={true}
          width="100%"
          height="100%"
        />
      </Box>
      <Container>
        <Grid container spacing={7} className={classes.mapBlock}>
          <Grid item xs={12} md={6} data-aos="fade-right" data-aos-delay="300">
            <Box>
              <Box className={classes.title}>WHY CHOOSE US</Box>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                Lorem ipsum dolor <br></br><span className={classes.highlight}>Sit amet </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam leo ligula, tincidunt quis neque in, pharetra congue turpis. Fusce
              </Typography>
              <Box display="flex">
                <Box className={classes.icon}>
                  <img className={classes.iconPic} src="icon.png"></img>
                </Box>
                <Box>
                  <Typography variant="subtitle1" className={classes.title1}>
                    Lorem ipsum
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.content1)}>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam quis gravida leo. Curabitur ut ultricies lorem.
                  </Typography>
                </Box>
              </Box>
              <Box display="flex">
                <Box className={classes.icon}>
                  <img className={classes.iconPic} src="icon.png"></img>
                </Box>
                <Box>
                  <Typography variant="subtitle1" className={classes.title1}>
                    Lorem ipsum
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.content1)}>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam quis gravida leo. Curabitur ut ultricies lorem.
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6} className={classes.mapContainer} >
            <img className={classes.map} src="map.png"></img>
          </Grid>
        </Grid>
        <Grid container spacing={7} className={classes.reverseOrder}>
          <Grid item xs={12} md={6} >
            <Grid container spacing={4}>
              <Grid item xs={6}>
                <Box className={classes.pointBlock}>
                  <Box className={classes.pointPlus}>
                    <Typography variant="subtitle1" className={classes.pointTitle}>
                      235
                    </Typography>
                    <span>+</span>
                  </Box>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.subtitleBottom)}>
                    Lorem ipsum dolor
                  </Typography>
                </Box>
                <Box className={classes.pointBlock}>
                  <Box className={classes.pointPlus}>
                    <Typography variant="subtitle1" className={classes.pointTitle}>
                      235
                    </Typography>
                    <span>+</span>
                  </Box>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.subtitleBottom)}>
                    Lorem ipsum dolor
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box className={clsx(classes.pointBlock, classes.rightSidePoint)}>
                  <Box className={classes.pointPlus}>
                    <Typography variant="subtitle1" className={classes.pointTitle}>
                      235
                    </Typography>
                    <span>+</span>
                  </Box>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.subtitleBottom)}>
                    Lorem ipsum dolor
                  </Typography>
                </Box>
                <Box className={classes.pointBlock}>
                  <Box className={classes.pointPlus}>
                    <Typography variant="subtitle1" className={classes.pointTitle}>
                      235
                    </Typography>
                    <span>+</span>
                  </Box>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.subtitleBottom)}>
                    Lorem ipsum dolor
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} md={6} data-aos="fade-left" data-aos-delay="300">
            <Box>
              <Box className={classes.title2}>FEW WORDS</Box>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                Lorem ipsum dolor <br></br><span className={classes.highlight}>Sit amet </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam leo ligula, tincidunt quis neque in, pharetra congue turpis. Fusce
              </Typography>
              <Box display="flex">
                <Box className={classes.icon1}>
                  <img src="icon1.png"></img>
                </Box>
                <Box>
                  <Typography variant="subtitle1" className={classes.title1}>
                    Lorem ipsum
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.content1)}>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam quis gravida leo. Curabitur ut ultricies lorem.
                  </Typography>
                </Box>
              </Box>
              <Box display="flex">
                <Box className={classes.icon1}>
                  <img src="icon1.png"></img>
                </Box>
                <Box>
                  <Typography variant="subtitle1" className={classes.title1}>
                    Lorem ipsum
                  </Typography>
                  <Typography variant="subtitle2" className={clsx(classes.subtitle2, classes.content1)}>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam quis gravida leo. Curabitur ut ultricies lorem.
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
        <Box className={classes.ctaBlock} data-aos="fade-left">
          <Typography variant="subtitle2" className={classes.ctaTitle}>
            Let's work together
          </Typography>
          <img className={classes.zoom} src="icon2.png"></img>
        </Box>
      </Container>
    </Box >
  );
}

export default Reason;
