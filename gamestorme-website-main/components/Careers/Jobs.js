import { Con<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";

const useStyles = makeStyles((theme) => ({
  mainContent: {
    marginTop: "100px",
    paddingBottom: "100px"
  },
  mainTitle: {
    display: "flex",
    justifyContent: "center"
  },
  highlight: {
    paddingLeft: "30px",
    paddingRight: "30px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
    }
  },
  categoryList: {
    background: "#1D1429",
    border: "1px solid #F0BC2B",
    borderRadius: "16px",
    padding: "45px 40px"
  },
  categoryImg: {
    width: "785px",
    height: "390px"
  },
  categoryLabel: {
    color: "rgba(255, 253, 250, 0.5)",
    fontWeight: "normal",
    marginBottom: "35px",
    cursor: "pointer",
    "& span": {
      color: "rgba(240, 188, 43, 0.5)",
    },
    "&:last-child": {
      marginBottom: 0
    }
  },
  active: {
    color: "rgba(255, 253, 250, 1)",
    "& span": {
      color: "rgba(240, 188, 43, 1)",
    }
  }
}));

const Jobs = () => {
  const classes = useStyles();

  return (
    <Box className={classes.mainContent}>
      <Container>
        <Grid container>
          <Grid container item xs={12} md={12} className={classes.mainTitle}>
            <Typography variant="subtitle1" className={classes.slideCaption}>
              <span className={classes.highlight}>Our jobs</span>
            </Typography>
          </Grid>
        </Grid>
        <Grid container>
          <Grid container item xs={12} md={8}>
            <img src="/main/2.png" className={classes.categoryImg}></img>
          </Grid>
          <Grid container item xs={12} md={4}>
            <Box className={classes.categoryList}>
              <Typography variant="subtitle2" className={classes.categoryLabel}>
                Engineering & Science <span>(1)</span>
              </Typography>
              <Typography variant="subtitle2" className={classes.categoryLabel}>
                Marketing <span>(1)</span>
              </Typography>
              <Typography variant="subtitle2" className={clsx(classes.categoryLabel, classes.active)}>
                Design <span>(2)</span>
              </Typography>
              <Typography variant="subtitle2" className={classes.categoryLabel}>
                Human Resources <span>(1)</span>
              </Typography>
              <Typography variant="subtitle2" className={classes.categoryLabel}>
                Early Talent Program <span>(1)</span>
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default Jobs;
