import Header from "./Header";
import Footer from "./Footer";
import Copyright from "./Copyright";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Head from "next/head";
import { Box } from '@mui/material';

const Layout = ({ children, title, description, ogImage, url }) => {
  // website Url
  const pageUrl = "https://gamestorme.com/";
  // when you share this page on facebook you'll see this image
  const ogImg = "logo.png";

  return (
    <>
      <Head>
        <title>{title || "Gamestorme"}</title>
        <meta
          name="description"
          key="description"
          content={description || "Gamestorme - Gaming and blockchain ecosystem"}
        />
        <meta
          property="og:title"
          content={title || "Gamestorme"}
          key="og:title"
        />
        <meta
          property="og:url"
          content={url || pageUrl}
          key="og:url"
        />
        <meta
          property="og:image"
          content={ogImage || ogImg}
          key="og:image"
        />
        <meta
          property="og:description"
          content={description || "Gamestorme - Gaming and blockchain ecosystem"}
          key="og:description"
        />
        <meta name="twitter:card" content="summary_large_image" />
      </Head>

      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          backgroundImage: 'url("main-bg1.png")',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          backgroundPosition: 'top',
          overflow: 'hidden',
        }}
      >
        <Header />
        <Box
          component="main"
          sx={{
            flex: 1,
            width: '100%',
            overflowX: 'hidden',
          }}
        >
          {children}
        </Box>
        <Footer />
        <Copyright />
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="dark"
        />
      </Box>
    </>
  );
};

export default Layout;
