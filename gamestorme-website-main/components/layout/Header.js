import Link from "components/Link";
import { useRouter } from "next/router";
import React, { useState } from "react";

import { makeStyles, useTheme } from "@material-ui/core/styles";
import useMediaQuery from "@material-ui/core/useMediaQuery";
import {
  Grid,
  AppBar,
  Toolbar,
  Typography,
  List,
  ListItem,
  ListItemText,
  SwipeableDrawer,
  IconButton,
  Button,
  Box
} from "@material-ui/core";
import useScrollTrigger from "@material-ui/core/useScrollTrigger";

import MenuIcon from "@material-ui/icons/Menu";
import CloseIcon from "@material-ui/icons/Close";

import { routes } from "data/routes";
import clsx from "clsx";

function ElevationScroll(props) {
  const { children } = props;

  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 0,
  });

  return React.cloneElement(children, {
    elevation: trigger ? 4 : 0,
  });
}

const useStyles = makeStyles((theme) => ({
  appBar: {
    background: "transparent",
    [theme.breakpoints.down("xs")]: {
      paddingTop: "15px",
      paddingBottom: "15px"
    }
  },
  toolbarMargin: {
    ...theme.mixins.toolbar,
    marginBottom: `43px`,
    [theme.breakpoints.up("xs")]: {
      minHeight: "50px",
    },
    [theme.breakpoints.down("xs")]: {
      marginBottom: "30px",
    },
  },
  logo: {
    width: "max-content",
    [theme.breakpoints.down("xs")]: {
      width: "170px",
      height: "30px"
    }
  },
  drawerIconContainer: {
    marginLeft: "auto",
    padding: 0,
    "&:hover": {
      backgroundColor: "transparent",
    },
  },
  drawerIcon: {
    height: `50px`,
    width: `50px`,
    color: `#F0BC2B`,
    opacity: "0.8",
    [theme.breakpoints.down("xs")]: {
      height: `35px`,
      width: `35px`,
    },
  },
  drawer: {
    padding: "0 24px",
    background: "#1A0428",
    borderRadius: "40px 0px 0px 40px"
  },
  link: {
    fontSize: "1.25em",
    color: theme.palette.secondary.main,
    "&:hover": {
      color: theme.palette.info.main,
    },
  },
  bgBlur: {
    background: "rgba(11, 15, 30, 0.25)",
    backdropFilter: "blur(30px)",
    boxShadow: "none",
    borderBottom: "1px solid rgba(240, 188, 43, 0.3)"
  }
}));

const Header = () => {
  const classes = useStyles();
  const theme = useTheme();
  const iOS = process.browser && /iPad|iPhone|iPod/.test(navigator.userAgent);
  const matches = useMediaQuery(theme.breakpoints.down("sm"));

  const [openDrawer, setOpenDrawer] = useState(false);

  const router = useRouter();

  const path = routes;

  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 0,
  });

  const tabs = (
    <>
      <Grid container justifyContent="flex-end" spacing={4}>
        {path.map(({ name, link }) => (
          <Grid item key={link}>
            <Link href={link}>
              <Typography
                variant="h1"
                className={classes.link}
                style={{
                  fontWeight: router.pathname === link && "bold",
                  borderBottom: router.pathname === link && "1px solid #757ce8",
                }}
              >
                {name}
              </Typography>
            </Link>
          </Grid>
        ))}
        <Link href="#">
          <Button style={{ padding: "5px 49px", fontSize: "15px", lineHeight: "20px", marginRight: "16px" }} variant="outlined">
            <Box diplay="block">
              <span>Marketplace</span>
              <br></br>
              <span style={{ fontSize: "12px", lineHeight: "13px" }}>(Coming soon)</span>
            </Box>
          </Button>
        </Link>
      </Grid>
    </>
  );
  const drawer = (
    <>
      <SwipeableDrawer
        disableBackdropTransition={!iOS}
        disableDiscovery={iOS}
        open={openDrawer}
        onClose={() => setOpenDrawer(false)}
        onOpen={() => setOpenDrawer(true)}
        classes={{ paper: classes.drawer }}
        anchor="right"
      >
        <Box textAlign="right">
          <CloseIcon onClick={() => setOpenDrawer(!openDrawer)} style={{ color: "#F0BC2B", width: "24px", height: "24px", marginTop: "18px" }} />
        </Box>
        <div className={classes.toolbarMargin} />
        <List disablePadding>
          {path.map(({ name, link }) => (
            <ListItem
              key={link}
              divider
              button
              onClick={() => {
                setOpenDrawer(false);
              }}
            >
              <ListItemText disableTypography>
                <Link href={link}>
                  <Typography
                    variant="h1"
                    style={{
                      color:
                        router.pathname === link
                          ? "#F0BC2B"
                          : "#FDF7EA",
                      fontSize: "26px",
                      fontWeight: "600",
                      lineHeight: "35px",
                      paddingBottom: "15px",
                      minWidth: "200px",
                      textAlign: "center",
                      borderBottom: "1px solid rgba(240, 188, 43, 0.3)",
                      marginBottom: "25px"
                    }}
                  >
                    {name}
                  </Typography>
                </Link>
              </ListItemText>
            </ListItem>
          ))}
          <ListItem
            divider
            button
            onClick={() => {
              setOpenDrawer(false);
            }}
          >
            <Link href="#">
              <Button style={{ fontSize: "14px", padding: "7px 35px", marginTop: "25px", minWidth: "200px" }} variant="outlined">
                <Box diplay="block">
                  <span>Marketplace</span>
                  <br></br>
                  <span style={{ fontSize: "12px", lineHeight: "13px" }}>(Coming soon)</span>
                </Box>
              </Button>
            </Link>
          </ListItem>
        </List>
      </SwipeableDrawer>
      <IconButton
        onClick={() => setOpenDrawer(!openDrawer)}
        disableRipple
        className={classes.drawerIconContainer}
      >
        <MenuIcon className={classes.drawerIcon} />
      </IconButton>
    </>
  );
  return (
    <>
      <ElevationScroll>
        <AppBar className={!trigger ? classes.appBar : clsx(classes.appBar, classes.bgBlur)}>
          <Toolbar
            disableGutters
            style={{
              maxWidth: "1280px",
              margin: "0 auto",
              width: "100%",
              padding: matches ? "0 16px" : "25px",
            }}
          >
            <Link href="/" style={{ display: "flex" }}>
              <img className={classes.logo} src="logo.png"></img>
            </Link>
            {matches ? drawer : tabs}
          </Toolbar>
        </AppBar>
      </ElevationScroll>

      <div className={classes.toolbarMargin} />
    </>
  );
};
export default Header;
