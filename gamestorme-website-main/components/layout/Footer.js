import Link from "components/Link";
import { useRouter } from "next/router";
import { makeStyles } from "@material-ui/core/styles";
import { Container, Grid, Typography } from "@material-ui/core";

import { routes } from "data/routes";
import Social from "components/Social";

import FormControl from '@material-ui/core/FormControl';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputLabel from '@material-ui/core/InputLabel';
import InputAdornment from '@material-ui/core/InputAdornment';
import MailOutline from '@material-ui/icons/MailOutline';
import IconButton from '@material-ui/core/IconButton';


const useStyles = makeStyles((theme) => ({
  footer: {
    background: "transparent",
    width: `100%`,
    position: "relative",
    overflow: "hidden",
    paddingTop: "100px",
    paddingBottom: "100px",
    borderBottom: "1px solid #F0BC2B",
    [theme.breakpoints.down("xs")]: {
      paddingTop: "50px",
      paddingBottom: "50px",
    }
  },
  link: {
    fontSize: "18px",
    color: "#FDF7EA",
    marginBottom: "25px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
    }
  },
  copylight: {
    color: "#fff",
    fontSize: "1em",
    "&:hover": {
      color: theme.palette.info.main,
    },
  },
  companyIntro: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    paddingRight: "100px",
    marginTop: "25px",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      paddingRight: "0px",
      marginBottom: "50px"
    }
  },
  subscribe: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    marginBottom: "50px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "35px",
    }
  },
  subTitle: {
    fontFamily: "NeueMachina",
    marginBottom: "25px",
    fontSize: "18px",
    color: "#FFFDFA",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
    }
  },
  logo: {
    width: "215px",
    height: "39px",
    [theme.breakpoints.down("xs")]: {
      width: "170px",
      height: "30px",
      marginBottom: "30px"
    }
  }
}));

const Footer = () => {
  const classes = useStyles();
  const path = routes;
  const router = useRouter();
  return (
    <footer className={classes.footer}>
      <Container maxWidth="lg">
        <Grid container>
          <Grid item md={4} xs={12}>
            <Link href="/">
              <img className={classes.logo} src="/logo.png"></img>
            </Link>
            {/* <Typography variant="subtitle1" className={classes.companyIntro}>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam leo ligula, tincidunt quis neque in, pharetra congue turpis. Fusce porttitor facilisis tempus.
            </Typography> */}
          </Grid>
          <Grid item md={2} xs={6}>
            <Typography variant="subtitle1" className={classes.subTitle}>
              LINKS
            </Typography>
            {path.map(({ name, link }) => (
              <Link key={link} href={link}>
                <Typography
                  variant="h3"
                  className={classes.link}
                >
                  {name}
                </Typography>
              </Link>
            ))}
            <Link href="https://gamestorme.creator-spring.com/">
              <Typography
                variant="h3"
                className={classes.link}
              >
                Shop
              </Typography>
            </Link>
          </Grid>
          <Grid item md={2} xs={6}>
            <Typography variant="subtitle1" className={classes.subTitle}>
              SOCIALS
            </Typography>
            <Social />
          </Grid>
          <Grid item md={4} xs={12}>
            <Typography variant="subtitle1" className={classes.subTitle}>
              DON'T MISS A THING!
            </Typography>
            <Typography variant="subtitle1" className={classes.subscribe}>
              Subscribe us now to get the latest news and update
            </Typography>
            <FormControl style={{ width: "100%" }} variant="outlined">
              <InputLabel htmlFor="outlined-adornment-email">Email Address</InputLabel>
              <OutlinedInput
                id="outlined-adornment-email"
                type='text'
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle email visibility"
                      edge="end"
                    >
                      <MailOutline />
                    </IconButton>
                  </InputAdornment>
                }
                color="secondary"
                labelWidth={110}
              />
            </FormControl>
          </Grid>
        </Grid>
      </Container>
    </footer>
  );
};

export default Footer;
