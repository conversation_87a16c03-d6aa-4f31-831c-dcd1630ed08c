import Link from "components/Link";
import { Container, Grid, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  copyRight: {
    fontSize: "16px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      textAlign: "center"
    }
  },
  terms: {
    fontSize: "16px",
    color: "#FDF7EA",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
    }
  },
  rightReserve: {
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
  termsItem: {
    [theme.breakpoints.down("xs")]: {
      flexGrow: "1",
      width: "50%",
      "&:last-child": {
        marginTop: "25px",
        marginBottom: "25px"
      }
    }
  },
  termContainer: {
    display: "flex",
    justifyContent: "space-between",
    [theme.breakpoints.down("xs")]: {
      textAlign: "center"
    }
  }
}));

const Copyright = () => {
  const classes = useStyles();

  return (
    <Container maxWidth="lg" style={{ paddingTop: "25px", paddingBottom: "25px" }}>
      <Grid container className={classes.rightReserve}>
        <Grid item md={6}>
          <Typography variant="subtitle1" className={classes.copyRight}>
            © 2022 Gamestorme. All rights reserved.
          </Typography>
        </Grid>
        <Grid container item md={6} className={classes.termContainer}>
          <Link href="/privacy" className={classes.termsItem}>
            <Typography variant="subtitle2" className={classes.terms}>
              Privacy Policy
            </Typography>
          </Link>
          <Link href="/cookie" className={classes.termsItem}>
            <Typography variant="subtitle2" className={classes.terms}>
              Cookie Policy
            </Typography>
          </Link>
          <Link href="/terms" className={classes.termsItem}>
            <Typography variant="subtitle2" className={classes.terms}>
              Terms & Conditions
            </Typography>
          </Link>
        </Grid>
      </Grid>
    </Container>
  );
}

export default Copyright;
