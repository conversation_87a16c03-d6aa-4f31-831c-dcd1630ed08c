import { makeStyles } from "@material-ui/core/styles";
import { Grid } from "@material-ui/core";

import { socialMedia } from "data/socialMedia";

import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faDiscord, faTwitter, faFacebook, faInstagram, faLinkedin, faYoutube } from '@fortawesome/free-brands-svg-icons'

library.add(faDiscord, faTwitter, faFacebook, faInstagram, faLinkedin, faYoutube);

const useStyles = makeStyles((theme) => ({
  snsIcon: {
    color: "#FFFDFA",
    width: "30px",
    height: "30px",
    "&:hover": {
      color: "#F0BC2B"
    },
  },
  socialContainer: {
    display: "flex",
    flexDirection: "column",
    width: "25%",
    [theme.breakpoints.down("xs")]: {
      flexGrow: 1,
      width: "100%",
      height: "150px",
      justifyContent: "flex-start",
      marginBottom: "50px"
    }
  }
}));

const Social = ({ color }) => {
  const classes = useStyles();
  // if you want to add more social medias, add it to here and /data/socialMedia.js.
  // and import the Material Icon, then add the code.
  // const { instagram, facebook, github, homepage } = socialMedia;

  // if you add twitter , it will be
  const { instagram, facebook, discord, linkedin, twitter, youtube } = socialMedia;

  return (
    <Grid item container spacing={2} className={classes.socialContainer}>
      <Grid
        item
        component={"a"}
        target="_blank"
        rel="noreferrer noopener"
        href={youtube}
      >
        <FontAwesomeIcon className={classes.snsIcon} icon="fa-brands fa-youtube" />
      </Grid>
      <Grid
        item
        component={"a"}
        target="_blank"
        rel="noreferrer noopener"
        href={twitter}
      >
        <FontAwesomeIcon className={classes.snsIcon} icon="fa-brands fa-twitter" />
      </Grid>
      <Grid
        item
        component={"a"}
        target="_blank"
        rel="noreferrer noopener"
        href={linkedin}
      >
        <FontAwesomeIcon className={classes.snsIcon} icon="fa-brands fa-linkedin" />
      </Grid>
      <Grid
        item
        component={"a"}
        target="_blank"
        rel="noreferrer noopener"
        href={facebook}
      >
        <FontAwesomeIcon className={classes.snsIcon} icon="fa-brands fa-facebook" />
      </Grid>
      <Grid
        item
        component={"a"}
        target="_blank"
        rel="noreferrer noopener"
        href={instagram}
      >
        <FontAwesomeIcon className={classes.snsIcon} icon="fa-brands fa-instagram" />
      </Grid>
      <Grid
        item
        component={"a"}
        target="_blank"
        rel="noreferrer noopener"
        href={discord}
      >
        <FontAwesomeIcon className={classes.snsIcon} icon="fa-brands fa-discord" />
      </Grid>
    </Grid>
  );
};

export default Social;
