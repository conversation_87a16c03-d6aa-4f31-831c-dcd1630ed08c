import { Container, Grid, Box, Typography } from "@material-ui/core";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import { useMediaQuery } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "20px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      marginBottom: "25px"
    }
  },
  subtitle2: {
    marginBottom: "15px",
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "25px"
    }
  },
  highlight: {
    paddingRight: "15px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  title: {
    color: "#F0BC2B",
    marginBottom: "25px",
    fontSize: "16px",
    fontWeight: "normal",
    fontFamily: "Exo",

    width: "156px",
    display: "flex",
    alignItems: "center",
    "&:before": {
      content: "''",
      flexGrow: 1,
      background: "#F0BC2B",
      height: "1px",
      fontSize: "0px",
      lineHeight: "0px",
      marginRight: "20px",
      marginBottom: "3px",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "12px",
      width: "97px",
      marginBottom: "15px",
      "&:before": {
        marginRight: "10px",
      }
    }
  },
  ctaBtn: {
    [theme.breakpoints.down("xs")]: {
      width: "100%",
    },
    "& button": {
      padding: "13px 54px",
      width: "100%",
    }
  },
  bgContainer: {
    marginTop: "100px",
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      marginTop: "50px",
      marginBottom: "50px",
    }
  },
  nftImg: {
    width: "100%",
    height: "100%"
  },
  reverseOrder: {
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse"
    }
  },
}));

const Why = () => {
  const classes = useStyles();
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Box className={classes.bgContainer}>
      <Container>
        <Grid container spacing={matches ? 0 : 8} className={classes.reverseOrder}>
          <Grid item xs={12} md={6}>
            <Grid container spacing={matches ? 2 : 3}>
              <Grid item md={6} xs={6}>
                <img className={classes.nftImg} src="nfts/nft (1).png"></img>
              </Grid>
              <Grid item md={6} xs={6}>
                <img className={classes.nftImg} src="nfts/nft (2).png"></img>
              </Grid>
              <Grid item md={6} xs={6}>
                <img className={classes.nftImg} src="nfts/nft (3).png"></img>
              </Grid>
              <Grid item md={6} xs={6}>
                <img className={classes.nftImg} src="nfts/nft (4).png"></img>
              </Grid>
            </Grid>
          </Grid>
          <Grid container item xs={12} md={6}>
            <Box alignItems="baseline" justifyContent="center" display="flex" flexDirection="column" textAlign="left">
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>WHY NFT’s?
                </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Just as DeFi is changing the game for borrowing and lending markets, allowing protocols to provide stablecoin yields higher than any bank can compete, NFT’s will allow game developers to distribute their games and in-game items without Steam’s heavy 30% revenue share. NFT’s will give gamers unique opportunities to earn yield on or monetize their virtual assets with efficiencies traditional game publishers can’t compete with.
              </Typography>
              <Typography variant="subtitle1" className={classes.slideCaption}>
                <span className={classes.highlight}>Introducing pNFT’s
                </span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Our NFT’s are carbon neutral and live on Solana’s lightning fast network. We utilize hash power to store even the largest, most complex data files on the blockchain without having to compress the data or sacrifice elements of that data. With this, we can store intricate player data such as in-game account, games played, achievements, rank, among other key pieces of data to allow for renting and selling of player accounts. The idea of player accounts as NFT’s we coin pNFT’s (player NFT’s).
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default Why;
