import { Container, Grid, Box, Typography, Card, CardMedia, Link } from "@material-ui/core";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useMediaQuery } from "@material-ui/core";
import { useState } from "react";

const useStyles = makeStyles((theme) => ({
  media: {
    transition: "transform .6s",
  },
  cardTitle: {
    fontSize: "32px",
    fontWeight: "600",
    lineHeight: "48px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "18px",
      lineHeight: "150%",
    }
  },
  cardContent: {
    position: "absolute",
    bottom: "35px",
    width: "100%",
    textAlign: "center",
    color: "#fff",
    background: "none",
    paddingLeft: "40px",
    paddingRight: "40px",
    [theme.breakpoints.down("xs")]: {
      paddingLeft: "20px",
      bottom: "20px",
      paddingRight: "20px",
    }
  },
  cardBody: {
    border: "1px solid #F0BC2B",
    borderRadius: "16px",
    maxWidth: "100%",
    position: "relative",
    background: "none",
    boxShadow: "0px 16px 16px rgba(0, 0, 0, 0.26)",
    "&:hover > img": {
      transform: "scale(1.05)",
      cursor: "pointer"
    }
  },
  wrapper: {
    marginTop: "200px",
    paddingBottom: "200px",
    borderBottom: "1px solid #F0BC2B",
    [theme.breakpoints.down("xs")]: {
      marginTop: "100px",
      paddingBottom: "100px",
    }
  },
  slideCaption: {
    fontFamily: "NeueMachina",
    marginBottom: "30px",
    lineHeight: "60px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "33px",
      marginBottom: "25px",
      lineHeight: "35px",
    }
  },
  slideCaption1: {
    fontFamily: "NeueMachina",
    marginBottom: "50px",
    lineHeight: "130%",
    fontSize: "36px",
    fontWeight: "800",
    [theme.breakpoints.down("xs")]: {
      fontSize: "22px",
      marginBottom: "25px"
    }
  },
  highlight: {
    paddingLeft: "15px",
    paddingRight: "15px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  highlight1: {
    paddingRight: "15px",
    backgroundImage: "linear-gradient(to bottom, rgba(0,0,0,0) 60%, #4229BC 60%)",
  },
  title: {
    marginBottom: "100px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "25px",
    }
  },
  featureImg: {
    marginBottom: "25px"
  },
  featureTitle: {
    fontStyle: "normal",
    fontWeight: "600",
    fontSize: "24px",
    lineHeight: "150%",
    textAlign: "center",
    color: "#FFFDFA",
    marginBottom: "15px",
    [theme.breakpoints.down("xs")]: {
      fontSize: "16px",
      lineHeight: "150%",
    }
  },
  featureContent: {
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "18px",
    lineHeight: "150%",
    textAlign: "center",
    color: "#A9AFC3",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      lineHeight: "150%",
    }
  },
  featureBlock: {
    width: "320px",
    [theme.breakpoints.down("xs")]: {
      width: "100%",
      marginBottom: "50px"
    }
  },
  featureBg: {
    backgroundImage: "url('nfts/feature-line.png')",
    backgroundRepeat: "no-repeat",
    backgroundSize: "70%, auto",
    backgroundPosition: "50% 12%",
    [theme.breakpoints.down("md")]: {
      background: 'none'
    }
  },
  handDrawn: {
    marginTop: "200px",
    marginBottom: "200px",
    [theme.breakpoints.down("xs")]: {
      flexFlow: "column-reverse",
      marginTop: "50px",
      marginBottom: "50px",
    }
  },
  subtitle2: {
    marginBottom: "25px",
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px",
      marginBottom: "15px",
    }
  },
  subtitle3: {
    fontSize: "18px",
    color: "#A9AFC3",
    fontWeight: "normal",
    lineHeight: "150%",
    [theme.breakpoints.down("xs")]: {
      fontSize: "14px"
    }
  },
  rightHand: {
    width: "100%",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "50px"
    }
  },
  nftImg: {
    width: "100%",
  },
  nftWrap: {
    padding: "40px",
    outline: 0,
    [theme.breakpoints.down("xs")]: {
      padding: "15px",
    }
  },
  sliderBlock: {
    marginBottom: "200px",
    [theme.breakpoints.down("xs")]: {
      marginBottom: "130px",
    }
  },
  ads: {
    width: "570px",
    height: "370px",
    borderRadius: ""
  },
  nextBtn: {
    width: "36px",
    height: "36px",
    [theme.breakpoints.down("xs")]: {
      width: "24px",
      height: "24px",
    }
  }
}));

const Feature = () => {
  const classes = useStyles();

  const settings = {
    className: "centered",
    centerMode: true,
    infinite: true,
    centerPadding: "0px",
    slidesToShow: 3,
    speed: 500,
    dots: true,
    appendDots: dots => (
      <div
        style={{
          backgroundColor: "#ddd",
          borderRadius: "10px",
          padding: "10px"
        }}
      >
        <ul style={{ margin: "0px" }}> {dots} </ul>
      </div>
    ),
    responsive: [
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          centerPadding: "40px",
        }
      }
    ]
  };
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Box className={classes.wrapper}>
      <Container>
        <Box display="flex" justifyContent="center" className={classes.title}>
          <Typography variant="subtitle1" className={classes.slideCaption}>
            <span className={classes.highlight}>Features</span>
          </Typography>
        </Box>
        <Grid container className={classes.featureBg}>
          <Grid item md={4}>
            <Box display="flex" justifyContent={!matches ? "start" : "center"}>
              <Box className={classes.featureBlock} textAlign="center">
                <img className={classes.featureImg} src="nfts/feature (1).png"></img>
                <Typography variant="subtitle1" className={classes.featureTitle}>Player Account Monetization</Typography>
                <Typography variant="subtitle1" className={classes.featureContent}>Our pNFT technology allows players to resell games and accounts within our ecosystem. Why stop at tokenizing your in-game items when you can monetize everything from your rank to in-game achievements and level unlocks. Imagine being able to earn passive income by renting out your maxed out Skybound account!</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item md={4}>
            <Box display="flex" justifyContent="center">
              <Box className={classes.featureBlock} textAlign="center">
                <img className={classes.featureImg} src="nfts/feature (2).png"></img>
                <Typography variant="subtitle1" className={classes.featureTitle}>First NFT & Mobile specific crypto wallet</Typography>
                <Typography variant="subtitle1" className={classes.featureContent}>The perfect mobile wallet with complete features as the sign-up portal in any one of our mobile games. Allow gamers to access the benefits/potential of NFT’s by simply playing. StormeWallet will be the App Store of crypto games. It will give a push for independent nft game companies to learn and grow within the gaming space while providing an option for a secure decentralized wallet for every gamers assets.</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item md={4}>
            <Box display="flex" justifyContent={!matches ? "end" : "center"}>
              <Box className={classes.featureBlock} textAlign="center">
                <img className={classes.featureImg} src="nfts/feature (3).png"></img>
                <Typography variant="subtitle1" className={classes.featureTitle}>Gaming specific NFT marketplace</Typography>
                <Typography variant="subtitle1" className={classes.featureContent}>Our marketplace will be built to support the renting and selling of our pNFT’s and be tailored specifically for the trading of in-game items. Additionally, there will be a workshop to connect creators with gamers ranging from item skins to game mods giving the creative capabilities and ownership back to the creators.</Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
        <Grid container spacing={!matches ? 8 : 0} className={classes.handDrawn}>
          <Grid item md={6}>
            <Box alignItems="baseline" justifyContent="center" display="flex" flexDirection="column" textAlign="left" height="100%">
              <Typography variant="subtitle1" className={classes.slideCaption1}>
                <span className={classes.highlight1}><span style={{ color: "#F0BC2B" }}>5,000</span> hand-drawn avatars</span><br></br> to kickoff your unique <br></br>gaming experience
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle2}>
                Introducing our genesis pNFT’s: the Dragonoids. <span style={{ fontStyle: "italic" }}>Hidden in the far distant planet of Mekon lives the empire of the Dragonoids harnessing the power of Meka, God of Bonds. Each Dragonoid stores the power of Meka in which it uses to store every detailed data of battle. Be one of the first to acquire a Dragonoid and harness the power of Meka itself!</span>
              </Typography>
              <Typography variant="subtitle2" className={classes.subtitle3}>
                The exclusive genesis NFT collection will provide users with priority access to all future Gamestorme games and NFT drops.
              </Typography>
            </Box>
          </Grid>
          <Grid item md={6}>
            <Box display="flex" justifyContent="end">
              <img className={classes.rightHand} src="nfts/artwork.png"></img>
            </Box>
          </Grid>
        </Grid>
      </Container>
      <Box className={classes.sliderBlock}>
        <Slider
          {...settings}>
          <Box display="flex" justifyContent="center" className={classes.nftWrap}>
            <img className={classes.nftImg} src="nfts/artwork.png"></img>
          </Box>
          <Box display="flex" justifyContent="center" className={classes.nftWrap}>
            <img className={classes.nftImg} src="nfts/artwork (1).png"></img>
          </Box>
          <Box display="flex" justifyContent="center" className={classes.nftWrap}>
            <img className={classes.nftImg} src="nfts/artwork (2).png"></img>
          </Box>
          <Box display="flex" justifyContent="center" className={classes.nftWrap}>
            <img className={classes.nftImg} src="nfts/nft (1).png"></img>
          </Box>
          <Box display="flex" justifyContent="center" className={classes.nftWrap}>
            <img className={classes.nftImg} src="nfts/nft (2).png"></img>
          </Box>
          <Box display="flex" justifyContent="center" className={classes.nftWrap}>
            <img className={classes.nftImg} src="nfts/nft (3).png"></img>
          </Box>
        </Slider>
      </Box>
      <Container>
        <Grid container spacing={8}>
          <Grid item md={6}>
            <Card className={classes.cardBody}>
              <CardMedia
                component="img"
                alt="Gamestorme"
                image="nfts/frame (1).png"
                title="Gamestorme"
                className={classes.media}
              />
              <Box className={classes.cardContent} display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="subtitle2" className={classes.cardTitle}>
                  Create a Wallet(Coming soon)
                </Typography>
                <img src="/next.png" className={classes.nextBtn} loading="eager"></img>
              </Box>
            </Card>
          </Grid>
          <Grid item md={6}>
            <Link
              target="_blank"
              rel="noopener noreferrer"
              href="https://discord.gg/wcBdt5duPB">
              <Card className={classes.cardBody}>
                <CardMedia
                  component="img"
                  alt="Gamestorme"
                  image="nfts/frame (2).png"
                  title="Gamestorme"
                  className={classes.media}
                />
                <Box className={classes.cardContent} display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="subtitle2" className={classes.cardTitle}>
                    Join our community
                  </Typography>
                  <img src="/next.png" className={classes.nextBtn} loading="eager"></img>
                </Box>
              </Card>
            </Link>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}
export default Feature;
