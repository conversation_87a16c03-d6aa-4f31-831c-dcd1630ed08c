# Gamestorme Website

A modern gaming platform that serves as a hub for all games, with features for game developers and players.

## Features

- **Game Store**: Browse and discover games in a modern, user-friendly interface
- **Marketing AI**: AI-powered marketing tools for game developers
- **pNFT System**: Playable NFTs that work across multiple games
- **Developer Ecosystem**: Resources and tools for game developers

## Technologies Used

- Next.js
- TypeScript
- Material UI
- Framer Motion
- React Icons

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/joelgriiyo/gamestorme.git
   cd gamestorme
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open [http://localhost:8000](http://localhost:8000) in your browser to see the result.

## Project Structure

- `pages/`: Next.js pages
- `components/`: Reusable React components
- `public/`: Static assets
- `styles/`: Global styles
- `theme/`: Material UI theme configuration
- `utils/`: Utility functions

## License

This project is licensed under the MIT License - see the LICENSE file for details.
