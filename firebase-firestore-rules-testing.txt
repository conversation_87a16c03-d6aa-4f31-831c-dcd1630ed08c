rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // TEMPORARY TESTING RULES - ALLOW PUBLIC ACCESS
    // WARNING: These rules are for development/testing only!
    // DO NOT USE IN PRODUCTION!
    
    // Games collection - Allow public read/write for testing
    match /games/{gameId} {
      allow read, write: if true;
    }
    
    // Support Tickets collection - Allow public read/write for testing
    match /supportTickets/{ticketId} {
      allow read, write: if true;
    }
    
    // Analytics Events collection - Allow public read/write for testing
    match /analyticsEvents/{eventId} {
      allow read, write: if true;
    }
    
    // Admin Logs collection - Allow public read/write for testing
    match /adminLogs/{logId} {
      allow read, write: if true;
    }
    
    // Notifications collection - Allow public read/write for testing
    match /notifications/{notificationId} {
      allow read, write: if true;
    }
    
    // Admin Notifications collection - Allow public read/write for testing
    match /adminNotifications/{notificationId} {
      allow read, write: if true;
    }
    
    // Users collection - Allow public read/write for testing
    match /users/{userId} {
      allow read, write: if true;
    }
    
    // Developers collection - Allow public read/write for testing
    match /developers/{developerId} {
      allow read, write: if true;
    }
    
    // Reviews collection - Allow public read/write for testing
    match /reviews/{reviewId} {
      allow read, write: if true;
    }
    
    // News collection - Allow public read/write for testing
    match /news/{articleId} {
      allow read, write: if true;
    }
    
    // Categories collection - Allow public read/write for testing
    match /categories/{categoryId} {
      allow read, write: if true;
    }
    
    // Platform Metrics collection - Allow public read/write for testing
    match /platformMetrics/{metricId} {
      allow read, write: if true;
    }
    
    // Game Uploads collection - Allow public read/write for testing
    match /gameUploads/{uploadId} {
      allow read, write: if true;
    }
    
    // Creator Content collection - Allow public read/write for testing
    match /creatorContent/{contentId} {
      allow read, write: if true;
    }
    
    // Business Partnerships collection - Allow public read/write for testing
    match /partnerships/{partnershipId} {
      allow read, write: if true;
    }
    
    // User Sessions collection - Allow public read/write for testing
    match /userSessions/{sessionId} {
      allow read, write: if true;
    }
    
    // Default fallback rule - Allow public read/write for testing
    match /{document=**} {
      allow read, write: if true;
    }
  }
}

// IMPORTANT NOTES:
// 1. These rules allow ANYONE to read/write ALL data
// 2. Use ONLY for development/testing
// 3. Replace with secure rules before going to production
// 4. Never use these rules with real user data
