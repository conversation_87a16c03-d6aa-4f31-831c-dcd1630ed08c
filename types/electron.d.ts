// Electron API types for GameStorme Desktop App

export interface ElectronAPI {
  // App information
  getAppVersion: () => Promise<string>;
  showMessageBox: (options: any) => Promise<any>;
  checkForUpdates: () => Promise<{ updateAvailable: boolean }>;

  // Game Engine APIs
  getInstalledGames: () => Promise<Game[]>;
  addInstalledGame: (gameData: GameData) => Promise<boolean>;
  launchGame: (gameId: string) => Promise<LaunchResult>;
  downloadGame: (gameData: GameData) => Promise<DownloadResult>;
  uninstallGame: (gameId: string) => Promise<UninstallResult>;
  
  // Configuration
  getGameEngineConfig: () => Promise<GameEngineConfig | null>;
  updateGameEngineConfig: (config: GameEngineConfig) => Promise<boolean>;
  
  // System information
  getSystemInfo: () => Promise<SystemInfo>;
}

export interface Game {
  id: string;
  title: string;
  version: string;
  directory: string;
  executable: string;
  installDate: string;
  size: string;
  developer: string;
  description: string;
  thumbnail: string;
  lastPlayed?: string;
}

export interface GameData {
  id: string;
  title: string;
  version?: string;
  size?: string;
  developer: string;
  description: string;
  thumbnail: string;
  downloadUrl?: string;
  executable?: string;
}

export interface LaunchResult {
  success: boolean;
  message?: string;
  error?: string;
}

export interface DownloadResult {
  success: boolean;
  game?: Game;
  error?: string;
}

export interface UninstallResult {
  success: boolean;
  error?: string;
}

export interface GameEngineConfig {
  version: string;
  theme: 'dark' | 'light';
  autoLaunch: boolean;
  notifications: boolean;
  gameSettings: {
    defaultResolution: string;
    fullscreen: boolean;
    vsync: boolean;
    antiAliasing: boolean;
  };
  installedGames: Game[];
  recentGames: Game[];
}

export interface SystemInfo {
  platform: string;
  arch: string;
  release: string;
  totalMemory: number;
  freeMemory: number;
  cpus: number;
  homeDir: string;
  gameEngineDir: string;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}
