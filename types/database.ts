// Database types for Firestore collections

export interface Game {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  developer: {
    name: string;
    email: string;
    website?: string;
    uid: string;
  };
  images: {
    thumbnail: string;
    screenshots: string[];
    banner?: string;
    logo?: string;
  };
  details: {
    genre: string[];
    platforms: string[];
    releaseDate: string;
    version: string;
    size: string;
    rating: number;
    ageRating: string;
    languages: string[];
  };
  pricing: {
    price: number;
    currency: string;
    discount?: number;
    isFree: boolean;
  };
  features: string[];
  systemRequirements?: {
    minimum: {
      os: string;
      processor: string;
      memory: string;
      graphics: string;
      storage: string;
    };
    recommended?: {
      os: string;
      processor: string;
      memory: string;
      graphics: string;
      storage: string;
    };
  };
  downloadLinks?: {
    windows?: string;
    mac?: string;
    linux?: string;
    android?: string;
    ios?: string;
  };
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  featured: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  approvedBy?: string;
  rejectionReason?: string;
  stats: {
    downloads: number;
    views: number;
    likes: number;
    reviews: number;
  };
}

export interface NewsArticle {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  author: {
    name: string;
    email: string;
    avatar?: string;
  };
  images: {
    featured: string;
    gallery?: string[];
  };
  category: string;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  stats: {
    views: number;
    likes: number;
    shares: number;
  };
}

export interface AdminUser {
  uid: string;
  email: string;
  name: string;
  role: 'admin' | 'moderator' | 'editor';
  permissions: string[];
  createdAt: Date;
  lastLogin?: Date;
  isActive: boolean;
}

export interface GameReview {
  id: string;
  gameId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  content: string;
  helpful: number;
  reported: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Developer {
  uid: string;
  email: string;
  name: string;
  company?: string;
  website?: string;
  bio?: string;
  avatar?: string;
  verified: boolean;
  games: string[]; // Array of game IDs
  stats: {
    totalGames: number;
    totalDownloads: number;
    averageRating: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface GameCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  gameCount: number;
  featured: boolean;
  createdAt: Date;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

// Filter and query types
export interface GameFilters {
  genre?: string[];
  platform?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  featured?: boolean;
  status?: string;
  developer?: string;
  search?: string;
}

export interface NewsFilters {
  category?: string;
  tags?: string[];
  author?: string;
  featured?: boolean;
  status?: string;
  search?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Upload types
export interface FileUpload {
  file: File;
  path: string;
  metadata?: {
    contentType: string;
    customMetadata?: Record<string, string>;
  };
}

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  state: 'running' | 'paused' | 'success' | 'canceled' | 'error';
  metadata?: any;
  downloadURL?: string;
}
