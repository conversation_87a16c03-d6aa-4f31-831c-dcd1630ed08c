declare module 'react-toastify' {
  import { ReactNode } from 'react';

  export interface ToastContainerProps {
    position?: 'top-right' | 'top-center' | 'top-left' | 'bottom-right' | 'bottom-center' | 'bottom-left';
    autoClose?: number | false;
    hideProgressBar?: boolean;
    newestOnTop?: boolean;
    closeOnClick?: boolean;
    rtl?: boolean;
    pauseOnFocusLoss?: boolean;
    draggable?: boolean;
    pauseOnHover?: boolean;
    theme?: 'light' | 'dark' | 'colored';
    closeButton?: ReactNode;
    transition?: any;
    className?: string;
    style?: React.CSSProperties;
    toastStyle?: React.CSSProperties;
    toastClassName?: string;
    bodyClassName?: string;
    progressClassName?: string;
    progressStyle?: React.CSSProperties;
    draggablePercent?: number;
    containerId?: string;
    limit?: number;
    enableMultiContainer?: boolean;
    children?: ReactNode;
  }

  export class ToastContainer extends React.Component<ToastContainerProps> {}

  export interface Toast {
    (content: ReactNode, options?: any): any;
    info(content: ReactNode, options?: any): any;
    success(content: ReactNode, options?: any): any;
    warning(content: ReactNode, options?: any): any;
    error(content: ReactNode, options?: any): any;
    dismiss(id?: string | number): void;
  }

  export const toast: Toast;
}
