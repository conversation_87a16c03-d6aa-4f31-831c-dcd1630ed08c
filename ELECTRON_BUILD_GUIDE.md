# 🚀 Gamestorme Electron Desktop Application Build Guide

## Overview
This guide will help you build a complete Windows executable (.exe) from your Gamestorme React/Next.js web application using Electron.js and upload it to Firebase Storage for distribution.

## 📋 Prerequisites

### Required Software
- **Node.js 20.x** or higher
- **npm** or **yarn** package manager
- **Git** for version control
- **Windows** (for building .exe files)

### Firebase Setup
- Firebase project with Storage enabled
- Firebase configuration credentials
- Storage rules configured for public downloads

## 🚀 Quick Start (Recommended)

### Using the Setup Script
```bash
# Switch to desktop development mode
node setup-electron.js desktop

# Build Windows executable
node setup-electron.js build

# Upload to Firebase Storage
node setup-electron.js upload

# Switch back to web mode for Heroku
node setup-electron.js web
```

## 🔧 Manual Step-by-Step Process

### Step 1: Switch to Desktop Mode
```bash
# Copy Electron configuration
cp electron-package.json package.json

# Install Electron dependencies
npm install
```

### Step 2: Build the Electron Application
```bash
# Build Next.js app for Electron
ELECTRON_BUILD=true npm run build

# Build Windows executable
npm run dist:win

# Build all platforms
npm run dist
```

## 📁 Project Structure

```
gamestorme/
├── public/
│   ├── electron.js          # Main Electron process
│   └── preload.js           # Preload script for security
├── scripts/
│   ├── build-electron.js    # Custom build script
│   └── upload-to-firebase.js # Firebase upload script
├── build/                   # Next.js build output (for Electron)
├── dist/                    # Electron executables
├── package.json             # Main package configuration
└── next.config.js           # Next.js configuration with Electron support
```

## ⚙️ Configuration Files

### package.json - Electron Configuration
```json
{
  "main": "public/electron.js",
  "scripts": {
    "electron": "electron .",
    "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:8000 && electron .\"",
    "build:electron": "electron-builder",
    "dist:win": "npm run build && electron-builder --win"
  },
  "build": {
    "appId": "com.gamestorme.launcher",
    "productName": "Gamestorme Launcher",
    "win": {
      "target": "nsis",
      "icon": "public/assets/images/logo.png"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true
    }
  }
}
```

### next.config.js - Electron Support
```javascript
const nextConfig = {
  output: process.env.ELECTRON_BUILD ? 'export' : 'standalone',
  trailingSlash: process.env.ELECTRON_BUILD ? true : false,
  distDir: process.env.ELECTRON_BUILD ? 'build' : '.next',
  webpack: (config, { isServer }) => {
    if (process.env.ELECTRON_BUILD) {
      config.target = 'electron-renderer';
    }
    return config;
  }
};
```

## 🔥 Firebase Storage Upload

### Step 1: Configure Firebase
1. Update `scripts/upload-to-firebase.js` with your Firebase config
2. Replace placeholder API keys with real credentials
3. Ensure Firebase Storage rules allow public downloads

### Step 2: Upload Executable
```bash
# Upload built executables to Firebase Storage
node scripts/upload-to-firebase.js --run
```

### Step 3: Update Download URLs
After upload, update the download URLs in `components/DownloadLauncher.tsx`:
```javascript
const realDownloadUrls = {
  'windows': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FYourActualFile.exe?alt=media',
  // ... other platforms
};
```

## 🎯 Build Commands Reference

### Development
```bash
# Run web app in development
npm run dev

# Run Electron app in development
npm run electron-dev
```

### Production Build
```bash
# Build web app for production
npm run build

# Build Electron app
npm run build:electron

# Build Windows executable
npm run dist:win

# Build and package (without installer)
npm run pack
```

### Firebase Deployment
```bash
# Upload executables to Firebase
node scripts/upload-to-firebase.js --run

# Deploy web app to Heroku
git push heroku main
```

## 📦 Output Files

After successful build, you'll find:

### Windows Build
- `dist/Gamestorme Launcher Setup 2.0.0.exe` - NSIS installer
- `dist/win-unpacked/` - Unpacked application files

### macOS Build
- `dist/Gamestorme Launcher-2.0.0.dmg` - DMG package
- `dist/mac/` - Application bundle

### Linux Build
- `dist/Gamestorme Launcher-2.0.0.AppImage` - Portable executable
- `dist/linux-unpacked/` - Unpacked application files

## 🔧 Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   # Clear cache and rebuild
   rm -rf node_modules build dist .next
   npm install
   npm run build:electron
   ```

2. **Electron Window Doesn't Load**
   - Check `public/electron.js` file paths
   - Verify Next.js build completed successfully
   - Check console for errors

3. **Firebase Upload Fails**
   - Verify Firebase credentials
   - Check Storage rules
   - Ensure file exists in dist directory

### Debug Mode
```bash
# Enable Electron debug mode
DEBUG=electron* npm run electron

# Build with verbose output
npm run dist:win -- --publish=never --debug
```

## 🚀 Deployment Workflow

### Complete Build and Deploy Process
```bash
# 1. Build the application
npm run build

# 2. Create Electron executable
npm run dist:win

# 3. Upload to Firebase Storage
node scripts/upload-to-firebase.js --run

# 4. Update download URLs in code
# Edit components/DownloadLauncher.tsx

# 5. Deploy web app
git add .
git commit -m "Update with real executable"
git push heroku main
```

## 📊 File Sizes (Approximate)
- **Windows .exe**: 150-200 MB
- **macOS .dmg**: 160-210 MB
- **Linux .AppImage**: 155-205 MB

## 🔐 Security Considerations
- Code signing for Windows executables
- Notarization for macOS applications
- Virus scanning before distribution
- Secure Firebase Storage rules

## 📞 Support
- **Documentation**: This guide
- **Issues**: GitHub Issues
- **Build Problems**: Check console output and logs

---

**Your Gamestorme desktop application is now ready for professional distribution! 🎮✨**
