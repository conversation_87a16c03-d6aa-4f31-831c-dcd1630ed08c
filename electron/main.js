const { app, BrowserWindow, Menu, shell, ipcMain, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/assets/images/logo.png'),
    titleBarStyle: 'default',
    show: false,
    backgroundColor: '#1a1a2e'
  });

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:8000' 
    : `file://${path.join(__dirname, '../.next/server/pages/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Prevent navigation to external websites
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:8000' && !isDev) {
      event.preventDefault();
    }
  });
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'Gamestorme',
      submenu: [
        {
          label: 'About Gamestorme',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Gamestorme',
              message: 'Gamestorme Launcher',
              detail: 'Version 2.0.0\nEmpowering Game Developers Through Innovative Solutions\n\nDeveloped by Gamestorme Team\n© 2024 Gamestorme. All rights reserved.'
            });
          }
        },
        { type: 'separator' },
        {
          label: 'Preferences',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            // Navigate to settings page
            mainWindow.webContents.executeJavaScript(`
              window.location.hash = '#settings';
            `);
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Home',
          accelerator: 'CmdOrCtrl+H',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.href = '/';
            `);
          }
        },
        {
          label: 'Developer Dashboard',
          accelerator: 'CmdOrCtrl+D',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.href = '/developer/dashboard';
            `);
          }
        },
        {
          label: 'Gamer Dashboard',
          accelerator: 'CmdOrCtrl+G',
          click: () => {
            mainWindow.webContents.executeJavaScript(`
              window.location.href = '/gamer/dashboard';
            `);
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'Gamestorme Website',
          click: () => {
            shell.openExternal('https://gamestorme.com');
          }
        },
        {
          label: 'Support Center',
          click: () => {
            shell.openExternal('https://support.gamestorme.com');
          }
        },
        {
          label: 'Developer Documentation',
          click: () => {
            shell.openExternal('https://docs.gamestorme.com');
          }
        },
        { type: 'separator' },
        {
          label: 'Report Issue',
          click: () => {
            shell.openExternal('https://github.com/joelgriiyo/gamestorme/issues');
          }
        }
      ]
    }
  ];

  if (process.platform === 'darwin') {
    template[0].label = app.getName();
    template[0].submenu.unshift({
      label: 'About ' + app.getName(),
      role: 'about'
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event listeners
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC handlers for communication with renderer process
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// Handle app updates (for future implementation)
ipcMain.handle('check-for-updates', () => {
  // This would integrate with auto-updater in production
  return { updateAvailable: false };
});

// Handle deep links (for future implementation)
app.setAsDefaultProtocolClient('gamestorme');

// Handle protocol for deep linking
app.on('open-url', (event, url) => {
  event.preventDefault();
  // Handle gamestorme:// protocol URLs
  console.log('Deep link:', url);
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // Someone tried to run a second instance, focus our window instead
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
