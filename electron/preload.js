const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON>er without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // Update methods
  checkForUpdates: () => ipc<PERSON>enderer.invoke('check-for-updates'),
  
  // Platform information
  platform: process.platform,
  
  // Environment
  isDev: process.env.NODE_ENV === 'development',
  
  // Gamestorme specific methods
  openExternalLink: (url) => {
    // This will be handled by the main process
    ipcRenderer.send('open-external', url);
  },
  
  // Navigation helpers
  navigateTo: (path) => {
    window.location.href = path;
  },
  
  // Local storage helpers for desktop app
  setDesktopSetting: (key, value) => {
    localStorage.setItem(`gamestorme-desktop-${key}`, JSON.stringify(value));
  },
  
  getDesktopSetting: (key) => {
    const item = localStorage.getItem(`gamestorme-desktop-${key}`);
    return item ? JSON.parse(item) : null;
  },
  
  // Desktop-specific features
  isDesktopApp: true,
  
  // Notification helpers
  showNotification: (title, body, icon) => {
    if (Notification.permission === 'granted') {
      new Notification(title, { body, icon });
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(title, { body, icon });
        }
      });
    }
  }
});

// Add desktop-specific styling when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Add desktop app class to body
  document.body.classList.add('desktop-app');
  
  // Add desktop-specific meta tag
  const meta = document.createElement('meta');
  meta.name = 'gamestorme-desktop';
  meta.content = 'true';
  document.head.appendChild(meta);
  
  // Disable text selection for better desktop feel
  document.body.style.userSelect = 'none';
  document.body.style.webkitUserSelect = 'none';
  
  // Allow text selection in input fields
  const inputs = document.querySelectorAll('input, textarea');
  inputs.forEach(input => {
    input.style.userSelect = 'text';
    input.style.webkitUserSelect = 'text';
  });
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (event) => {
  // Prevent default browser shortcuts that don't make sense in desktop app
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'r':
        if (!event.shiftKey) {
          event.preventDefault();
          window.location.reload();
        }
        break;
      case 'w':
        event.preventDefault();
        // Don't close tab in desktop app
        break;
      case 't':
        event.preventDefault();
        // Don't open new tab in desktop app
        break;
    }
  }
});

// Desktop app initialization
window.addEventListener('load', () => {
  // Set desktop app flag
  window.isDesktopApp = true;
  
  // Initialize desktop-specific features
  if (typeof window.initDesktopFeatures === 'function') {
    window.initDesktopFeatures();
  }
  
  // Show desktop welcome message (only on first launch)
  const hasShownWelcome = localStorage.getItem('gamestorme-desktop-welcome-shown');
  if (!hasShownWelcome) {
    setTimeout(() => {
      if (window.electronAPI && window.electronAPI.showNotification) {
        window.electronAPI.showNotification(
          'Welcome to Gamestorme!',
          'Your desktop launcher is ready. Explore games, connect with developers, and enjoy the ultimate gaming experience.',
          '/assets/images/logo.png'
        );
      }
      localStorage.setItem('gamestorme-desktop-welcome-shown', 'true');
    }, 2000);
  }
});

// Error handling for desktop app
window.addEventListener('error', (event) => {
  console.error('Desktop app error:', event.error);
  
  // Report critical errors to main process
  if (event.error && event.error.message) {
    ipcRenderer.send('renderer-error', {
      message: event.error.message,
      stack: event.error.stack,
      timestamp: new Date().toISOString()
    });
  }
});

// Unhandled promise rejection handling
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in desktop app:', event.reason);
  
  ipcRenderer.send('renderer-error', {
    message: 'Unhandled promise rejection',
    reason: event.reason,
    timestamp: new Date().toISOString()
  });
});

// Desktop app performance monitoring
if (typeof PerformanceObserver !== 'undefined') {
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
      if (entry.entryType === 'navigation' && entry.loadEventEnd > 5000) {
        console.warn('Slow page load detected:', entry.loadEventEnd + 'ms');
      }
    });
  });
  
  observer.observe({ entryTypes: ['navigation'] });
}
