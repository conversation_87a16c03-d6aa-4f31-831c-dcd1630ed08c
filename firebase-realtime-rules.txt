{
  "rules": {
    // User profiles and authentication data
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid || auth.token.admin === true",
        ".write": "$uid === auth.uid || auth.token.admin === true",
        ".validate": "auth != null"
      }
    },
    
    // Real-time chat and messaging
    "chat": {
      "rooms": {
        "$roomId": {
          ".read": "auth != null",
          ".write": "auth != null",
          "messages": {
            "$messageId": {
              ".validate": "newData.hasChildren(['text', 'timestamp', 'userId']) && newData.child('userId').val() === auth.uid"
            }
          }
        }
      }
    },
    
    // Live game statistics and analytics
    "gameStats": {
      "$gameId": {
        ".read": true,
        ".write": "auth != null && (auth.token.admin === true || auth.token.developer === true)",
        "downloads": {
          ".validate": "newData.isNumber()"
        },
        "views": {
          ".validate": "newData.isNumber()"
        },
        "likes": {
          ".validate": "newData.isNumber()"
        }
      }
    },
    
    // Real-time notifications
    "notifications": {
      "$userId": {
        ".read": "$userId === auth.uid || auth.token.admin === true",
        ".write": "auth != null && (auth.token.admin === true || auth.token.system === true)",
        "$notificationId": {
          ".validate": "newData.hasChildren(['message', 'timestamp', 'read'])"
        }
      }
    },
    
    // Live user presence
    "presence": {
      "$userId": {
        ".read": true,
        ".write": "$userId === auth.uid",
        ".validate": "newData.hasChildren(['online', 'lastSeen'])"
      }
    },
    
    // Real-time game sessions (for multiplayer)
    "gameSessions": {
      "$sessionId": {
        ".read": "auth != null",
        ".write": "auth != null",
        "players": {
          "$playerId": {
            ".validate": "$playerId === auth.uid || auth.token.admin === true"
          }
        }
      }
    },
    
    // Live leaderboards
    "leaderboards": {
      "$gameId": {
        ".read": true,
        ".write": "auth != null",
        "scores": {
          "$userId": {
            ".validate": "auth.uid === $userId && newData.isNumber()"
          }
        }
      }
    },
    
    // System status and health checks
    "system": {
      "status": {
        ".read": true,
        ".write": "auth != null && auth.token.admin === true"
      },
      "maintenance": {
        ".read": true,
        ".write": "auth != null && auth.token.admin === true"
      }
    },
    
    // Developer analytics
    "analytics": {
      "$developerId": {
        ".read": "auth.uid === $developerId || auth.token.admin === true",
        ".write": "auth != null && (auth.token.admin === true || auth.token.system === true)"
      }
    },
    
    // Admin logs
    "adminLogs": {
      ".read": "auth != null && auth.token.admin === true",
      ".write": "auth != null && auth.token.admin === true",
      "$logId": {
        ".validate": "newData.hasChildren(['action', 'timestamp', 'userId'])"
      }
    }
  }
}
