// Simple test for the new Stormie Chat API
const axios = require('axios');

const BASE_URL = 'http://localhost:8000/api/stormie';

async function testStormieChat() {
  console.log('🧠 Testing Stormie Chat API...\n');

  const testMessages = [
    "Hello Stormie!",
    "What can you help me with?",
    "I have a puzzle game called Mind Maze",
    "Can you analyze my space exploration RPG?",
    "Generate keywords for my mobile action game",
    "Create Twitter content for my indie game launch",
    "What's the best pricing for a premium strategy game?",
    "Show me market insights for casual games"
  ];

  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`📝 Test ${i + 1}: "${message}"`);
    
    try {
      const response = await axios.post(`${BASE_URL}/chat`, {
        message: message,
        context: 'Testing Stormie Chat',
        gameData: {
          title: 'Test Game',
          genre: 'Action',
          platform: 'Mobile'
        }
      });

      if (response.data.success) {
        console.log(`✅ Response: "${response.data.response.substring(0, 100)}..."`);
        console.log(`📊 Type: ${response.data.type || 'general'}`);
        if (response.data.data) {
          console.log(`📈 Additional Data: Available`);
        }
      } else {
        console.log(`❌ Failed: ${response.data.error}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.response?.data?.error || error.message}`);
    }
    
    console.log(''); // Empty line for readability
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('🎯 Chat API Test Complete!');
  console.log('\n🎮 Now test in browser:');
  console.log('1. Go to http://localhost:8000/developer/dashboard');
  console.log('2. Click "Marketing AI" in sidebar');
  console.log('3. Click "Stormie Chat" feature card');
  console.log('4. Try chatting with Stormie like ChatGPT!');
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.log('\n❌ Test Error:', error.message);
  console.log('💡 Make sure the development server is running on port 8000');
});

// Run test
testStormieChat();
