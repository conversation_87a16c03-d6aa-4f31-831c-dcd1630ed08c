// 🔒 Gamestorme Firestore Security Rules - Maximum Protection
// Advanced security rules to prevent unauthorized access and data breaches

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // 🛡️ Helper Functions for Security
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isAdmin() {
      return isAuthenticated() &&
             request.auth.token.email == '<EMAIL>';
    }

    function isValidString(str, maxLength) {
      return str is string && str.size() <= maxLength && str.size() > 0;
    }

    // 👤 User Profiles - Strict Access Control
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(userId);
      allow update: if isAuthenticated() && isOwner(userId);
      allow delete: if isAdmin();
    }

    // 🎮 Games Collection - Developer and Admin Control
    match /games/{gameId} {
      allow read: if true; // Public read for games
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() || isAdmin();
      allow delete: if isAdmin();
    }

    // 🎫 Support Tickets - User and Admin Access
    match /supportTickets/{ticketId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // 🔔 Notifications - User Specific
    match /notifications/{notificationId} {
      allow read: if isAuthenticated();
      allow create: if isAdmin();
      allow update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // 📊 Analytics - Admin access
    match /analytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // 🔒 Admin Logs - Admin Only
    match /adminLogs/{logId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // 💰 Financial Data - Strict Admin Only
    match /financial/{docId} {
      allow read, write: if isAdmin();
    }

    // 🛡️ Security Logs - Admin Only
    match /securityLogs/{logId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // 📈 Platform Stats - Read for authenticated users
    match /platformStats/{statId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // 🎯 Game Reviews - User and Admin Control
    match /gameReviews/{reviewId} {
      allow read: if true; // Public read
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() || isAdmin();
      allow delete: if isAuthenticated() || isAdmin();
    }

    // 💬 Comments - User Control with Moderation
    match /comments/{commentId} {
      allow read: if true; // Public read
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() || isAdmin();
      allow delete: if isAuthenticated() || isAdmin();
    }

    // 🔐 API Keys - Admin Only
    match /apiKeys/{keyId} {
      allow read, write: if isAdmin();
    }

    // 🚫 Blacklist - Admin Only
    match /blacklist/{entryId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }

    // 📱 App Settings - Admin Control
    match /appSettings/{settingId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // 🚨 Emergency Override - Super Admin Only
    match /emergency/{docId} {
      allow read, write: if isAdmin() &&
                         request.auth.token.email == '<EMAIL>';
    }

    // 🛡️ Default Deny Rule - Block all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
