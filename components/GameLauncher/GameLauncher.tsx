import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  IconButton,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  PlayArrow as PlayIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Delete as DeleteIcon,
  Folder as FolderIcon,
  Computer as ComputerIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  Update as UpdateIcon,
} from '@mui/icons-material';

const LauncherContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
  color: 'white',
}));

const GameCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
  },
}));

const SystemCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.05)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
  borderRadius: theme.spacing(1),
}));

interface Game {
  id: string;
  title: string;
  version: string;
  directory: string;
  executable: string;
  installDate: string;
  size: string;
  developer: string;
  description: string;
  thumbnail: string;
  lastPlayed?: string;
}

interface SystemInfo {
  platform: string;
  arch: string;
  release: string;
  totalMemory: number;
  freeMemory: number;
  cpus: number;
  homeDir: string;
  gameEngineDir: string;
}

const GameLauncher: React.FC = () => {
  const [installedGames, setInstalledGames] = useState<Game[]>([]);
  const [recentGames, setRecentGames] = useState<Game[]>([]);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [launchingGame, setLaunchingGame] = useState<string | null>(null);
  const [downloadingGame, setDownloadingGame] = useState<string | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);

  // Check if we're running in Electron
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  useEffect(() => {
    if (isElectron) {
      loadGameData();
      loadSystemInfo();
    } else {
      setLoading(false);
    }
  }, [isElectron]);

  const loadGameData = async () => {
    try {
      if (window.electronAPI) {
        const games = await window.electronAPI.getInstalledGames();
        const config = await window.electronAPI.getGameEngineConfig();
        
        setInstalledGames(games || []);
        setRecentGames(config?.recentGames || []);
      }
    } catch (error) {
      console.error('Error loading game data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSystemInfo = async () => {
    try {
      if (window.electronAPI) {
        const info = await window.electronAPI.getSystemInfo();
        setSystemInfo(info);
      }
    } catch (error) {
      console.error('Error loading system info:', error);
    }
  };

  const handleLaunchGame = async (gameId: string) => {
    if (!window.electronAPI) return;
    
    setLaunchingGame(gameId);
    try {
      const result = await window.electronAPI.launchGame(gameId);
      if (result.success) {
        // Reload recent games
        loadGameData();
      } else {
        alert(`Failed to launch game: ${result.error}`);
      }
    } catch (error) {
      console.error('Error launching game:', error);
      alert('Failed to launch game');
    } finally {
      setLaunchingGame(null);
    }
  };

  const handleDownloadGame = async (gameData: any) => {
    if (!window.electronAPI) return;
    
    setDownloadingGame(gameData.id);
    try {
      const result = await window.electronAPI.downloadGame(gameData);
      if (result.success) {
        loadGameData();
        alert(`${gameData.title} installed successfully!`);
      } else {
        alert(`Failed to install game: ${result.error}`);
      }
    } catch (error) {
      console.error('Error downloading game:', error);
      alert('Failed to install game');
    } finally {
      setDownloadingGame(null);
    }
  };

  const handleUninstallGame = async (gameId: string) => {
    if (!window.electronAPI) return;
    
    if (confirm('Are you sure you want to uninstall this game?')) {
      try {
        const result = await window.electronAPI.uninstallGame(gameId);
        if (result.success) {
          loadGameData();
        } else {
          alert(`Failed to uninstall game: ${result.error}`);
        }
      } catch (error) {
        console.error('Error uninstalling game:', error);
        alert('Failed to uninstall game');
      }
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isElectron) {
    return (
      <LauncherContainer>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="info" sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              🎮 GameStorme Desktop Launcher
            </Typography>
            <Typography variant="body2">
              This is the desktop game launcher interface. To use this feature, please download and install the GameStorme Desktop App.
            </Typography>
            <Button 
              variant="contained" 
              sx={{ mt: 2 }}
              startIcon={<DownloadIcon />}
              onClick={() => window.open('/download', '_blank')}
            >
              Download Desktop App
            </Button>
          </Alert>
        </Container>
      </LauncherContainer>
    );
  }

  if (loading) {
    return (
      <LauncherContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
          <CircularProgress size={60} sx={{ color: '#ff5722' }} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            Loading GameStorme Engine...
          </Typography>
        </Box>
      </LauncherContainer>
    );
  }

  return (
    <LauncherContainer>
      <Container maxWidth="xl" sx={{ py: 2 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" fontWeight="bold">
            🎮 GameStorme Engine
          </Typography>
          <Box>
            <IconButton onClick={() => setSettingsOpen(true)} sx={{ color: 'white' }}>
              <SettingsIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Navigation Tabs */}
        <Tabs 
          value={activeTab} 
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ mb: 3, '& .MuiTab-root': { color: 'rgba(255,255,255,0.7)' } }}
        >
          <Tab label="Library" />
          <Tab label="Recent" />
          <Tab label="Store" />
          <Tab label="System" />
        </Tabs>

        {/* Library Tab */}
        {activeTab === 0 && (
          <Grid container spacing={3}>
            {installedGames.length === 0 ? (
              <Grid item xs={12}>
                <Alert severity="info">
                  <Typography variant="h6" gutterBottom>
                    No games installed yet
                  </Typography>
                  <Typography variant="body2">
                    Visit the Store tab to browse and install games from GameStorme.
                  </Typography>
                </Alert>
              </Grid>
            ) : (
              installedGames.map((game) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={game.id}>
                  <GameCard>
                    <CardMedia
                      component="img"
                      height="200"
                      image={game.thumbnail || '/game-icon-1.png'}
                      alt={game.title}
                    />
                    <CardContent>
                      <Typography variant="h6" gutterBottom noWrap>
                        {game.title}
                      </Typography>
                      <Typography variant="body2" color="rgba(255,255,255,0.7)" gutterBottom>
                        by {game.developer}
                      </Typography>
                      <Typography variant="caption" display="block" gutterBottom>
                        Version {game.version} • {game.size}
                      </Typography>
                      
                      <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                        <Button
                          variant="contained"
                          startIcon={launchingGame === game.id ? <CircularProgress size={16} /> : <PlayIcon />}
                          onClick={() => handleLaunchGame(game.id)}
                          disabled={launchingGame === game.id}
                          fullWidth
                        >
                          {launchingGame === game.id ? 'Launching...' : 'Play'}
                        </Button>
                        <IconButton 
                          onClick={() => handleUninstallGame(game.id)}
                          sx={{ color: 'error.main' }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </GameCard>
                </Grid>
              ))
            )}
          </Grid>
        )}

        {/* Recent Tab */}
        {activeTab === 1 && (
          <Grid container spacing={3}>
            {recentGames.length === 0 ? (
              <Grid item xs={12}>
                <Alert severity="info">
                  <Typography variant="body2">
                    No recently played games. Start playing to see your recent activity here.
                  </Typography>
                </Alert>
              </Grid>
            ) : (
              recentGames.map((game) => (
                <Grid item xs={12} sm={6} md={4} key={game.id}>
                  <GameCard>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar src={game.thumbnail} sx={{ mr: 2 }} />
                        <Box>
                          <Typography variant="h6">{game.title}</Typography>
                          <Typography variant="caption" color="rgba(255,255,255,0.7)">
                            Last played: {new Date(game.lastPlayed!).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Box>
                      <Button
                        variant="outlined"
                        startIcon={<PlayIcon />}
                        onClick={() => handleLaunchGame(game.id)}
                        fullWidth
                      >
                        Play Again
                      </Button>
                    </CardContent>
                  </GameCard>
                </Grid>
              ))
            )}
          </Grid>
        )}

        {/* Store Tab */}
        {activeTab === 2 && (
          <Alert severity="info">
            <Typography variant="h6" gutterBottom>
              🏪 GameStorme Store
            </Typography>
            <Typography variant="body2">
              The integrated store will show available games from the GameStorme platform. 
              Games can be downloaded and installed directly to your library.
            </Typography>
            <Button 
              variant="contained" 
              sx={{ mt: 2 }}
              onClick={() => window.open('/', '_blank')}
            >
              Browse Online Store
            </Button>
          </Alert>
        )}

        {/* System Tab */}
        {activeTab === 3 && systemInfo && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <SystemCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <ComputerIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    System Information
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="Platform" 
                        secondary={`${systemInfo.platform} ${systemInfo.arch}`} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="OS Release" 
                        secondary={systemInfo.release} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="CPU Cores" 
                        secondary={systemInfo.cpus} 
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </SystemCard>
            </Grid>
            <Grid item xs={12} md={6}>
              <SystemCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <MemoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Memory Usage
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      Used: {formatBytes(systemInfo.totalMemory - systemInfo.freeMemory)} / {formatBytes(systemInfo.totalMemory)}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={((systemInfo.totalMemory - systemInfo.freeMemory) / systemInfo.totalMemory) * 100}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                  <Typography variant="caption" color="rgba(255,255,255,0.7)">
                    Free: {formatBytes(systemInfo.freeMemory)}
                  </Typography>
                </CardContent>
              </SystemCard>
            </Grid>
            <Grid item xs={12}>
              <SystemCard>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <FolderIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    GameStorme Directories
                  </Typography>
                  <Typography variant="body2" color="rgba(255,255,255,0.7)">
                    Games Directory: {systemInfo.gameEngineDir}
                  </Typography>
                </CardContent>
              </SystemCard>
            </Grid>
          </Grid>
        )}
      </Container>
    </LauncherContainer>
  );
};

export default GameLauncher;
