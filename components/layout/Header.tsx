import React, { useState, useEffect } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON>,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
  useScrollTrigger,
  Slide,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  useTheme,
  useMediaQuery,
  alpha
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { useRouter } from 'next/router';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import InfoIcon from '@mui/icons-material/Info';
import WorkIcon from '@mui/icons-material/Work';
import GroupIcon from '@mui/icons-material/Group';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import NewspaperIcon from '@mui/icons-material/Newspaper';
import BusinessIcon from '@mui/icons-material/Business';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import DashboardIcon from '@mui/icons-material/Dashboard';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonIcon from '@mui/icons-material/Person';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useAuth } from '../../contexts/AuthContext';

// Navigation items
const pages = [
  { name: 'About', href: '/about', icon: <InfoIcon /> },
  { name: 'AI Marketing', href: '/marketing-ai', icon: <BusinessIcon /> },
  { name: 'Join our Ecosystem', href: '/ecosystem', icon: <GroupIcon /> },
  { name: 'Contact', href: '/contact', icon: <ContactSupportIcon /> },
];

// Styled components
const StyledLogo = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginRight: theme.spacing(2),
  cursor: 'pointer',
}));

const LogoText = styled(Typography)(({ theme }) => ({
  fontFamily: 'Exo',
  fontWeight: 800,
  letterSpacing: '0.05em',
  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  textShadow: '0 0 20px rgba(66, 41, 188, 0.3)',
  marginLeft: theme.spacing(1),
}));

const NavButton = styled(Button)(({ theme }) => ({
  position: 'relative',
  color: theme.palette.text.primary,
  fontWeight: 600,
  fontSize: '0.875rem',
  padding: '8px 16px',
  minWidth: 100,
  height: 40,
  marginLeft: theme.spacing(0.5),
  marginRight: theme.spacing(0.5),
  '&::after': {
    content: '""',
    position: 'absolute',
    width: '0%',
    height: '2px',
    bottom: '4px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: theme.palette.secondary.main,
    transition: 'width 0.3s ease',
  },
  '&:hover': {
    backgroundColor: 'transparent',
    '&::after': {
      width: '70%',
    },
  },
  '&.active': {
    '&::after': {
      width: '70%',
    },
  },
}));

const MobileNavItem = styled(ListItem)(({ theme }) => ({
  padding: theme.spacing(2, 4),
  '&.active': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    borderRight: `4px solid ${theme.palette.primary.main}`,
  },
}));

interface HideOnScrollProps {
  children: React.ReactElement;
}

function HideOnScroll(props: HideOnScrollProps) {
  const { children } = props;
  const trigger = useScrollTrigger();

  return (
    <Slide appear={false} direction="down" in={!trigger}>
      {children}
    </Slide>
  );
}

const Header: React.FC = () => {
  const [anchorElNav, setAnchorElNav] = useState<null | HTMLElement>(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { currentUser, userProfile, logout } = useAuth();
  const router = useRouter();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const handleOpenNavMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      handleCloseUserMenu();
      router.push('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const handleDashboardNavigation = () => {
    handleCloseUserMenu();
    if (userProfile?.userType === 'developer') {
      router.push('/developer/dashboard');
    } else if (userProfile?.userType === 'gamer') {
      router.push('/gamer/dashboard');
    }
  };

  const isActive = (href: string) => router.pathname === href;

  const drawer = (
    <Box sx={{ width: 280, height: '100%', backgroundColor: theme.palette.background.paper }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        <StyledLogo>
          <Image
            src="/assets/images/logo/Logo Art.png"
            alt="Gamestorme Logo"
            width={32}
            height={32}
          />
          <LogoText variant="h6">GAMESTORME</LogoText>
        </StyledLogo>
        <IconButton onClick={handleDrawerToggle} sx={{ color: theme.palette.text.primary }}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Divider />
      <List>
        {pages.map((page) => (
          <Link href={page.href} key={page.name} passHref style={{ textDecoration: 'none', color: 'inherit' }}>
            <MobileNavItem
              className={isActive(page.href) ? 'active' : ''}
              onClick={handleDrawerToggle}
            >
              <ListItemIcon sx={{ color: theme.palette.primary.main }}>
                {page.icon}
              </ListItemIcon>
              <ListItemText
                primary={page.name}
                primaryTypographyProps={{
                  fontWeight: isActive(page.href) ? 600 : 400,
                  fontSize: '1.1rem'
                }}
              />
            </MobileNavItem>
          </Link>
        ))}
      </List>
      <Box sx={{ p: 2, mt: 'auto' }}>
        <Link href="/games" passHref style={{ textDecoration: 'none', width: '100%' }}>
          <Button
            variant="contained"
            fullWidth
            sx={{ mb: 2 }}
            startIcon={<SportsEsportsIcon />}
          >
            Games
          </Button>
        </Link>
        <Divider sx={{ mb: 2 }} />

        {/* Authentication Section */}
        {currentUser ? (
          // Authenticated User Menu
          <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                {userProfile?.displayName?.[0] || currentUser.email?.[0] || 'U'}
              </Avatar>
              <Box>
                <Typography variant="subtitle2" fontWeight="bold">
                  {userProfile?.displayName || 'User'}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {userProfile?.userType === 'developer' ? 'Developer' : 'Gamer'}
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<DashboardIcon />}
                onClick={handleDashboardNavigation}
              >
                Dashboard
              </Button>
              <Button
                variant="outlined"
                color="error"
                fullWidth
                startIcon={<LogoutIcon />}
                onClick={handleLogout}
              >
                Sign Out
              </Button>
            </Box>
          </Box>
        ) : (
          // Login/Signup Buttons for Non-authenticated Users
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Link href="/login" passHref style={{ textDecoration: 'none', width: '50%' }}>
              <Button
                variant="text"
                fullWidth
              >
                Login
              </Button>
            </Link>
            <Link href="/signup" passHref style={{ textDecoration: 'none', width: '50%' }}>
              <Button
                variant="outlined"
                color="primary"
                fullWidth
              >
                Sign Up
              </Button>
            </Link>
          </Box>
        )}
      </Box>
    </Box>
  );

  return (
    <>
      <HideOnScroll>
        <AppBar
          position="fixed"
          sx={{
            boxShadow: scrolled ? 4 : 0,
            backgroundColor: scrolled ? alpha(theme.palette.background.default, 0.9) : 'transparent',
            transition: 'all 0.3s ease',
            borderBottom: scrolled ? `1px solid ${alpha(theme.palette.primary.main, 0.1)}` : 'none',
          }}
        >
          <Container maxWidth="lg">
            <Toolbar disableGutters>
              {/* Desktop Logo */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Link href="/" passHref style={{ textDecoration: 'none' }}>
                  <StyledLogo
                    sx={{
                      mr: 2,
                      display: { xs: 'none', md: 'flex' },
                    }}
                  >
                    <Image
                      src="/assets/images/logo/Logo Art.png"
                      alt="Gamestorme Logo"
                      width={40}
                      height={40}
                    />
                    <LogoText variant="h5" sx={{ fontSize: '1.8rem' }}>
                      GAMESTORME
                    </LogoText>
                  </StyledLogo>
                </Link>
              </motion.div>

              {/* Mobile Menu Icon */}
              <Box sx={{ flexGrow: 0, display: { xs: 'flex', md: 'none' } }}>
                <IconButton
                  size="large"
                  aria-label="menu"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleDrawerToggle}
                  color="inherit"
                >
                  <MenuIcon />
                </IconButton>
              </Box>

              {/* Mobile Logo */}
              <Link href="/" passHref style={{ textDecoration: 'none' }}>
                <StyledLogo
                  sx={{
                    flexGrow: 1,
                    display: { xs: 'flex', md: 'none' },
                  }}
                >
                  <Image
                    src="/assets/images/logo/Logo Art.png"
                    alt="Gamestorme Logo"
                    width={32}
                    height={32}
                  />
                  <LogoText variant="h5" sx={{ fontSize: '1.5rem' }}>
                    GAMESTORME
                  </LogoText>
                </StyledLogo>
              </Link>

              {/* Desktop Navigation */}
              <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' }, justifyContent: 'center' }}>
                {pages.map((page, index) => (
                  <motion.div
                    key={page.name}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                  >
                    <Link href={page.href} passHref style={{ textDecoration: 'none' }}>
                      <NavButton
                        className={isActive(page.href) ? 'active' : ''}
                      >
                        {page.name}
                      </NavButton>
                    </Link>
                  </motion.div>
                ))}
              </Box>

              {/* Games Button and Auth */}
              <Box sx={{ flexGrow: 0, display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 1 }}>
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <Link href="/games" passHref style={{ textDecoration: 'none' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      sx={{
                        minWidth: 100,
                        height: 40,
                        fontSize: '0.875rem',
                        fontWeight: 600,
                      }}
                      startIcon={<SportsEsportsIcon />}
                    >
                      Games
                    </Button>
                  </Link>
                </motion.div>
                {/* Authentication Section */}
                {currentUser ? (
                  // Authenticated User Menu
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <Tooltip title="Account settings">
                      <IconButton
                        onClick={handleOpenUserMenu}
                        sx={{ p: 0, ml: 2 }}
                      >
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {userProfile?.displayName?.[0] || currentUser.email?.[0] || 'U'}
                        </Avatar>
                      </IconButton>
                    </Tooltip>
                    <Menu
                      sx={{ mt: '45px' }}
                      id="menu-appbar"
                      anchorEl={anchorElUser}
                      anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'right',
                      }}
                      keepMounted
                      transformOrigin={{
                        vertical: 'top',
                        horizontal: 'right',
                      }}
                      open={Boolean(anchorElUser)}
                      onClose={handleCloseUserMenu}
                    >
                      <MenuItem onClick={handleCloseUserMenu} disabled>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {userProfile?.displayName || 'User'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {userProfile?.userType === 'developer' ? 'Developer Account' : 'Gamer Account'}
                          </Typography>
                        </Box>
                      </MenuItem>
                      <Divider />
                      <MenuItem onClick={handleDashboardNavigation}>
                        <ListItemIcon>
                          <DashboardIcon fontSize="small" />
                        </ListItemIcon>
                        <Typography textAlign="center">Dashboard</Typography>
                      </MenuItem>
                      <MenuItem onClick={handleLogout}>
                        <ListItemIcon>
                          <LogoutIcon fontSize="small" />
                        </ListItemIcon>
                        <Typography textAlign="center">Sign Out</Typography>
                      </MenuItem>
                    </Menu>
                  </motion.div>
                ) : (
                  // Login/Signup Buttons for Non-authenticated Users
                  <>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.7 }}
                    >
                      <Link href="/login" passHref style={{ textDecoration: 'none' }}>
                        <Button
                          variant="text"
                          color="inherit"
                          sx={{
                            minWidth: 80,
                            height: 40,
                            fontSize: '0.875rem',
                            fontWeight: 600,
                          }}
                        >
                          Login
                        </Button>
                      </Link>
                    </motion.div>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.8 }}
                    >
                      <Link href="/signup" passHref style={{ textDecoration: 'none' }}>
                        <Button
                          variant="outlined"
                          color="primary"
                          sx={{
                            minWidth: 90,
                            height: 40,
                            fontSize: '0.875rem',
                            fontWeight: 600,
                          }}
                        >
                          Sign Up
                        </Button>
                      </Link>
                    </motion.div>
                  </>
                )}
              </Box>
            </Toolbar>
          </Container>
        </AppBar>
      </HideOnScroll>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 280 },
        }}
      >
        {drawer}
      </Drawer>

      {/* Toolbar placeholder to prevent content from hiding behind the AppBar */}
      <Toolbar />
    </>
  );
};

export default Header;
