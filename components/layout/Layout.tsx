import Head from "next/head";
import { Box, CssBaseline } from '@mui/material';
import { ReactNode, useEffect } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Header from './Header';
import Footer from './Footer';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/router';

interface LayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
  ogImage?: string;
  url?: string;
}

const Layout = ({ children, title, description, ogImage, url }: LayoutProps) => {
  // website Url
  const pageUrl = "https://gamestorme.com/";
  // when you share this page on facebook you'll see this image
  const ogImg = "logo.png";
  const router = useRouter();

  // Scroll to top on route change
  useEffect(() => {
    const handleRouteChange = () => {
      window.scrollTo(0, 0);
    };

    router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router.events]);

  // Page transition variants
  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeInOut",
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  };

  return (
    <>
      <Head>
        <title>{title || "Gamestorme - Gaming and Blockchain Ecosystem"}</title>
        <meta
          name="description"
          key="description"
          content={description || "Gamestorme - Revolutionary gaming and blockchain ecosystem designed to transform the way players experience and interact with games."}
        />
        <meta
          property="og:title"
          content={title || "Gamestorme"}
          key="og:title"
        />
        <meta
          property="og:url"
          content={url || pageUrl}
          key="og:url"
        />
        <meta
          property="og:image"
          content={ogImage || ogImg}
          key="og:image"
        />
        <meta
          property="og:description"
          content={description || "Gamestorme - Revolutionary gaming and blockchain ecosystem designed to transform the way players experience and interact with games."}
          key="og:description"
        />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="theme-color" content="#4229BC" />
        <link rel="icon" href="/favicon.ico" />
        <link
          href="https://fonts.googleapis.com/css2?family=Exo:wght@300;400;500;600;700;800&display=swap"
          rel="stylesheet"
        />
      </Head>

      <CssBaseline />

      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundImage: 'url("/main-bg1.png")',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'top',
            zIndex: -1,
          },
        }}
      >
        <Header />

        <AnimatePresence mode="wait">
          <motion.main
            key={router.pathname}
            initial="initial"
            animate="animate"
            exit="exit"
            variants={pageVariants}
            style={{
              flex: 1,
              width: '100%',
              overflowX: 'hidden',
              paddingTop: '1rem',
              paddingBottom: '2rem',
            }}
          >
            {children}
          </motion.main>
        </AnimatePresence>

        <Footer />

        <ToastContainer
          position="bottom-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="dark"
          toastStyle={{
            backgroundColor: 'rgba(29, 20, 41, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(66, 41, 188, 0.2)',
            borderRadius: '12px',
            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)',
          }}
        />
      </Box>
    </>
  );
};

export default Layout;
