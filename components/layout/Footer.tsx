import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Link as MuiLink,
  IconButton,
  Divider,
  Button,
  TextField,
  InputAdornment,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import InstagramIcon from '@mui/icons-material/Instagram';
import YouTubeIcon from '@mui/icons-material/YouTube';
import SendIcon from '@mui/icons-material/Send';
import { FaDiscord } from 'react-icons/fa';
import { motion } from 'framer-motion';

// Styled components
const FooterContainer = styled(Box)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.7),
  backdropFilter: 'blur(10px)',
  borderTop: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'radial-gradient(circle at 20% 25%, rgba(66, 41, 188, 0.15) 0%, transparent 50%)',
    zIndex: -1,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'radial-gradient(circle at 80% 75%, rgba(240, 188, 43, 0.1) 0%, transparent 50%)',
    zIndex: -1,
  },
}));

const FooterHeading = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(3),
  position: 'relative',
  display: 'inline-block',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: 40,
    height: 3,
    backgroundColor: theme.palette.secondary.main,
  },
}));

// Use a span instead of MuiLink to avoid nested <a> tags
const FooterLink = styled('span')(({ theme }) => ({
  color: theme.palette.text.secondary,
  transition: 'all 0.2s ease-in-out',
  display: 'block',
  marginBottom: theme.spacing(1.5),
  position: 'relative',
  paddingLeft: theme.spacing(1.5),
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: 4,
    height: 4,
    borderRadius: '50%',
    backgroundColor: theme.palette.secondary.main,
    opacity: 0,
    transition: 'opacity 0.2s ease-in-out',
  },
  '&:hover': {
    color: theme.palette.text.primary,
    transform: 'translateX(5px)',
    '&::before': {
      opacity: 1,
    },
  },
}));

const SocialButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.text.primary,
  margin: theme.spacing(0, 0.5),
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    transform: 'translateY(-3px)',
    boxShadow: `0 5px 10px ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const NewsletterInput = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    backgroundColor: alpha(theme.palette.background.default, 0.5),
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.primary.light,
    },
  },
}));

const FooterLogo = styled(Typography)(({ theme }) => ({
  fontFamily: 'Exo',
  fontWeight: 800,
  letterSpacing: '0.05em',
  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  textShadow: '0 0 20px rgba(66, 41, 188, 0.3)',
  marginBottom: theme.spacing(2),
}));

const Footer: React.FC = () => {
  const theme = useTheme();
  // Use a fixed year to avoid hydration errors
  const currentYear = 2023;

  const footerLinks = {
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Careers', href: '/careers' },
      { name: 'Join our Ecosystem', href: '/ecosystem' },
      { name: 'pNFT', href: '/pnft' },
    ],
    resources: [
      { name: 'Games', href: '/games' },
      { name: 'Marketing AI', href: '/marketing-ai' },
      { name: 'Community', href: '/community' },
      { name: 'Support', href: '/support' },
    ],
    legal: [
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Contact Us', href: '/contact' },
      { name: 'GDPR', href: '/gdpr' },
    ],
  };

  const socialLinks = [
    { name: 'Facebook', icon: <FacebookIcon />, href: 'https://facebook.com' },
    { name: 'Twitter', icon: <TwitterIcon />, href: 'https://twitter.com' },
    { name: 'Instagram', icon: <InstagramIcon />, href: 'https://instagram.com' },
    { name: 'YouTube', icon: <YouTubeIcon />, href: 'https://youtube.com' },
    { name: 'Discord', icon: <FaDiscord size={20} color="#5865F2" />, href: 'https://discord.com' },
  ];

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log('Newsletter subscription submitted');
  };

  return (
    <FooterContainer>
      <Container maxWidth="lg">
        <Box py={8}>
          <Grid container spacing={4}>
            {/* Logo and About */}
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <FooterLogo variant="h4">GAMESTORME</FooterLogo>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 300 }}>
                  Gamestorme is a revolutionary gaming and blockchain ecosystem designed to transform the way players experience and interact with games.
                </Typography>
                <Box sx={{ display: 'flex', mt: 2 }}>
                  {socialLinks.map((social, index) => (
                    <motion.div
                      key={social.name}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <a
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ textDecoration: 'none' }}
                      >
                        <SocialButton
                          aria-label={social.name}
                        >
                          {social.icon}
                        </SocialButton>
                      </a>
                    </motion.div>
                  ))}
                </Box>
              </motion.div>
            </Grid>

            {/* Quick Links */}
            <Grid item xs={12} sm={4} md={2}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <FooterHeading variant="h6">Company</FooterHeading>
                <Box>
                  {footerLinks.company.map((link, index) => (
                    <Link href={link.href} key={link.name} style={{ textDecoration: 'none' }}>
                      <FooterLink>
                        {link.name}
                      </FooterLink>
                    </Link>
                  ))}
                </Box>
              </motion.div>
            </Grid>

            <Grid item xs={12} sm={4} md={2}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <FooterHeading variant="h6">Resources</FooterHeading>
                <Box>
                  {footerLinks.resources.map((link) => (
                    <Link href={link.href} key={link.name} style={{ textDecoration: 'none' }}>
                      <FooterLink>
                        {link.name}
                      </FooterLink>
                    </Link>
                  ))}
                </Box>
              </motion.div>
            </Grid>

            <Grid item xs={12} sm={4} md={2}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <FooterHeading variant="h6">Legal</FooterHeading>
                <Box>
                  {footerLinks.legal.map((link) => (
                    <Link href={link.href} key={link.name} style={{ textDecoration: 'none' }}>
                      <FooterLink>
                        {link.name}
                      </FooterLink>
                    </Link>
                  ))}
                </Box>
              </motion.div>
            </Grid>

            {/* Newsletter */}
            <Grid item xs={12} md={2}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <FooterHeading variant="h6">Newsletter</FooterHeading>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Subscribe to our newsletter for the latest updates.
                </Typography>
                <Box component="form" onSubmit={handleNewsletterSubmit}>
                  <NewsletterInput
                    fullWidth
                    placeholder="Your email"
                    variant="outlined"
                    size="small"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton edge="end" type="submit" color="primary">
                            <SendIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{ mb: 2 }}
                  />
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ borderColor: alpha(theme.palette.text.primary, 0.1) }} />

        {/* Copyright */}
        <Box py={3} display="flex" flexDirection={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">
            © {currentYear} Gamestorme. All rights reserved.
          </Typography>
          <Box mt={{ xs: 2, sm: 0 }}>
            <Link href="/terms" style={{ textDecoration: 'none' }}>
              <Typography component="span" color="text.secondary" sx={{ mx: 1 }}>
                Terms
              </Typography>
            </Link>
            <Link href="/privacy" style={{ textDecoration: 'none' }}>
              <Typography component="span" color="text.secondary" sx={{ mx: 1 }}>
                Privacy
              </Typography>
            </Link>
            <Link href="/cookies" style={{ textDecoration: 'none' }}>
              <Typography component="span" color="text.secondary" sx={{ mx: 1 }}>
                Cookies
              </Typography>
            </Link>
          </Box>
        </Box>
      </Container>
    </FooterContainer>
  );
};

export default Footer;
