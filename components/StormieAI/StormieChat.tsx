import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Avatar,
  Chip,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';

// Icons
import SendIcon from '@mui/icons-material/Send';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import CampaignIcon from '@mui/icons-material/Campaign';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';

// Styled Components
const ChatContainer = styled(Paper)(({ theme }) => ({
  height: '600px',
  display: 'flex',
  flexDirection: 'column',
  background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${theme.palette.background.default})`,
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
}));

const ChatHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  padding: theme.spacing(1),
  overflowY: 'auto',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: theme.palette.background.default,
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.primary.main,
    borderRadius: '3px',
  },
}));

const MessageBubble = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isUser',
})<{ isUser: boolean }>(({ theme, isUser }) => ({
  display: 'flex',
  justifyContent: isUser ? 'flex-end' : 'flex-start',
  marginBottom: theme.spacing(1),
}));

const MessageContent = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'isUser',
})<{ isUser: boolean }>(({ theme, isUser }) => ({
  padding: theme.spacing(1.5),
  maxWidth: '70%',
  backgroundColor: isUser ? theme.palette.primary.main : theme.palette.background.paper,
  color: isUser ? 'white' : theme.palette.text.primary,
  borderRadius: theme.spacing(2),
  borderBottomRightRadius: isUser ? theme.spacing(0.5) : theme.spacing(2),
  borderBottomLeftRadius: isUser ? theme.spacing(2) : theme.spacing(0.5),
  boxShadow: theme.shadows[2],
}));

const InputContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.divider}`,
  display: 'flex',
  gap: theme.spacing(1),
  alignItems: 'flex-end',
}));

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'analysis' | 'keywords' | 'social' | 'pricing';
  data?: any;
}

interface StormieResponse {
  type: 'analysis' | 'keywords' | 'social' | 'pricing' | 'insights';
  content: string;
  data?: any;
}

const StormieChat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "👋 Hello! I'm Stormie, your AI marketing assistant powered by machine learning technology. I continuously learn from the Gamestorme platform and user interactions to provide increasingly intelligent insights. I specialize in game marketing, development strategies, market analysis, and industry trends. I'm designed to evolve and improve with every interaction, making me smarter and more helpful over time. What can I help you with today?",
      isUser: false,
      timestamp: new Date(),
      type: 'text'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [gameData, setGameData] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      isUser: true,
      timestamp: new Date(),
      type: 'text'
    };

    const currentInput = inputValue;
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Use the new general chat API
      const response = await fetch('/api/stormie/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput,
          context: 'Game marketing consultation',
          gameData: gameData
        }),
      });

      const result = await response.json();

      if (result.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: result.response,
          isUser: false,
          timestamp: new Date(),
          type: result.type || 'text',
          data: result.data
        };

        setMessages(prev => [...prev, aiMessage]);

        // Extract and store game data if mentioned
        const extractedGameData = extractGameDataFromInput(currentInput);
        if (extractedGameData) {
          setGameData(extractedGameData);
        }
      } else {
        throw new Error(result.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I encountered an error. Please try again or rephrase your request.",
        isUser: false,
        timestamp: new Date(),
        type: 'text'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified game data extraction for context
  const extractGameDataFromInput = (input: string) => {
    // Enhanced extraction logic
    const titleMatch = input.match(/(?:game|title|called|named)\s+(?:is\s+)?["']?([^"'.,!?]+)["']?/i);
    const genreMatch = input.match(/(?:genre|type|category)\s+(?:is\s+)?["']?([^"'.,!?]+)["']?/i);
    const platformMatch = input.match(/(?:platform|on)\s+(?:is\s+)?["']?([^"'.,!?]+)["']?/i);

    if (titleMatch || genreMatch || platformMatch) {
      return {
        title: titleMatch?.[1]?.trim(),
        genre: genreMatch?.[1]?.trim(),
        platform: platformMatch?.[1]?.trim(),
        description: input
      };
    }
    return null;
  };

  // Utility functions for better user experience
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };







  // Utility functions for better user experience
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderMessageContent = (message: Message) => {
    if (message.type === 'text' || !message.data) {
      return <Typography variant="body1">{message.content}</Typography>;
    }

    return (
      <Box>
        <Typography variant="body1" sx={{ mb: 2 }}>
          {message.content}
        </Typography>
        {renderDataVisualization(message.type, message.data)}
      </Box>
    );
  };

  const renderDataVisualization = (type: string, data: any) => {
    switch (type) {
      case 'analysis':
        return (
          <Card sx={{ mt: 1 }}>
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    <AnalyticsIcon sx={{ mr: 1 }} />
                    Market Position
                  </Typography>
                  <Typography variant="body2">{data.marketPosition}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    <TrendingUpIcon sx={{ mr: 1 }} />
                    Success Score
                  </Typography>
                  <Typography variant="h4" color="primary">
                    {data.successScore}/10
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        );

      case 'keywords':
        return (
          <Box sx={{ mt: 1 }}>
            <Typography variant="subtitle2" gutterBottom>Primary Keywords:</Typography>
            <Box sx={{ mb: 2 }}>
              {data.primary?.map((keyword: string, index: number) => (
                <Chip
                  key={index}
                  label={keyword}
                  color="primary"
                  size="small"
                  sx={{ mr: 0.5, mb: 0.5 }}
                  onClick={() => copyToClipboard(keyword)}
                />
              ))}
            </Box>
            <Typography variant="subtitle2" gutterBottom>Secondary Keywords:</Typography>
            <Box>
              {data.secondary?.map((keyword: string, index: number) => (
                <Chip
                  key={index}
                  label={keyword}
                  variant="outlined"
                  size="small"
                  sx={{ mr: 0.5, mb: 0.5 }}
                  onClick={() => copyToClipboard(keyword)}
                />
              ))}
            </Box>
          </Box>
        );

      case 'social':
        return (
          <Card sx={{ mt: 1 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <CampaignIcon sx={{ mr: 1 }} />
                Social Media Content
              </Typography>
              <Paper sx={{ p: 2, bgcolor: 'background.default', mb: 2 }}>
                <Typography variant="body1">{data.content}</Typography>
                <IconButton
                  size="small"
                  onClick={() => copyToClipboard(data.content)}
                  sx={{ mt: 1 }}
                >
                  <ContentCopyIcon />
                </IconButton>
              </Paper>
              <Typography variant="subtitle2">Hashtags:</Typography>
              <Box>
                {data.hashtags?.map((tag: string, index: number) => (
                  <Chip
                    key={index}
                    label={tag}
                    size="small"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        );

      case 'pricing':
        return (
          <Card sx={{ mt: 1 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <AttachMoneyIcon sx={{ mr: 1 }} />
                Pricing Strategy
              </Typography>
              <Typography variant="body1">{data.strategy}</Typography>
              <Typography variant="h6" color="primary" sx={{ mt: 2 }}>
                Recommended Price: {data.recommendedPrice}
              </Typography>
            </CardContent>
          </Card>
        );

      default:
        return <Typography variant="body2">{JSON.stringify(data)}</Typography>;
    }
  };

  return (
    <ChatContainer elevation={3}>
      <ChatHeader>
        <Avatar sx={{ bgcolor: '#ffffff', color: 'primary.main' }}>
          <SmartToyIcon />
        </Avatar>
        <Box>
          <Typography variant="h6" fontWeight="bold">
            Stormie AI
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.9 }}>
            Your AI Marketing Assistant
          </Typography>
        </Box>
      </ChatHeader>

      <MessagesContainer>
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <MessageBubble isUser={message.isUser}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  {!message.isUser && (
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                      <SmartToyIcon sx={{ fontSize: 18 }} />
                    </Avatar>
                  )}
                  <MessageContent isUser={message.isUser}>
                    {renderMessageContent(message)}
                  </MessageContent>
                  {message.isUser && (
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                      <PersonIcon sx={{ fontSize: 18 }} />
                    </Avatar>
                  )}
                </Box>
              </MessageBubble>
            </motion.div>
          ))}
        </AnimatePresence>

        {isLoading && (
          <MessageBubble isUser={false}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                <SmartToyIcon sx={{ fontSize: 18 }} />
              </Avatar>
              <MessageContent isUser={false}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={16} />
                  <Typography variant="body2">Stormie is thinking...</Typography>
                </Box>
              </MessageContent>
            </Box>
          </MessageBubble>
        )}

        <div ref={messagesEndRef} />
      </MessagesContainer>

      <InputContainer>
        <TextField
          fullWidth
          multiline
          maxRows={3}
          placeholder="Ask Stormie anything about game marketing, development, or industry insights..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isLoading}
          variant="outlined"
          size="small"
        />
        <Button
          variant="contained"
          endIcon={<SendIcon />}
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || isLoading}
          sx={{ minWidth: 'auto', px: 3 }}
        >
          Send
        </Button>
      </InputContainer>
    </ChatContainer>
  );
};

export default StormieChat;
