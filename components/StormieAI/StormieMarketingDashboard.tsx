import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  Chip,
  alpha,
  useTheme,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Avatar,
  Container,
  LinearProgress,
  Divider,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  SmartToy as SmartToyIcon,
  Chat as ChatIcon,
  Analytics as AnalyticsIcon,
  Campaign as CampaignIcon,
  AttachMoney as AttachMoneyIcon,
  TrendingUp as TrendingUpIcon,
  Gamepad as GamepadIcon,
  Add as AddIcon,
  AutoAwesome as AutoAwesomeIcon,
  Insights as InsightsIcon,
  ContentCopy as ContentCopyIcon,
  Download as DownloadIcon,
  History as HistoryIcon,
  Bookmark as BookmarkIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

// Components
import StormieChat from './StormieChat';

// Styled Components
const DashboardContainer = styled(Container)(({ theme }) => ({
  padding: theme.spacing(3),
  maxWidth: '1400px',
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.spacing(2),
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.default, 0.9)})`,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const StormieHeader = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  color: 'white',
  borderRadius: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

interface GameProject {
  id: string;
  title: string;
  genre: string;
  description: string;
  platform: string;
  targetAudience: string;
  status: 'draft' | 'analyzing' | 'completed';
}

const StormieMarketingDashboard: React.FC = () => {
  const theme = useTheme();
  const { logout } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [gameProjects, setGameProjects] = useState<GameProject[]>([
    {
      id: '1',
      title: 'Cosmic Odyssey',
      genre: 'Space Exploration',
      description: 'An epic space adventure game with stunning visuals',
      platform: 'PC, Console',
      targetAudience: 'Core gamers 18-35',
      status: 'completed'
    }
  ]);
  const [selectedProject, setSelectedProject] = useState<GameProject | null>(null);
  const [newProjectDialog, setNewProjectDialog] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);

  const aiFeatures = [
    {
      title: 'Game Analysis',
      description: 'AI-powered market analysis and positioning insights',
      icon: <AnalyticsIcon />,
      color: theme.palette.primary.main,
      action: () => handleFeatureClick('analysis'),
      stats: '15 analyses completed'
    },
    {
      title: 'ASO Keywords',
      description: 'Generate optimized app store keywords',
      icon: <TrendingUpIcon />,
      color: theme.palette.success.main,
      action: () => handleFeatureClick('keywords'),
      stats: '200+ keywords generated'
    },
    {
      title: 'Social Content',
      description: 'Create engaging social media posts and campaigns',
      icon: <CampaignIcon />,
      color: theme.palette.info.main,
      action: () => handleFeatureClick('social'),
      stats: '50+ posts created'
    },
    {
      title: 'Pricing Strategy',
      description: 'Optimize pricing with AI recommendations',
      icon: <AttachMoneyIcon />,
      color: theme.palette.warning.main,
      action: () => handleFeatureClick('pricing'),
      stats: '8 strategies optimized'
    },
    {
      title: 'Market Insights',
      description: 'Real-time market trends and predictions',
      icon: <InsightsIcon />,
      color: theme.palette.secondary.main,
      action: () => handleFeatureClick('insights'),
      stats: 'Live market data'
    },
    {
      title: 'Stormie Chat',
      description: 'Interactive AI assistant for all your marketing needs',
      icon: <ChatIcon />,
      color: theme.palette.primary.main,
      action: () => setChatOpen(true),
      stats: 'Always available'
    }
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleFeatureClick = (feature: string) => {
    // Open specific feature or redirect to chat with context
    setChatOpen(true);
  };

  const handleNewProject = () => {
    setNewProjectDialog(true);
  };

  const handleProjectSelect = (project: GameProject) => {
    setSelectedProject(project);
  };

  return (
    <DashboardContainer>
      {/* Stormie Header */}
      <StormieHeader elevation={0}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar sx={{ bgcolor: 'white', color: 'primary.main', mr: 2, width: 56, height: 56 }}>
            <SmartToyIcon sx={{ fontSize: 32 }} />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              Stormie AI Marketing Platform
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              Empowering Game Developers with AI-Driven Marketing Intelligence
            </Typography>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            sx={{ bgcolor: 'white', color: 'primary.main', '&:hover': { bgcolor: alpha('white', 0.9) } }}
            startIcon={<AddIcon />}
            onClick={handleNewProject}
          >
            New Project
          </Button>
          <IconButton sx={{ color: 'white' }}>
            <SettingsIcon />
          </IconButton>
          <Button
            variant="outlined"
            startIcon={<LogoutIcon />}
            onClick={async () => {
              try {
                await logout();
                window.location.href = '/login';
              } catch (error) {
                console.error('Logout failed:', error);
              }
            }}
            sx={{ color: 'white', borderColor: 'white' }}
          >
            Logout
          </Button>
        </Box>
      </StormieHeader>

      {/* Navigation Tabs */}
      <Paper sx={{ mb: 3, borderRadius: 2 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            '& .MuiTabs-indicator': {
              height: 3,
              borderRadius: 1.5,
            },
          }}
        >
          <Tab icon={<AutoAwesomeIcon />} label="AI Features" />
          <Tab icon={<GamepadIcon />} label="Game Projects" />
          <Tab icon={<HistoryIcon />} label="Analytics" />
          <Tab icon={<BookmarkIcon />} label="Saved Content" />
        </Tabs>
      </Paper>

      {/* AI Features Tab */}
      {activeTab === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            AI Marketing Tools
          </Typography>
          <Grid container spacing={3}>
            {aiFeatures.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <FeatureCard onClick={feature.action}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: alpha(feature.color, 0.1),
                          color: feature.color,
                          mr: 2,
                        }}
                      >
                        {feature.icon}
                      </Avatar>
                      <Typography variant="h6" fontWeight="bold">
                        {feature.title}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {feature.description}
                    </Typography>
                    <Chip
                      label={feature.stats}
                      size="small"
                      sx={{
                        bgcolor: alpha(feature.color, 0.1),
                        color: feature.color,
                      }}
                    />
                  </CardContent>
                </FeatureCard>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      )}

      {/* Game Projects Tab */}
      {activeTab === 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" fontWeight="bold">
              Game Projects
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleNewProject}
            >
              Add Game Project
            </Button>
          </Box>

          <Grid container spacing={3}>
            {gameProjects.map((project) => (
              <Grid item xs={12} md={6} lg={4} key={project.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: theme.shadows[8],
                    },
                  }}
                  onClick={() => handleProjectSelect(project)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {project.title}
                      </Typography>
                      <Chip
                        label={project.status}
                        size="small"
                        color={project.status === 'completed' ? 'success' : 'warning'}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Genre:</strong> {project.genre}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Platform:</strong> {project.platform}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {project.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Target: {project.targetAudience}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      )}

      {/* Analytics Tab */}
      {activeTab === 2 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            Marketing Analytics
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    AI Usage Statistics
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Keywords Generated
                    </Typography>
                    <LinearProgress variant="determinate" value={75} sx={{ mt: 1 }} />
                    <Typography variant="caption">200+ keywords</Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Social Posts Created
                    </Typography>
                    <LinearProgress variant="determinate" value={60} sx={{ mt: 1 }} />
                    <Typography variant="caption">50+ posts</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Market Analyses
                    </Typography>
                    <LinearProgress variant="determinate" value={45} sx={{ mt: 1 }} />
                    <Typography variant="caption">15 analyses</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Performance Insights
                  </Typography>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    Your ASO keywords have improved search ranking by 35%
                  </Alert>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Social media engagement increased by 120% this month
                  </Alert>
                  <Alert severity="warning">
                    Consider updating pricing strategy for better conversion
                  </Alert>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Saved Content Tab */}
      {activeTab === 3 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            Saved Marketing Content
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Your saved marketing content will appear here. Use Stormie Chat to generate and save content.
          </Typography>
        </motion.div>
      )}

      {/* Stormie Chat Dialog */}
      <Dialog
        open={chatOpen}
        onClose={() => setChatOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            height: '80vh',
            borderRadius: 3,
          }
        }}
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', pb: 1 }}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <SmartToyIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Stormie AI Assistant
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Your AI marketing companion
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <StormieChat />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChatOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* New Project Dialog */}
      <Dialog open={newProjectDialog} onClose={() => setNewProjectDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Game Project</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Game Title"
            margin="normal"
            variant="outlined"
          />
          <TextField
            fullWidth
            label="Genre"
            margin="normal"
            variant="outlined"
          />
          <TextField
            fullWidth
            label="Description"
            margin="normal"
            variant="outlined"
            multiline
            rows={3}
          />
          <TextField
            fullWidth
            label="Platform"
            margin="normal"
            variant="outlined"
          />
          <TextField
            fullWidth
            label="Target Audience"
            margin="normal"
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewProjectDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setNewProjectDialog(false)}>
            Add Project
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardContainer>
  );
};

export default StormieMarketingDashboard;
