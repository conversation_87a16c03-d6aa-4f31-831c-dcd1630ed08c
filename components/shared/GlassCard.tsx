import React from 'react';
import { Card, CardProps, useTheme, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';

interface GlassCardProps extends Omit<CardProps, 'component'> {
  blur?: number;
  opacity?: number;
  borderColor?: string;
  glowColor?: string;
  hoverEffect?: 'lift' | 'scale' | 'glow' | 'none';
  animationDelay?: number;
  children: React.ReactNode;
}

const StyledGlassCard = styled(motion.div)<{
  blur: number;
  opacity: number;
  borderColor: string;
  glowColor: string;
  hoverEffect: string;
}>(({ theme, blur, opacity, borderColor, glowColor, hoverEffect }) => ({
  position: 'relative',
  borderRadius: 24,
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.glass, opacity)} 0%, ${alpha(theme.palette.background.paper, opacity * 0.5)} 100%)`,
  backdropFilter: `blur(${blur}px)`,
  border: `1px solid ${borderColor}`,
  boxShadow: `0 8px 32px ${alpha('#000', 0.3)}, inset 0 1px 0 ${alpha('#fff', 0.1)}`,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  overflow: 'hidden',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '1px',
    background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.light, 0.6)} 50%, transparent 100%)`,
    zIndex: 1,
  },
  
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '1px',
    height: '100%',
    background: `linear-gradient(180deg, transparent 0%, ${alpha(theme.palette.primary.light, 0.4)} 50%, transparent 100%)`,
    zIndex: 1,
  },

  ...(hoverEffect === 'lift' && {
    '&:hover': {
      transform: 'translateY(-8px) scale(1.02)',
      background: `linear-gradient(135deg, ${alpha(theme.palette.background.glassHover, opacity * 1.2)} 0%, ${alpha(theme.palette.background.paper, opacity * 0.8)} 100%)`,
      borderColor: alpha(theme.palette.primary.main, 0.4),
      boxShadow: `0 20px 60px ${alpha('#000', 0.4)}, 0 0 40px ${alpha(glowColor, 0.3)}, inset 0 1px 0 ${alpha('#fff', 0.2)}`,
      '&::before': {
        background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.light, 0.8)} 50%, transparent 100%)`,
      },
    },
  }),

  ...(hoverEffect === 'scale' && {
    '&:hover': {
      transform: 'scale(1.05)',
      background: `linear-gradient(135deg, ${alpha(theme.palette.background.glassHover, opacity * 1.2)} 0%, ${alpha(theme.palette.background.paper, opacity * 0.8)} 100%)`,
      borderColor: alpha(theme.palette.primary.main, 0.4),
      boxShadow: `0 15px 50px ${alpha('#000', 0.4)}, 0 0 30px ${alpha(glowColor, 0.4)}`,
    },
  }),

  ...(hoverEffect === 'glow' && {
    '&:hover': {
      borderColor: alpha(theme.palette.primary.main, 0.6),
      boxShadow: `0 12px 40px ${alpha('#000', 0.3)}, 0 0 50px ${alpha(glowColor, 0.5)}, inset 0 1px 0 ${alpha('#fff', 0.15)}`,
    },
  }),
}));

const GlassCard: React.FC<GlassCardProps> = ({
  blur = 20,
  opacity = 0.8,
  borderColor,
  glowColor,
  hoverEffect = 'lift',
  animationDelay = 0,
  children,
  sx,
  ...props
}) => {
  const theme = useTheme();
  
  const finalBorderColor = borderColor || alpha(theme.palette.primary.main, 0.15);
  const finalGlowColor = glowColor || theme.palette.primary.main;

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1],
        delay: animationDelay,
      },
    },
  };

  return (
    <StyledGlassCard
      blur={blur}
      opacity={opacity}
      borderColor={finalBorderColor}
      glowColor={finalGlowColor}
      hoverEffect={hoverEffect}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      sx={sx}
      {...props}
    >
      {children}
    </StyledGlassCard>
  );
};

export default GlassCard;
