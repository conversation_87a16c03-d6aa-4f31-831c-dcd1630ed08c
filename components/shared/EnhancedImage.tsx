import React from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import Image from 'next/image';

interface EnhancedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  borderColor?: string;
  glowColor?: string;
  borderRadius?: number;
  hoverEffect?: 'scale' | 'glow' | 'rotate' | 'lift' | 'none';
  className?: string;
  priority?: boolean;
  fill?: boolean;
  style?: React.CSSProperties;
}

const ImageContainer = styled(motion.div)<{
  borderColor: string;
  glowColor: string;
  borderRadius: number;
}>(({ theme, borderColor, glowColor, borderRadius }) => ({
  position: 'relative',
  overflow: 'hidden',
  borderRadius: `${borderRadius}px`,
  border: `2px solid ${borderColor}`,
  boxShadow: `0 8px 25px ${alpha('#000', 0.3)}, 0 0 15px ${alpha(glowColor, 0.3)}`,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `linear-gradient(135deg, ${alpha('#fff', 0.1)} 0%, transparent 50%, ${alpha('#fff', 0.05)} 100%)`,
    zIndex: 1,
    pointerEvents: 'none',
  },

  '&.hover-scale:hover': {
    transform: 'scale(1.05)',
    borderColor: alpha(glowColor, 0.8),
    boxShadow: `0 15px 40px ${alpha('#000', 0.4)}, 0 0 30px ${alpha(glowColor, 0.6)}`,
  },

  '&.hover-glow:hover': {
    borderColor: alpha(glowColor, 0.9),
    boxShadow: `0 12px 35px ${alpha('#000', 0.4)}, 0 0 40px ${alpha(glowColor, 0.7)}`,
  },

  '&.hover-rotate:hover': {
    transform: 'rotate(3deg) scale(1.02)',
    borderColor: alpha(glowColor, 0.8),
    boxShadow: `0 15px 40px ${alpha('#000', 0.4)}, 0 0 25px ${alpha(glowColor, 0.5)}`,
  },

  '&.hover-lift:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    borderColor: alpha(glowColor, 0.8),
    boxShadow: `0 20px 50px ${alpha('#000', 0.4)}, 0 0 30px ${alpha(glowColor, 0.6)}`,
  },
}));

const EnhancedImage: React.FC<EnhancedImageProps> = ({
  src,
  alt,
  width,
  height,
  borderColor,
  glowColor,
  borderRadius = 16,
  hoverEffect = 'scale',
  className = '',
  priority = false,
  fill = false,
  style = {},
}) => {
  const theme = useTheme();
  
  const finalBorderColor = borderColor || alpha(theme.palette.primary.main, 0.4);
  const finalGlowColor = glowColor || theme.palette.primary.main;
  const hoverClass = hoverEffect !== 'none' ? `hover-${hoverEffect}` : '';

  const imageVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  return (
    <ImageContainer
      borderColor={finalBorderColor}
      glowColor={finalGlowColor}
      borderRadius={borderRadius}
      className={`${hoverClass} ${className}`}
      variants={imageVariants}
      initial="hidden"
      animate="visible"
      style={style}
    >
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        style={{
          objectFit: 'cover',
          borderRadius: `${borderRadius - 2}px`,
          display: 'block',
        }}
      />
    </ImageContainer>
  );
};

export default EnhancedImage;
