import React, { ReactNode } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';

interface EnhancedStatsCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: ReactNode;
  color?: string;
  subtitle?: string;
  animationDelay?: number;
  onClick?: () => void;
}

const StatsCard = styled(motion.div)<{ clickable: boolean }>(({ theme, clickable }) => ({
  height: '100%',
  cursor: clickable ? 'pointer' : 'default',
}));

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: 20,
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 8px 32px ${alpha('#000', 0.1)}, inset 0 1px 0 ${alpha('#fff', 0.1)}`,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '2px',
    background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.main, 0.6)} 50%, transparent 100%)`,
    zIndex: 1,
  },
  
  '&:hover': {
    transform: 'translateY(-4px) scale(1.02)',
    borderColor: alpha(theme.palette.primary.main, 0.2),
    boxShadow: `0 16px 48px ${alpha('#000', 0.15)}, 0 0 24px ${alpha(theme.palette.primary.main, 0.1)}, inset 0 1px 0 ${alpha('#fff', 0.15)}`,
    
    '& .stats-icon': {
      transform: 'scale(1.1) rotate(5deg)',
    },
    
    '& .stats-value': {
      transform: 'scale(1.05)',
    },
  },
}));

const IconContainer = styled(Box)<{ iconColor: string }>(({ theme, iconColor }) => ({
  width: 64,
  height: 64,
  borderRadius: 18,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${alpha(iconColor, 0.15)} 0%, ${alpha(iconColor, 0.08)} 100%)`,
  border: `2px solid ${alpha(iconColor, 0.2)}`,
  boxShadow: `0 8px 20px ${alpha(iconColor, 0.2)}, inset 0 1px 0 ${alpha('#fff', 0.1)}`,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `radial-gradient(circle at 30% 30%, ${alpha(iconColor, 0.3)} 0%, transparent 70%)`,
    borderRadius: 16,
    zIndex: 0,
  },
  
  '& svg': {
    fontSize: 28,
    color: iconColor,
    zIndex: 1,
    position: 'relative',
    filter: `drop-shadow(0 2px 4px ${alpha(iconColor, 0.3)})`,
  },
}));

const ValueContainer = styled(Box)({
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
});

const ChangeChip = styled(Chip)<{ changeType: 'positive' | 'negative' | 'neutral' }>(({ theme, changeType }) => {
  const getColor = () => {
    switch (changeType) {
      case 'positive':
        return theme.palette.success.main;
      case 'negative':
        return theme.palette.error.main;
      default:
        return theme.palette.info.main;
    }
  };

  const color = getColor();
  
  return {
    height: 24,
    fontSize: '0.75rem',
    fontWeight: 600,
    background: `linear-gradient(135deg, ${alpha(color, 0.15)} 0%, ${alpha(color, 0.08)} 100%)`,
    color: color,
    border: `1px solid ${alpha(color, 0.2)}`,
    '& .MuiChip-icon': {
      color: color,
      fontSize: 16,
    },
  };
});

const EnhancedStatsCard: React.FC<EnhancedStatsCardProps> = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  color,
  subtitle,
  animationDelay = 0,
  onClick,
}) => {
  const theme = useTheme();
  const finalColor = color || theme.palette.primary.main;

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1],
        delay: animationDelay,
      },
    },
  };

  const getChangeIcon = () => {
    if (!change) return null;
    
    if (changeType === 'positive') {
      return <TrendingUpIcon />;
    } else if (changeType === 'negative') {
      return <TrendingDownIcon />;
    }
    return null;
  };

  return (
    <StatsCard
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={onClick ? { scale: 1.02 } : {}}
      whileTap={onClick ? { scale: 0.98 } : {}}
      clickable={!!onClick}
      onClick={onClick}
    >
      <StyledCard>
        <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
            <IconContainer iconColor={finalColor} className="stats-icon">
              {icon}
            </IconContainer>
            
            {change && (
              <ChangeChip
                label={change}
                size="small"
                changeType={changeType}
                icon={getChangeIcon()}
              />
            )}
          </Box>
          
          <Box sx={{ flex: 1 }}>
            <Typography 
              variant="body2" 
              color="text.secondary" 
              sx={{ mb: 1, fontWeight: 500 }}
            >
              {title}
            </Typography>
            
            <ValueContainer className="stats-value">
              <Typography 
                variant="h3" 
                fontWeight="bold" 
                sx={{ 
                  color: finalColor,
                  lineHeight: 1.2,
                  mb: subtitle ? 1 : 0,
                }}
              >
                {value}
              </Typography>
            </ValueContainer>
            
            {subtitle && (
              <Typography 
                variant="caption" 
                color="text.secondary"
                sx={{ opacity: 0.8 }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        </CardContent>
      </StyledCard>
    </StatsCard>
  );
};

export default EnhancedStatsCard;
