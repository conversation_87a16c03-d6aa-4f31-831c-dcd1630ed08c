import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  LinearProgress,
  Alert,
  Chip,
  Grid,
  Card,
  CardContent,
  Avatar,
  useTheme,
  alpha,
} from '@mui/material';
import { motion } from 'framer-motion';

// Icons
import DownloadIcon from '@mui/icons-material/Download';
import WindowsIcon from '@mui/icons-material/Computer';
import AppleIcon from '@mui/icons-material/Apple';
import LinuxIcon from '@mui/icons-material/DesktopWindows';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';

// Firebase
import { getStorage, ref, getDownloadURL, getMetadata } from 'firebase/storage';
import { app } from '../lib/firebase';

interface DownloadLauncherProps {
  open: boolean;
  onClose: () => void;
}

interface LauncherInfo {
  platform: string;
  filename: string;
  size: string;
  version: string;
  icon: React.ReactNode;
  color: string;
  description: string;
}

const DownloadLauncher: React.FC<DownloadLauncherProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const [downloading, setDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [downloadError, setDownloadError] = useState<string | null>(null);
  const [downloadSuccess, setDownloadSuccess] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);

  const storage = getStorage(app);

  // Detect user's platform
  const detectPlatform = () => {
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent;
      if (userAgent.includes('Windows')) return 'windows';
      if (userAgent.includes('Mac')) return 'mac';
      if (userAgent.includes('Linux')) return 'linux';
    }
    return 'windows'; // Default to Windows
  };

  const launcherVersions: LauncherInfo[] = [
    {
      platform: 'windows',
      filename: 'Gamestorme-Launcher-Setup-2.0.0.exe',
      size: '85 MB',
      version: '2.0.0',
      icon: <WindowsIcon />,
      color: '#0078d4',
      description: 'Windows 10/11 (64-bit)',
    },
    {
      platform: 'mac',
      filename: 'Gamestorme-Launcher-2.0.0.dmg',
      size: '92 MB',
      version: '2.0.0',
      icon: <AppleIcon />,
      color: '#000000',
      description: 'macOS 10.15+ (Intel & Apple Silicon)',
    },
    {
      platform: 'linux',
      filename: 'Gamestorme-Launcher-2.0.0.AppImage',
      size: '88 MB',
      version: '2.0.0',
      icon: <LinuxIcon />,
      color: '#ff6600',
      description: 'Linux (AppImage)',
    },
  ];

  useEffect(() => {
    if (open) {
      setSelectedPlatform(detectPlatform());
      setDownloadError(null);
      setDownloadSuccess(false);
      setDownloading(false);
      setDownloadProgress(0);
    }
  }, [open]);

  const handleDownload = async (launcher: LauncherInfo) => {
    setDownloading(true);
    setDownloadError(null);
    setDownloadProgress(0);

    try {
      // Try to download real executable from Firebase first
      const realDownloadUrls = {
        'windows': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FGamestorme%20Launcher%20Setup%202.0.0.exe?alt=media',
        'mac': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FGamestorme%20Launcher-2.0.0.dmg?alt=media',
        'linux': 'https://firebasestorage.googleapis.com/v0/b/gamestorme-faf42.appspot.com/o/launchers%2FGamestorme%20Launcher-2.0.0.AppImage?alt=media'
      };

      const realDownloadUrl = realDownloadUrls[launcher.platform];

      // Check if real executable exists
      if (realDownloadUrl) {
        try {
          const response = await fetch(realDownloadUrl, { method: 'HEAD' });
          if (response.ok) {
            // Real executable exists, download it
            setDownloadProgress(100);
            window.open(realDownloadUrl, '_blank');
            setDownloadSuccess(true);
            setDownloading(false);
            return;
          }
        } catch (error) {
          console.log('Real executable not found, falling back to demo');
        }
      }

      // Fallback to demo download
      const progressInterval = setInterval(() => {
        setDownloadProgress((prev) => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + Math.random() * 10 + 5;
        });
      }, 150);

      // Create a comprehensive demo launcher file
      setTimeout(async () => {
        clearInterval(progressInterval);
        setDownloadProgress(100);

        // Create a detailed demo launcher script/batch file
        let demoContent = '';
        let fileExtension = '';

        if (launcher.platform === 'windows') {
          fileExtension = '.bat';
          demoContent = `@echo off
title Gamestorme Launcher ${launcher.version}
color 0A
echo.
echo ========================================
echo    GAMESTORME LAUNCHER ${launcher.version}
echo ========================================
echo.
echo Welcome to Gamestorme Desktop Launcher!
echo.
echo This is a DEMO version of the launcher.
echo In production, this would be a full
echo Electron application with:
echo.
echo ✓ Login and Signup functionality
echo ✓ Developer Dashboard access
echo ✓ Gamer Dashboard with game library
echo ✓ Real-time notifications
echo ✓ Offline capabilities
echo ✓ Auto-update system
echo ✓ Native OS integration
echo.
echo Platform: Windows ${launcher.description}
echo Version: ${launcher.version}
echo Size: ${launcher.size}
echo Build Date: ${new Date().toLocaleDateString()}
echo.
echo To build the real launcher:
echo 1. Clone: git clone https://github.com/joelgriiyo/gamestorme.git
echo 2. Setup: cp electron-package.json package.json
echo 3. Install: npm install
echo 4. Build: npm run dist-win
echo.
echo Thank you for using Gamestorme!
echo.
echo Opening Gamestorme website...
start https://gamestorme-9c0b68273ab5.herokuapp.com
echo.
pause`;
        } else if (launcher.platform === 'mac') {
          fileExtension = '.command';
          demoContent = `#!/bin/bash
clear
echo "========================================"
echo "   GAMESTORME LAUNCHER ${launcher.version}"
echo "========================================"
echo ""
echo "Welcome to Gamestorme Desktop Launcher!"
echo ""
echo "This is a DEMO version of the launcher."
echo "In production, this would be a full"
echo "Electron application with:"
echo ""
echo "✓ Login and Signup functionality"
echo "✓ Developer Dashboard access"
echo "✓ Gamer Dashboard with game library"
echo "✓ Real-time notifications"
echo "✓ Offline capabilities"
echo "✓ Auto-update system"
echo "✓ Native macOS integration"
echo ""
echo "Platform: macOS ${launcher.description}"
echo "Version: ${launcher.version}"
echo "Size: ${launcher.size}"
echo "Build Date: $(date)"
echo ""
echo "To build the real launcher:"
echo "1. Clone: git clone https://github.com/joelgriiyo/gamestorme.git"
echo "2. Setup: cp electron-package.json package.json"
echo "3. Install: npm install"
echo "4. Build: npm run dist-mac"
echo ""
echo "Thank you for using Gamestorme!"
echo ""
echo "Opening Gamestorme website..."
open https://gamestorme-9c0b68273ab5.herokuapp.com
echo ""
read -p "Press Enter to continue..."`;
        } else {
          fileExtension = '.sh';
          demoContent = `#!/bin/bash
clear
echo "========================================"
echo "   GAMESTORME LAUNCHER ${launcher.version}"
echo "========================================"
echo ""
echo "Welcome to Gamestorme Desktop Launcher!"
echo ""
echo "This is a DEMO version of the launcher."
echo "In production, this would be a full"
echo "Electron application with:"
echo ""
echo "✓ Login and Signup functionality"
echo "✓ Developer Dashboard access"
echo "✓ Gamer Dashboard with game library"
echo "✓ Real-time notifications"
echo "✓ Offline capabilities"
echo "✓ Auto-update system"
echo "✓ Native Linux integration"
echo ""
echo "Platform: Linux ${launcher.description}"
echo "Version: ${launcher.version}"
echo "Size: ${launcher.size}"
echo "Build Date: $(date)"
echo ""
echo "To build the real launcher:"
echo "1. Clone: git clone https://github.com/joelgriiyo/gamestorme.git"
echo "2. Setup: cp electron-package.json package.json"
echo "3. Install: npm install"
echo "4. Build: npm run dist-linux"
echo ""
echo "Thank you for using Gamestorme!"
echo ""
echo "Opening Gamestorme website..."
xdg-open https://gamestorme-9c0b68273ab5.herokuapp.com
echo ""
read -p "Press Enter to continue..."`;
        }

        const blob = new Blob([demoContent], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);

        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = `Gamestorme-Launcher-Demo-${launcher.version}${fileExtension}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        setDownloadSuccess(true);
        setDownloading(false);
      }, 2500);

    } catch (error) {
      console.error('Download error:', error);
      setDownloadError('Failed to download the launcher. Please try again.');
      setDownloading(false);
    }
  };

  const handleClose = () => {
    if (!downloading) {
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.9)})`,
          backdropFilter: 'blur(10px)',
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2 }}>
            <DownloadIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Download Gamestorme Launcher
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Get the desktop application for the best gaming experience
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {downloadError && (
          <Alert severity="error" sx={{ mb: 3 }} icon={<ErrorIcon />}>
            {downloadError}
          </Alert>
        )}

        {downloadSuccess && (
          <Alert severity="success" sx={{ mb: 3 }} icon={<CheckCircleIcon />}>
            Demo launcher downloaded successfully! Check your Downloads folder and run the script to see the launcher concept.
            For the full Electron application, follow the build instructions in the downloaded file.
          </Alert>
        )}

        {downloading && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" gutterBottom>
              Downloading Gamestorme Launcher...
            </Typography>
            <LinearProgress
              variant="determinate"
              value={downloadProgress}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {Math.round(downloadProgress)}% complete
            </Typography>
          </Box>
        )}

        <Alert severity="info" sx={{ mb: 3 }} icon={<InfoIcon />}>
          <Typography variant="body2">
            <strong>Demo Download:</strong> This downloads a demo script that showcases the launcher concept.
            The full Electron application with enhanced performance, offline access, desktop notifications,
            and seamless OS integration is available for developers to build from the source code.
          </Typography>
        </Alert>

        <Typography variant="h6" fontWeight="bold" gutterBottom>
          Choose Your Platform
        </Typography>

        <Grid container spacing={2}>
          {launcherVersions.map((launcher, index) => (
            <Grid item xs={12} key={launcher.platform}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    border: selectedPlatform === launcher.platform
                      ? `2px solid ${launcher.color}`
                      : `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 8px 25px ${alpha(launcher.color, 0.15)}`,
                    }
                  }}
                  onClick={() => setSelectedPlatform(launcher.platform)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: alpha(launcher.color, 0.1), color: launcher.color, mr: 2 }}>
                          {launcher.icon}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {launcher.platform.charAt(0).toUpperCase() + launcher.platform.slice(1)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {launcher.description}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip label={`v${launcher.version}`} size="small" color="primary" sx={{ mb: 1 }} />
                        <Typography variant="caption" color="text.secondary" display="block">
                          {launcher.size}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 3, p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Demo Requirements:</strong> Any system that can run scripts (.bat for Windows, .command for macOS, .sh for Linux).
            <br />
            <strong>Full Launcher Requirements:</strong> 4GB RAM, 500MB free disk space, Internet connection for setup and updates.
            <br />
            <strong>Build Requirements:</strong> Node.js 20.x, npm, Git (for building the full Electron application).
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={handleClose} disabled={downloading}>
          Cancel
        </Button>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={() => {
            const launcher = launcherVersions.find(l => l.platform === selectedPlatform);
            if (launcher) handleDownload(launcher);
          }}
          disabled={!selectedPlatform || downloading}
          sx={{
            background: selectedPlatform
              ? `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              : undefined,
          }}
        >
          {downloading ? 'Downloading...' : 'Download Launcher'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DownloadLauncher;
