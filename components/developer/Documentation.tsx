import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  alpha,
  useTheme,
  InputAdornment,
} from '@mui/material';
import {
  Article as ArticleIcon,
  Code as CodeIcon,
  School as SchoolIcon,
  VideoLibrary as VideoLibraryIcon,
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  ArrowForward as ArrowForwardIcon,
  Description as DescriptionIcon,
  MenuBook as MenuBookIcon,
  Help as HelpIcon,
  Assignment as AssignmentIcon,
  Lightbulb as LightbulbIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Sample documentation categories
const docCategories = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: <SchoolIcon />,
    docs: [
      {
        id: 'introduction',
        title: 'Introduction to Gamestorme',
        description: 'Learn about the Gamestorme platform and ecosystem.',
      },
      {
        id: 'account-setup',
        title: 'Creating Your Developer Account',
        description: 'Step-by-step guide to setting up your Gamestorme developer account.',
      },
      {
        id: 'dashboard-overview',
        title: 'Dashboard Overview',
        description: 'Navigate the developer dashboard and understand its features.',
      },
      {
        id: 'first-game',
        title: 'Uploading Your First Game',
        description: 'Learn how to prepare and upload your first game to the platform.',
      },
    ],
  },
  {
    id: 'game-development',
    title: 'Game Development',
    icon: <CodeIcon />,
    docs: [
      {
        id: 'sdk-integration',
        title: 'SDK Integration Guide',
        description: 'Integrate the Gamestorme SDK into your game project.',
      },
      {
        id: 'api-reference',
        title: 'API Reference',
        description: 'Complete reference for the Gamestorme API endpoints and methods.',
      },
      {
        id: 'authentication',
        title: 'Authentication & Security',
        description: 'Implement secure authentication in your games.',
      },
      {
        id: 'analytics-integration',
        title: 'Analytics Integration',
        description: 'Track player behavior and game performance with our analytics tools.',
      },
    ],
  },
  {
    id: 'monetization',
    title: 'Monetization',
    icon: <ArticleIcon />,
    docs: [
      {
        id: 'monetization-options',
        title: 'Monetization Options',
        description: 'Overview of available monetization strategies for your games.',
      },
      {
        id: 'iap-setup',
        title: 'In-App Purchase Setup',
        description: 'Implement and manage in-app purchases in your games.',
      },
      {
        id: 'ad-integration',
        title: 'Ad Integration',
        description: 'Integrate and optimize ad placements in your games.',
      },
      {
        id: 'subscription-models',
        title: 'Subscription Models',
        description: 'Implement subscription-based monetization for your games.',
      },
    ],
  },
  {
    id: 'tutorials',
    title: 'Video Tutorials',
    icon: <VideoLibraryIcon />,
    docs: [
      {
        id: 'video-getting-started',
        title: 'Getting Started with Gamestorme',
        description: 'Video walkthrough of the Gamestorme platform basics.',
      },
      {
        id: 'video-sdk-integration',
        title: 'SDK Integration Tutorial',
        description: 'Step-by-step video guide to integrating our SDK.',
      },
      {
        id: 'video-analytics',
        title: 'Analytics Dashboard Tutorial',
        description: 'Learn how to use the analytics dashboard effectively.',
      },
      {
        id: 'video-marketing',
        title: 'Marketing Tools Tutorial',
        description: 'Video guide to using our marketing tools for your games.',
      },
    ],
  },
];

// Sample documentation content
const sampleDocContent = `
# Introduction to Gamestorme

Welcome to Gamestorme, the premier platform for game developers to publish, monetize, and grow their games. This guide will help you understand the Gamestorme ecosystem and how you can leverage our platform to reach millions of players worldwide.

## What is Gamestorme?

Gamestorme is a comprehensive game publishing platform that provides developers with all the tools they need to succeed:

- **Game Distribution**: Reach players across multiple platforms including web, mobile, and desktop.
- **Developer Tools**: Access SDKs, APIs, and analytics to enhance your games.
- **Monetization Options**: Implement various revenue models including in-app purchases, ads, and subscriptions.
- **Marketing Support**: Utilize AI-powered marketing tools to promote your games effectively.
- **Community Building**: Engage with players and other developers through our community features.

## Platform Benefits

### For Developers
- **Simplified Publishing**: Streamlined process to get your games to market quickly.
- **Cross-Platform Support**: Publish once and reach players on multiple platforms.
- **Comprehensive Analytics**: Gain insights into player behavior and game performance.
- **Marketing Assistance**: AI-powered tools to help market your games effectively.
- **Revenue Optimization**: Multiple monetization options to maximize your earnings.

### For Players
- **Diverse Game Library**: Access to a wide variety of games across genres.
- **Seamless Experience**: Play games across different devices with synchronized progress.
- **Community Features**: Connect with other players and developers.
- **Rewards System**: Earn rewards for playing and engaging with games.

## Getting Started

To begin your journey with Gamestorme, follow these steps:

1. **Create a Developer Account**: Sign up for a developer account on the Gamestorme platform.
2. **Set Up Your Profile**: Complete your developer profile with relevant information.
3. **Explore the Dashboard**: Familiarize yourself with the developer dashboard and its features.
4. **Integrate the SDK**: Add the Gamestorme SDK to your game project.
5. **Upload Your Game**: Prepare and upload your game to the platform.
6. **Launch and Monitor**: Publish your game and monitor its performance using our analytics tools.

## Next Steps

Once you've created your account, check out the following resources:

- [Dashboard Overview](dashboard-overview)
- [SDK Integration Guide](sdk-integration)
- [Monetization Options](monetization-options)
- [Marketing Tools](marketing-tools)

If you have any questions or need assistance, our support team is available to help you through the [Support Portal](support-portal) or via <NAME_EMAIL>.

Welcome aboard, and we look forward to seeing your games on Gamestorme!
`;

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

const Documentation: React.FC = () => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedDoc, setSelectedDoc] = useState<string | null>(null);
  const [docContent, setDocContent] = useState<string | null>(null);

  // Handle search
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setSelectedDoc(null);
    setDocContent(null);
  };

  // Handle document selection
  const handleDocSelect = (docId: string) => {
    setSelectedDoc(docId);
    // In a real app, you would fetch the document content from your backend
    setDocContent(sampleDocContent);
  };

  // Filter categories and docs based on search query
  const filteredCategories = docCategories.filter((category) => {
    if (!searchQuery) return true;
    
    const matchesCategory = category.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDocs = category.docs.some((doc) => 
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      doc.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    return matchesCategory || matchesDocs;
  });

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" fontWeight="bold">
          Documentation
        </Typography>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<BookmarkIcon />}
          sx={{
            borderRadius: theme.shape.borderRadius * 1.5,
            textTransform: 'none',
          }}
        >
          Bookmarks
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            placeholder="Search documentation..."
            variant="outlined"
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              mb: 4,
              '& .MuiOutlinedInput-root': {
                borderRadius: theme.shape.borderRadius * 1.5,
              },
            }}
          />
        </Grid>

        {!selectedDoc ? (
          <>
            <Grid item xs={12} md={selectedCategory ? 4 : 12}>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <Grid container spacing={3}>
                  {filteredCategories.map((category) => (
                    <Grid item xs={12} sm={selectedCategory ? 12 : 6} md={selectedCategory ? 12 : 3} key={category.id}>
                      <motion.div variants={itemVariants}>
                        <Card
                          sx={{
                            borderRadius: theme.shape.borderRadius * 2,
                            overflow: 'hidden',
                            boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.1)}`,
                            transition: 'all 0.3s ease',
                            height: '100%',
                            cursor: 'pointer',
                            border: selectedCategory === category.id ? `2px solid ${theme.palette.primary.main}` : 'none',
                            '&:hover': {
                              transform: 'translateY(-5px)',
                              boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.15)}`,
                            },
                          }}
                          onClick={() => handleCategorySelect(category.id)}
                        >
                          <CardContent sx={{ p: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Box
                                sx={{
                                  width: 48,
                                  height: 48,
                                  borderRadius: '50%',
                                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  mr: 2,
                                  color: theme.palette.primary.main,
                                }}
                              >
                                {category.icon}
                              </Box>
                              <Typography variant="h6" fontWeight="bold">
                                {category.title}
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {category.docs.length} articles
                            </Typography>
                            <Button
                              endIcon={<ArrowForwardIcon />}
                              sx={{ textTransform: 'none' }}
                            >
                              Browse
                            </Button>
                          </CardContent>
                        </Card>
                      </motion.div>
                    </Grid>
                  ))}
                </Grid>
              </motion.div>
            </Grid>

            {selectedCategory && (
              <Grid item xs={12} md={8}>
                <Paper
                  sx={{
                    p: 3,
                    borderRadius: theme.shape.borderRadius * 2,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    backdropFilter: 'blur(10px)',
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  }}
                >
                  <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                    {docCategories.find(cat => cat.id === selectedCategory)?.title}
                  </Typography>
                  <List>
                    {docCategories
                      .find(cat => cat.id === selectedCategory)
                      ?.docs.map((doc) => (
                        <React.Fragment key={doc.id}>
                          <ListItem
                            button
                            onClick={() => handleDocSelect(doc.id)}
                            sx={{
                              borderRadius: theme.shape.borderRadius,
                              mb: 1,
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.05),
                              },
                            }}
                          >
                            <ListItemIcon>
                              <DescriptionIcon color="primary" />
                            </ListItemIcon>
                            <ListItemText
                              primary={doc.title}
                              secondary={doc.description}
                              primaryTypographyProps={{ fontWeight: 'bold' }}
                            />
                          </ListItem>
                          <Divider component="li" />
                        </React.Fragment>
                      ))}
                  </List>
                </Paper>
              </Grid>
            )}
          </>
        ) : (
          <Grid item xs={12}>
            <Paper
              sx={{
                p: 3,
                borderRadius: theme.shape.borderRadius * 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.8),
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />}>
                  <Link
                    color="inherit"
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setSelectedCategory(null);
                      setSelectedDoc(null);
                      setDocContent(null);
                    }}
                  >
                    Documentation
                  </Link>
                  <Link
                    color="inherit"
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setSelectedDoc(null);
                      setDocContent(null);
                    }}
                  >
                    {docCategories.find(cat => cat.id === selectedCategory)?.title}
                  </Link>
                  <Typography color="text.primary">
                    {docCategories
                      .find(cat => cat.id === selectedCategory)
                      ?.docs.find(doc => doc.id === selectedDoc)?.title}
                  </Typography>
                </Breadcrumbs>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" fontWeight="bold">
                  {docCategories
                    .find(cat => cat.id === selectedCategory)
                    ?.docs.find(doc => doc.id === selectedDoc)?.title}
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<BookmarkIcon />}
                  sx={{
                    borderRadius: theme.shape.borderRadius * 1.5,
                    textTransform: 'none',
                  }}
                >
                  Bookmark
                </Button>
              </Box>

              <Divider sx={{ mb: 3 }} />

              <Typography
                variant="body1"
                component="div"
                sx={{
                  '& h1': {
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    mb: 2,
                  },
                  '& h2': {
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    mb: 2,
                    mt: 4,
                  },
                  '& p': {
                    mb: 2,
                  },
                  '& ul, & ol': {
                    mb: 2,
                    pl: 4,
                  },
                  '& li': {
                    mb: 1,
                  },
                  '& a': {
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  },
                }}
              >
                {docContent?.split('\n').map((line, index) => {
                  if (line.startsWith('# ')) {
                    return <Typography key={index} variant="h4" fontWeight="bold" gutterBottom>{line.substring(2)}</Typography>;
                  } else if (line.startsWith('## ')) {
                    return <Typography key={index} variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3 }}>{line.substring(3)}</Typography>;
                  } else if (line.startsWith('- ')) {
                    return <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <Box sx={{ width: 6, height: 6, borderRadius: '50%', bgcolor: 'primary.main', mt: 1, mr: 1 }} />
                      <Typography variant="body1">{line.substring(2)}</Typography>
                    </Box>;
                  } else if (line.startsWith('1. ')) {
                    const number = line.substring(0, line.indexOf('.'));
                    return <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', mr: 1 }}>{number}.</Typography>
                      <Typography variant="body1">{line.substring(line.indexOf('.') + 2)}</Typography>
                    </Box>;
                  } else if (line.trim() === '') {
                    return <Box key={index} sx={{ mb: 2 }} />;
                  } else {
                    return <Typography key={index} variant="body1" paragraph>{line}</Typography>;
                  }
                })}
              </Typography>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default Documentation;
