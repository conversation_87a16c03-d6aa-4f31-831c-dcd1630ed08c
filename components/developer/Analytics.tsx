import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
  alpha,
  useTheme,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  BarChart as BarChartIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  AttachMoney as AttachMoneyIcon,
  Public as PublicIcon,
  Devices as DevicesIcon,
  CalendarToday as CalendarTodayIcon,
  FilterList as FilterListIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

// Sample analytics data
const sampleGames = [
  { id: 1, name: 'Cosmic Odyssey', players: 12500, revenue: 125000, growth: 15 },
  { id: 2, name: '<PERSON>n Racer', players: 8700, revenue: 87000, growth: 8 },
  { id: 3, name: 'Mystic Realms', players: 3200, revenue: 32000, growth: -5 },
];

const samplePlayerData = [
  { date: '2023-01', players: 15000, newPlayers: 2500, churn: 1200 },
  { date: '2023-02', players: 16300, newPlayers: 2800, churn: 1500 },
  { date: '2023-03', players: 17600, newPlayers: 2600, churn: 1300 },
  { date: '2023-04', players: 18900, newPlayers: 2900, churn: 1600 },
  { date: '2023-05', players: 20200, newPlayers: 3100, churn: 1800 },
  { date: '2023-06', players: 21500, newPlayers: 3000, churn: 1700 },
];

const sampleRevenueData = [
  { date: '2023-01', revenue: 125000, transactions: 5000, arpu: 25 },
  { date: '2023-02', revenue: 137500, transactions: 5500, arpu: 25 },
  { date: '2023-03', revenue: 150000, transactions: 6000, arpu: 25 },
  { date: '2023-04', revenue: 162500, transactions: 6500, arpu: 25 },
  { date: '2023-05', revenue: 175000, transactions: 7000, arpu: 25 },
  { date: '2023-06', revenue: 187500, transactions: 7500, arpu: 25 },
];

const sampleGeoData = [
  { country: 'United States', players: 8500, revenue: 85000 },
  { country: 'China', players: 5200, revenue: 52000 },
  { country: 'Germany', players: 3100, revenue: 31000 },
  { country: 'United Kingdom', players: 2800, revenue: 28000 },
  { country: 'Japan', players: 2400, revenue: 24000 },
  { country: 'Brazil', players: 2000, revenue: 20000 },
  { country: 'India', players: 1800, revenue: 18000 },
  { country: 'Canada', players: 1500, revenue: 15000 },
  { country: 'France', players: 1300, revenue: 13000 },
  { country: 'Australia', players: 1100, revenue: 11000 },
];

const sampleDeviceData = [
  { device: 'Mobile', players: 12500, percentage: 52 },
  { device: 'PC', players: 8700, percentage: 36 },
  { device: 'Console', players: 2800, percentage: 12 },
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

const Analytics: React.FC = () => {
  const theme = useTheme();
  const { logout } = useAuth();
  const [selectedGame, setSelectedGame] = useState('all');
  const [timeRange, setTimeRange] = useState('6m');
  const [tabValue, setTabValue] = useState(0);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle filter changes
  const handleGameChange = (event: SelectChangeEvent) => {
    setSelectedGame(event.target.value);
  };

  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  // Calculate totals
  const totalPlayers = sampleGames.reduce((sum, game) => sum + game.players, 0);
  const totalRevenue = sampleGames.reduce((sum, game) => sum + game.revenue, 0);
  const averageGrowth = sampleGames.reduce((sum, game) => sum + game.growth, 0) / sampleGames.length;

  const handleLogout = async () => {
    try {
      await logout();
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" fontWeight="bold">
          Analytics Dashboard
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Game</InputLabel>
            <Select
              value={selectedGame}
              onChange={handleGameChange}
              label="Game"
            >
              <MenuItem value="all">All Games</MenuItem>
              {sampleGames.map((game) => (
                <MenuItem key={game.id} value={game.id.toString()}>
                  {game.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              onChange={handleTimeRangeChange}
              label="Time Range"
            >
              <MenuItem value="7d">Last 7 Days</MenuItem>
              <MenuItem value="1m">Last Month</MenuItem>
              <MenuItem value="3m">Last 3 Months</MenuItem>
              <MenuItem value="6m">Last 6 Months</MenuItem>
              <MenuItem value="1y">Last Year</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<LogoutIcon />}
            onClick={handleLogout}
            color="error"
            size="small"
          >
            Logout
          </Button>
        </Box>
      </Box>

      {/* Overview Cards */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <Paper
                sx={{
                  p: 3,
                  borderRadius: theme.shape.borderRadius * 2,
                  backgroundColor: alpha(theme.palette.background.paper, 0.8),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  height: '100%',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '50%',
                      backgroundColor: alpha(theme.palette.info.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <PeopleIcon sx={{ color: theme.palette.info.main }} />
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Total Players
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {totalPlayers.toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUpIcon sx={{ color: theme.palette.success.main, mr: 1 }} />
                  <Typography variant="body2" fontWeight="medium">
                    +{samplePlayerData[samplePlayerData.length - 1].newPlayers.toLocaleString()} new players this month
                  </Typography>
                </Box>
              </Paper>
            </motion.div>
          </Grid>
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <Paper
                sx={{
                  p: 3,
                  borderRadius: theme.shape.borderRadius * 2,
                  backgroundColor: alpha(theme.palette.background.paper, 0.8),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  height: '100%',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '50%',
                      backgroundColor: alpha(theme.palette.success.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <AttachMoneyIcon sx={{ color: theme.palette.success.main }} />
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      ${totalRevenue.toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUpIcon sx={{ color: theme.palette.success.main, mr: 1 }} />
                  <Typography variant="body2" fontWeight="medium">
                    +${(sampleRevenueData[sampleRevenueData.length - 1].revenue - sampleRevenueData[sampleRevenueData.length - 2].revenue).toLocaleString()} from last month
                  </Typography>
                </Box>
              </Paper>
            </motion.div>
          </Grid>
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <Paper
                sx={{
                  p: 3,
                  borderRadius: theme.shape.borderRadius * 2,
                  backgroundColor: alpha(theme.palette.background.paper, 0.8),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  height: '100%',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '50%',
                      backgroundColor: alpha(theme.palette.secondary.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <TrendingUpIcon sx={{ color: theme.palette.secondary.main }} />
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Average Growth
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {averageGrowth > 0 ? '+' : ''}{averageGrowth.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {averageGrowth > 0 ? (
                    <TrendingUpIcon sx={{ color: theme.palette.success.main, mr: 1 }} />
                  ) : (
                    <TrendingDownIcon sx={{ color: theme.palette.error.main, mr: 1 }} />
                  )}
                  <Typography variant="body2" fontWeight="medium">
                    {averageGrowth > 0 ? 'Positive' : 'Negative'} trend over {timeRange === '7d' ? 'the week' : timeRange === '1m' ? 'the month' : timeRange === '3m' ? '3 months' : timeRange === '6m' ? '6 months' : 'the year'}
                  </Typography>
                </Box>
              </Paper>
            </motion.div>
          </Grid>
        </Grid>
      </motion.div>

      {/* Tabs */}
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{
          mb: 3,
          '& .MuiTabs-indicator': {
            height: 3,
            borderRadius: 1.5,
          },
        }}
      >
        <Tab icon={<PeopleIcon />} label="Players" />
        <Tab icon={<AttachMoneyIcon />} label="Revenue" />
        <Tab icon={<PublicIcon />} label="Geography" />
        <Tab icon={<DevicesIcon />} label="Platforms" />
      </Tabs>

      {/* Tab Content */}
      <Box sx={{ mb: 4 }}>
        {tabValue === 0 && (
          <Paper
            sx={{
              p: 3,
              borderRadius: theme.shape.borderRadius * 2,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            }}
          >
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
              Player Statistics
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Month</TableCell>
                    <TableCell align="right">Total Players</TableCell>
                    <TableCell align="right">New Players</TableCell>
                    <TableCell align="right">Churn</TableCell>
                    <TableCell align="right">Growth</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {samplePlayerData.map((row) => (
                    <TableRow key={row.date}>
                      <TableCell>{new Date(row.date).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</TableCell>
                      <TableCell align="right">{row.players.toLocaleString()}</TableCell>
                      <TableCell align="right">{row.newPlayers.toLocaleString()}</TableCell>
                      <TableCell align="right">{row.churn.toLocaleString()}</TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                          {row.newPlayers > row.churn ? (
                            <TrendingUpIcon sx={{ color: theme.palette.success.main, mr: 0.5, fontSize: '1rem' }} />
                          ) : (
                            <TrendingDownIcon sx={{ color: theme.palette.error.main, mr: 0.5, fontSize: '1rem' }} />
                          )}
                          {((row.newPlayers - row.churn) / row.players * 100).toFixed(1)}%
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}

        {tabValue === 1 && (
          <Paper
            sx={{
              p: 3,
              borderRadius: theme.shape.borderRadius * 2,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            }}
          >
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
              Revenue Statistics
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Month</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">Transactions</TableCell>
                    <TableCell align="right">Avg. Revenue Per User</TableCell>
                    <TableCell align="right">Growth</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sampleRevenueData.map((row, index) => {
                    const prevRevenue = index > 0 ? sampleRevenueData[index - 1].revenue : row.revenue;
                    const growth = ((row.revenue - prevRevenue) / prevRevenue) * 100;

                    return (
                      <TableRow key={row.date}>
                        <TableCell>{new Date(row.date).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</TableCell>
                        <TableCell align="right">${row.revenue.toLocaleString()}</TableCell>
                        <TableCell align="right">{row.transactions.toLocaleString()}</TableCell>
                        <TableCell align="right">${row.arpu.toFixed(2)}</TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            {growth >= 0 ? (
                              <TrendingUpIcon sx={{ color: theme.palette.success.main, mr: 0.5, fontSize: '1rem' }} />
                            ) : (
                              <TrendingDownIcon sx={{ color: theme.palette.error.main, mr: 0.5, fontSize: '1rem' }} />
                            )}
                            {index === 0 ? '0.0' : growth.toFixed(1)}%
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}

        {tabValue === 2 && (
          <Paper
            sx={{
              p: 3,
              borderRadius: theme.shape.borderRadius * 2,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            }}
          >
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
              Geographic Distribution
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Country</TableCell>
                    <TableCell align="right">Players</TableCell>
                    <TableCell align="right">% of Total</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">% of Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sampleGeoData.map((row) => (
                    <TableRow key={row.country}>
                      <TableCell>{row.country}</TableCell>
                      <TableCell align="right">{row.players.toLocaleString()}</TableCell>
                      <TableCell align="right">{(row.players / totalPlayers * 100).toFixed(1)}%</TableCell>
                      <TableCell align="right">${row.revenue.toLocaleString()}</TableCell>
                      <TableCell align="right">{(row.revenue / totalRevenue * 100).toFixed(1)}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}

        {tabValue === 3 && (
          <Paper
            sx={{
              p: 3,
              borderRadius: theme.shape.borderRadius * 2,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            }}
          >
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
              Platform Distribution
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Platform</TableCell>
                    <TableCell align="right">Players</TableCell>
                    <TableCell align="right">% of Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sampleDeviceData.map((row) => (
                    <TableRow key={row.device}>
                      <TableCell>{row.device}</TableCell>
                      <TableCell align="right">{row.players.toLocaleString()}</TableCell>
                      <TableCell align="right">{row.percentage}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}
      </Box>
    </Box>
  );
};

export default Analytics;
