import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  Tabs,
  Tab,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  alpha,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Comment as CommentIcon,
  ThumbUp as ThumbUpIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  Send as SendIcon,
  Forum as ForumIcon,
  Article as ArticleIcon,
  Group as GroupIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Sample blog posts
const sampleBlogPosts = [
  {
    id: 1,
    title: 'How We Optimized Our Game for Mobile Devices',
    content: "In this post, I'll share our journey of optimizing 'Cosmic Odyssey' for mobile devices. We faced several challenges with performance and UI scaling, but through careful optimization and testing, we managed to achieve smooth gameplay across various devices...",
    author: {
      name: '<PERSON>',
      avatar: '/images/avatars/avatar1.jpg',
      company: 'Stellar Games',
    },
    date: '2023-06-15',
    likes: 42,
    comments: 8,
    tags: ['Mobile', 'Optimization', 'Performance'],
  },
  {
    id: 2,
    title: 'Implementing Effective Monetization Strategies',
    content: "Monetization is a critical aspect of game development. In this post, I'll discuss how we implemented various monetization strategies in 'Neon Racer' without compromising player experience. From in-app purchases to rewarded ads, we'll cover it all...",
    author: {
      name: 'Sarah Chen',
      avatar: '/images/avatars/avatar2.jpg',
      company: 'Neon Studios',
    },
    date: '2023-06-10',
    likes: 38,
    comments: 12,
    tags: ['Monetization', 'IAP', 'Revenue'],
  },
  {
    id: 3,
    title: 'Building a Thriving Game Community',
    content: "Community is the heart of any successful game. In this post, I'll share our strategies for building and nurturing the 'Mystic Realms' community. From Discord servers to social media engagement, learn how we created a passionate player base...",
    author: {
      name: 'Michael Rodriguez',
      avatar: '/images/avatars/avatar3.jpg',
      company: 'Mystic Games',
    },
    date: '2023-06-05',
    likes: 56,
    comments: 15,
    tags: ['Community', 'Engagement', 'Discord'],
  },
];

// Sample forum discussions
const sampleDiscussions = [
  {
    id: 1,
    title: 'Best practices for implementing cloud saves?',
    content: "I'm working on implementing cloud saves for my game and would love to hear about best practices and potential pitfalls to avoid.",
    author: {
      name: 'David Kim',
      avatar: '/images/avatars/avatar4.jpg',
    },
    date: '2023-06-18',
    replies: 12,
    views: 156,
    lastReply: '2023-06-20',
  },
  {
    id: 2,
    title: 'How do you handle player feedback and bug reports?',
    content: "I'm looking for efficient ways to collect and organize player feedback and bug reports. What tools and processes do you use?",
    author: {
      name: 'Emily Watson',
      avatar: '/images/avatars/avatar5.jpg',
    },
    date: '2023-06-16',
    replies: 8,
    views: 124,
    lastReply: '2023-06-19',
  },
  {
    id: 3,
    title: 'Strategies for cross-platform multiplayer implementation',
    content: "I'm planning to add cross-platform multiplayer to my game. What are some effective strategies and technologies for implementing this?",
    author: {
      name: 'James Wilson',
      avatar: '/images/avatars/avatar6.jpg',
    },
    date: '2023-06-14',
    replies: 15,
    views: 210,
    lastReply: '2023-06-20',
  },
];

// Sample chat messages
const sampleChatMessages = [
  {
    id: 1,
    channel: 'General',
    messages: [
      {
        id: 1,
        author: {
          name: 'Alex Johnson',
          avatar: '/images/avatars/avatar1.jpg',
        },
        content: "Hey everyone! Has anyone here implemented Apple's Game Center in their iOS games?",
        timestamp: '2023-06-20T10:30:00',
      },
      {
        id: 2,
        author: {
          name: 'Sarah Chen',
          avatar: '/images/avatars/avatar2.jpg',
        },
        content: "Yes, I've implemented it in my last game. It's pretty straightforward but there are a few gotchas.",
        timestamp: '2023-06-20T10:32:00',
      },
      {
        id: 3,
        author: {
          name: 'Michael Rodriguez',
          avatar: '/images/avatars/avatar3.jpg',
        },
        content: 'I found this great tutorial that might help: [link to tutorial]',
        timestamp: '2023-06-20T10:35:00',
      },
    ],
  },
  {
    id: 2,
    channel: 'Unity Developers',
    messages: [
      {
        id: 1,
        author: {
          name: 'Emily Watson',
          avatar: '/images/avatars/avatar5.jpg',
        },
        content: 'Has anyone upgraded to Unity 2022 yet? Any major issues I should be aware of?',
        timestamp: '2023-06-20T09:15:00',
      },
      {
        id: 2,
        author: {
          name: 'James Wilson',
          avatar: '/images/avatars/avatar6.jpg',
        },
        content: 'I upgraded last week. The new features are great, but I did run into some shader compatibility issues.',
        timestamp: '2023-06-20T09:18:00',
      },
    ],
  },
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

const Community: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [openNewPostDialog, setOpenNewPostDialog] = useState(false);
  const [openNewDiscussionDialog, setOpenNewDiscussionDialog] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(1);
  const [newMessage, setNewMessage] = useState('');
  const [newPost, setNewPost] = useState({
    title: '',
    content: '',
    tags: '',
  });
  const [newDiscussion, setNewDiscussion] = useState({
    title: '',
    content: '',
  });

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle new post dialog
  const handleOpenNewPostDialog = () => {
    setOpenNewPostDialog(true);
  };

  const handleCloseNewPostDialog = () => {
    setOpenNewPostDialog(false);
  };

  // Handle new discussion dialog
  const handleOpenNewDiscussionDialog = () => {
    setOpenNewDiscussionDialog(true);
  };

  const handleCloseNewDiscussionDialog = () => {
    setOpenNewDiscussionDialog(false);
  };

  // Handle input changes
  const handlePostInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setNewPost({ ...newPost, [name]: value });
  };

  const handleDiscussionInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setNewDiscussion({ ...newDiscussion, [name]: value });
  };

  const handleMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(event.target.value);
  };

  // Handle channel selection
  const handleChannelSelect = (channelId: number) => {
    setSelectedChannel(channelId);
  };

  // Handle send message
  const handleSendMessage = () => {
    if (newMessage.trim()) {
      // In a real app, you would send this message to your backend
      console.log('Sending message:', newMessage);
      setNewMessage('');
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" fontWeight="bold">
          Community
        </Typography>
        {tabValue === 0 && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenNewPostDialog}
            sx={{
              borderRadius: theme.shape.borderRadius * 1.5,
              textTransform: 'none',
              px: 3,
            }}
          >
            Write New Post
          </Button>
        )}
        {tabValue === 1 && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenNewDiscussionDialog}
            sx={{
              borderRadius: theme.shape.borderRadius * 1.5,
              textTransform: 'none',
              px: 3,
            }}
          >
            Start New Discussion
          </Button>
        )}
      </Box>

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{
          mb: 4,
          '& .MuiTabs-indicator': {
            height: 3,
            borderRadius: 1.5,
          },
        }}
      >
        <Tab icon={<ArticleIcon />} label="Developer Blogs" />
        <Tab icon={<ForumIcon />} label="Forums" />
        <Tab icon={<GroupIcon />} label="Community Chat" />
      </Tabs>

      {/* Developer Blogs */}
      {tabValue === 0 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3}>
            {sampleBlogPosts.map((post) => (
              <Grid item xs={12} key={post.id}>
                <motion.div variants={itemVariants}>
                  <Card sx={{
                    borderRadius: theme.shape.borderRadius * 2,
                    overflow: 'hidden',
                    boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.1)}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.15)}`,
                    },
                  }}>
                    <CardHeader
                      avatar={
                        <Avatar src={post.author.avatar} alt={post.author.name} />
                      }
                      action={
                        <IconButton aria-label="settings">
                          <MoreVertIcon />
                        </IconButton>
                      }
                      title={post.author.name}
                      subheader={`${post.author.company} • ${new Date(post.date).toLocaleDateString()}`}
                    />
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {post.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {post.content}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                        {post.tags.map((tag) => (
                          <Chip key={tag} label={tag} size="small" />
                        ))}
                      </Box>
                      <Divider sx={{ my: 2 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Button
                            startIcon={<ThumbUpIcon />}
                            size="small"
                            color="primary"
                          >
                            {post.likes}
                          </Button>
                          <Button
                            startIcon={<CommentIcon />}
                            size="small"
                            color="primary"
                          >
                            {post.comments}
                          </Button>
                        </Box>
                        <Button
                          startIcon={<ShareIcon />}
                          size="small"
                          color="primary"
                        >
                          Share
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      )}

      {/* Forums */}
      {tabValue === 1 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3}>
            {sampleDiscussions.map((discussion) => (
              <Grid item xs={12} key={discussion.id}>
                <motion.div variants={itemVariants}>
                  <Card sx={{
                    borderRadius: theme.shape.borderRadius * 2,
                    overflow: 'hidden',
                    boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.1)}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.15)}`,
                    },
                  }}>
                    <CardHeader
                      avatar={
                        <Avatar src={discussion.author.avatar} alt={discussion.author.name} />
                      }
                      action={
                        <IconButton aria-label="settings">
                          <MoreVertIcon />
                        </IconButton>
                      }
                      title={discussion.author.name}
                      subheader={`Posted on ${new Date(discussion.date).toLocaleDateString()}`}
                    />
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {discussion.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {discussion.content}
                      </Typography>
                      <Divider sx={{ my: 2 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            {discussion.replies} replies
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {discussion.views} views
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          Last reply: {new Date(discussion.lastReply).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      )}

      {/* Community Chat */}
      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Paper
              sx={{
                p: 2,
                borderRadius: theme.shape.borderRadius * 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.8),
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                height: '70vh',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                Channels
              </Typography>
              <List sx={{ flexGrow: 1, overflow: 'auto' }}>
                {sampleChatMessages.map((channel) => (
                  <ListItem
                    key={channel.id}
                    button
                    selected={selectedChannel === channel.id}
                    onClick={() => handleChannelSelect(channel.id)}
                    sx={{
                      borderRadius: theme.shape.borderRadius,
                      mb: 1,
                      '&.Mui-selected': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.2),
                        },
                      },
                    }}
                  >
                    <ListItemText
                      primary={channel.channel}
                      secondary={`${channel.messages.length} messages`}
                    />
                  </ListItem>
                ))}
              </List>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                fullWidth
                sx={{ mt: 2 }}
              >
                Join New Channel
              </Button>
            </Paper>
          </Grid>
          <Grid item xs={12} md={9}>
            <Paper
              sx={{
                borderRadius: theme.shape.borderRadius * 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.8),
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                height: '70vh',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Box sx={{
                p: 2,
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                <Typography variant="h6" fontWeight="bold">
                  {sampleChatMessages.find(channel => channel.id === selectedChannel)?.channel}
                </Typography>
                <IconButton>
                  <NotificationsIcon />
                </IconButton>
              </Box>
              <Box sx={{ flexGrow: 1, p: 2, overflow: 'auto' }}>
                {sampleChatMessages
                  .find(channel => channel.id === selectedChannel)
                  ?.messages.map((message) => (
                    <Box
                      key={message.id}
                      sx={{
                        display: 'flex',
                        mb: 2,
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar src={message.author.avatar} alt={message.author.name} />
                      </ListItemAvatar>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {message.author.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </Box>
                        <Typography variant="body2">
                          {message.content}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
              </Box>
              <Box sx={{
                p: 2,
                borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                display: 'flex',
                gap: 1,
              }}>
                <TextField
                  fullWidth
                  placeholder="Type a message..."
                  variant="outlined"
                  value={newMessage}
                  onChange={handleMessageChange}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSendMessage();
                    }
                  }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<SendIcon />}
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                >
                  Send
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* New Post Dialog */}
      <Dialog
        open={openNewPostDialog}
        onClose={handleCloseNewPostDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Write New Blog Post</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Title"
                name="title"
                value={newPost.title}
                onChange={handlePostInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Content"
                name="content"
                value={newPost.content}
                onChange={handlePostInputChange}
                multiline
                rows={8}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Tags (comma separated)"
                name="tags"
                value={newPost.tags}
                onChange={handlePostInputChange}
                placeholder="e.g., Unity, Mobile, Optimization"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseNewPostDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            disabled={!newPost.title || !newPost.content}
          >
            Publish Post
          </Button>
        </DialogActions>
      </Dialog>

      {/* New Discussion Dialog */}
      <Dialog
        open={openNewDiscussionDialog}
        onClose={handleCloseNewDiscussionDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Start New Discussion</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Title"
                name="title"
                value={newDiscussion.title}
                onChange={handleDiscussionInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Content"
                name="content"
                value={newDiscussion.content}
                onChange={handleDiscussionInputChange}
                multiline
                rows={6}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseNewDiscussionDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            disabled={!newDiscussion.title || !newDiscussion.content}
          >
            Post Discussion
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Community;
