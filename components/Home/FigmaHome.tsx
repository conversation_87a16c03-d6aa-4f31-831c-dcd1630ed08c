import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  useTheme,
  alpha,
  Card,
  CardContent,
  CardMedia,
  Divider,
  IconButton,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import Link from 'next/link';
import Image from 'next/image';

// Styled components
const HeroSection = styled(Box)(({ theme }) => ({
  position: 'relative',
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  overflow: 'hidden',
  paddingTop: theme.spacing(10),
  paddingBottom: theme.spacing(10),
  [theme.breakpoints.down('md')]: {
    paddingTop: theme.spacing(8),
    paddingBottom: theme.spacing(8),
  },
}));

const GradientText = styled(Typography)(({ theme }) => ({
  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  display: 'inline-block',
}));

const HeroButton = styled(Button)(({ theme }) => ({
  borderRadius: '0px 16px',
  padding: '15px 40px',
  fontSize: '1.1rem',
  fontWeight: 600,
  marginTop: theme.spacing(4),
  marginRight: theme.spacing(2),
  position: 'relative',
  overflow: 'hidden',
  zIndex: 1,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'linear-gradient(270deg, #4229BC 0%, #7B65ED 100%)',
    zIndex: -1,
    transition: 'all 0.3s ease-in-out',
  },
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 10px 20px rgba(0, 0, 0, 0.3)',
  },
}));

const VideoButton = styled(IconButton)(({ theme }) => ({
  width: 80,
  height: 80,
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  border: `2px solid ${theme.palette.primary.main}`,
  marginTop: theme.spacing(4),
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    transform: 'scale(1.1)',
    boxShadow: `0 0 30px ${alpha(theme.palette.primary.main, 0.5)}`,
  },
  '& svg': {
    fontSize: 40,
    color: theme.palette.primary.main,
    transition: 'all 0.3s ease',
  },
  '&:hover svg': {
    color: theme.palette.common.white,
  },
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-10px)',
    boxShadow: `0 20px 30px ${alpha(theme.palette.common.black, 0.3)}`,
  },
}));

const GamesGrid = styled(Grid)(({ theme }) => ({
  marginTop: theme.spacing(10),
  marginBottom: theme.spacing(10),
}));

const GameCard = styled(Card)(({ theme }) => ({
  position: 'relative',
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  backgroundColor: 'transparent',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-10px)',
    boxShadow: `0 20px 30px ${alpha(theme.palette.common.black, 0.3)}`,
    '& .game-image': {
      transform: 'scale(1.05)',
    },
  },
}));

const GameImage = styled(CardMedia)(({ theme }) => ({
  height: 240,
  transition: 'transform 0.5s ease-in-out',
}));

const FigmaHome: React.FC = () => {
  const theme = useTheme();

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: [0.6, 0.05, -0.01, 0.9] }
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  // Gamestorme's featured games
  const games = [
    { id: 1, title: 'Agueybana', image: '/assets/images/games/agueybana.jpg', category: 'Adventure' },
    { id: 2, title: 'Guaramania', image: '/assets/images/games/guaramania.png', category: 'Rhythm' },
    { id: 3, title: 'Hajimari', image: '/assets/images/games/hajimari.jpg', category: 'Anime' },
  ];

  return (
    <>
      {/* Hero Section */}
      <HeroSection>
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <motion.div
                initial="hidden"
                animate="visible"
                variants={fadeIn}
              >
                <Typography
                  variant="overline"
                  sx={{
                    color: theme.palette.secondary.main,
                    fontWeight: 600,
                    letterSpacing: 2,
                    mb: 2,
                    display: 'block'
                  }}
                >
                  WELCOME TO GAMESTORME
                </Typography>

                <Typography
                  variant="h1"
                  sx={{
                    fontWeight: 800,
                    mb: 2,
                    fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' }
                  }}
                >
                  The Future of <GradientText>Gaming</GradientText> is Here
                </Typography>

                <Typography
                  variant="h6"
                  color="text.secondary"
                  sx={{
                    maxWidth: 500,
                    mb: 4,
                    lineHeight: 1.6
                  }}
                >
                  Experience the revolutionary gaming and blockchain ecosystem designed to transform the way players interact with games.
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                  <Link href="/games" passHref style={{ textDecoration: 'none' }}>
                    <HeroButton
                      variant="contained"
                      endIcon={<ArrowForwardIcon />}
                    >
                      Explore Games
                    </HeroButton>
                  </Link>

                  <Box sx={{ display: 'flex', alignItems: 'center', ml: { xs: 0, sm: 2 }, mt: { xs: 2, sm: 0 } }}>
                    <VideoButton aria-label="Watch video">
                      <PlayArrowIcon />
                    </VideoButton>
                    <Typography
                      variant="subtitle1"
                      sx={{ ml: 2, fontWeight: 500 }}
                    >
                      Watch Trailer
                    </Typography>
                  </Box>
                </Box>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, ease: [0.6, 0.05, -0.01, 0.9] }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    height: { xs: '300px', md: '500px' },
                    width: '100%',
                    borderRadius: '20px',
                    overflow: 'hidden',
                    boxShadow: `0 20px 80px ${alpha(theme.palette.primary.main, 0.3)}`,
                  }}
                >
                  <Image
                    src="/assets/images/support/Gamestorme Experience.jpg"
                    alt="Gaming Experience"
                    fill
                    style={{ objectFit: 'cover' }}
                    priority
                  />
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </HeroSection>

      {/* Features Section */}
      <Box sx={{ py: 10, position: 'relative' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="overline"
              sx={{
                color: theme.palette.secondary.main,
                fontWeight: 600,
                letterSpacing: 2,
                mb: 1,
                display: 'block'
              }}
            >
              FEATURES
            </Typography>
            <Typography variant="h2" sx={{ fontWeight: 800, mb: 2 }}>
              Why Choose Gamestorme
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: 700,
                mx: 'auto'
              }}
            >
              Discover the cutting-edge features that make our platform unique
            </Typography>
          </Box>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
          >
            <Grid container spacing={4}>
              {[1, 2, 3].map((item) => (
                <Grid item xs={12} md={4} key={item}>
                  <motion.div variants={fadeIn} style={{ height: '100%' }}>
                    <FeatureCard>
                      <CardContent sx={{ p: 4 }}>
                        <Box
                          sx={{
                            width: 70,
                            height: 70,
                            borderRadius: '20px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(
                              theme.palette.primary.main,
                              0.1
                            )} 100%)`,
                            boxShadow: `0 10px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
                            mb: 3
                          }}
                        >
                          <Typography variant="h4" color="primary">0{item}</Typography>
                        </Box>
                        <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
                          Feature Title {item}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          Detailed description of this amazing feature and how it benefits users of the Gamestorme platform.
                        </Typography>
                      </CardContent>
                    </FeatureCard>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </Box>

      {/* Games Section */}
      <GamesGrid container>
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 6 }}>
            <Box>
              <Typography
                variant="overline"
                sx={{
                  color: theme.palette.secondary.main,
                  fontWeight: 600,
                  letterSpacing: 2,
                  mb: 1,
                  display: 'block'
                }}
              >
                GAMES
              </Typography>
              <Typography variant="h2" sx={{ fontWeight: 800 }}>
                Featured Games
              </Typography>
            </Box>
            <Link href="/games" passHref style={{ textDecoration: 'none' }}>
              <Button
                variant="outlined"
                color="secondary"
                endIcon={<ArrowForwardIcon />}
              >
                View All
              </Button>
            </Link>
          </Box>

          <Grid container spacing={4}>
            {games.map((game) => (
              <Grid item xs={12} sm={6} md={4} key={game.id}>
                <GameCard>
                  <GameImage
                    className="game-image"
                    image={game.image}
                    title={game.title}
                  />
                  <CardContent sx={{ p: 3 }}>
                    <Typography
                      variant="caption"
                      sx={{
                        color: theme.palette.secondary.main,
                        fontWeight: 600,
                        mb: 1,
                        display: 'block'
                      }}
                    >
                      {game.category}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
                      {game.title}
                    </Typography>
                    <Link href={`/games/${game.id}`} passHref style={{ textDecoration: 'none', width: '100%' }}>
                      <Button
                        variant="contained"
                        color="primary"
                        fullWidth
                        endIcon={<ArrowForwardIcon />}
                      >
                        Play Now
                      </Button>
                    </Link>
                  </CardContent>
                </GameCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </GamesGrid>
    </>
  );
};

export default FigmaHome;
