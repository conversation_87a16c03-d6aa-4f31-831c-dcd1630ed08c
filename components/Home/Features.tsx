import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import DevicesIcon from '@mui/icons-material/Devices';
import TokenIcon from '@mui/icons-material/Token';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  padding: theme.spacing(10, 0),
  overflow: 'hidden',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  fontWeight: 800,
  textAlign: 'center',
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(8),
  textAlign: 'center',
  maxWidth: 700,
  margin: '0 auto',
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: 24,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.glass, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.1)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
  boxShadow: `0 8px 32px ${alpha('#000', 0.3)}, inset 0 1px 0 ${alpha('#fff', 0.1)}`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '1px',
    background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.light, 0.6)} 50%, transparent 100%)`,
    zIndex: 1,
  },
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.glassHover, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.2)} 100%)`,
    borderColor: alpha(theme.palette.primary.main, 0.3),
    boxShadow: `0 25px 60px ${alpha('#000', 0.4)}, 0 0 40px ${alpha(theme.palette.primary.main, 0.3)}, inset 0 1px 0 ${alpha('#fff', 0.2)}`,
    '& .feature-icon-container': {
      transform: 'scale(1.15) rotate(5deg)',
    },
    '&::before': {
      background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.light, 0.8)} 50%, transparent 100%)`,
    },
  },
}));

const FeatureIconContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(3),
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
}));

const FeatureTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const FeatureDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
}));

// Feature data
const features = [
  {
    icon: <SportsEsportsIcon />,
    title: 'Digital Game Store',
    description: 'Discover and download amazing games from our curated digital marketplace with seamless distribution.',
    color: '#4229BC',
  },
  {
    icon: <SmartToyIcon />,
    title: 'AI Marketing Tools',
    description: 'Boost your game sales with our AI-powered marketing platform designed specifically for game developers.',
    color: '#F0BC2B',
  },
  {
    icon: <AnalyticsIcon />,
    title: 'Sales Analytics',
    description: 'Track performance, analyze market trends, and optimize your marketing campaigns with real-time data.',
    color: '#00D97E',
  },
  {
    icon: <TrendingUpIcon />,
    title: 'Revenue Optimization',
    description: 'Maximize your game revenue with adaptive pricing strategies and AI-driven sales recommendations.',
    color: '#FF5252',
  },
  {
    icon: <DevicesIcon />,
    title: 'Cross-Platform',
    description: 'Reach players across PC, mobile, and console with unified distribution and marketing campaigns.',
    color: '#0288D1',
  },
  {
    icon: <TokenIcon />,
    title: 'Blockchain Enhanced',
    description: 'Optional blockchain features for enhanced security, ownership, and unique gaming experiences.',
    color: '#FF9800',
  },
];

const Features: React.FC = () => {
  const theme = useTheme();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <SectionContainer>
      {/* Background elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          left: '-10%',
          width: '25%',
          height: '40%',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 70%)`,
          filter: 'blur(60px)',
          zIndex: -1,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '10%',
          right: '-5%',
          width: '20%',
          height: '30%',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.secondary.main, 0.15)} 0%, transparent 70%)`,
          filter: 'blur(60px)',
          zIndex: -1,
        }}
      />

      <Container maxWidth="lg">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
        >
          <SectionTitle variant="h2">
            Empowering Game Developers
          </SectionTitle>
          <SectionSubtitle variant="h6" color="text.secondary">
            Discover how our AI-powered marketing tools and digital game store help developers boost sales and reach more players worldwide.
          </SectionSubtitle>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
        >
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <motion.div variants={itemVariants} style={{ height: '100%' }}>
                  <FeatureCard>
                    <CardContent sx={{ p: 4 }}>
                      <FeatureIconContainer className="feature-icon-container">
                        <Box
                          sx={{
                            width: 80,
                            height: 80,
                            borderRadius: '24px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: `linear-gradient(135deg, ${alpha(feature.color, 0.25)} 0%, ${alpha(
                              feature.color,
                              0.1
                            )} 100%)`,
                            border: `2px solid ${alpha(feature.color, 0.3)}`,
                            boxShadow: `0 15px 30px ${alpha(feature.color, 0.3)}, 0 0 20px ${alpha(feature.color, 0.2)}, inset 0 1px 0 ${alpha('#fff', 0.1)}`,
                            position: 'relative',
                            overflow: 'hidden',
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              background: `radial-gradient(circle at 30% 30%, ${alpha(feature.color, 0.4)} 0%, transparent 70%)`,
                              zIndex: 0,
                            },
                            '& svg': {
                              fontSize: 40,
                              color: feature.color,
                              zIndex: 1,
                              position: 'relative',
                              filter: `drop-shadow(0 2px 4px ${alpha(feature.color, 0.3)})`,
                            },
                          }}
                        >
                          {feature.icon}
                        </Box>
                      </FeatureIconContainer>
                      <FeatureTitle variant="h5">{feature.title}</FeatureTitle>
                      <FeatureDescription variant="body1">
                        {feature.description}
                      </FeatureDescription>
                    </CardContent>
                  </FeatureCard>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      </Container>
    </SectionContainer>
  );
};

export default Features;
