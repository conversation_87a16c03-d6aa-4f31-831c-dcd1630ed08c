import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import DownloadIcon from '@mui/icons-material/Download';
import Link from 'next/link';

// Styled components
const CTAContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  padding: theme.spacing(12, 0),
  overflow: 'hidden',
  marginTop: theme.spacing(8),
  marginBottom: theme.spacing(8),
}));

const CTAContent = styled(Box)(({ theme }) => ({
  position: 'relative',
  zIndex: 2,
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(8, 4),
  backgroundImage: `linear-gradient(135deg, ${alpha(theme.palette.primary.dark, 0.9)} 0%, ${alpha(
    theme.palette.primary.main,
    0.8
  )} 100%)`,
  backdropFilter: 'blur(10px)',
  boxShadow: `0 30px 60px ${alpha(theme.palette.common.black, 0.25)}`,
  overflow: 'hidden',
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(8),
  },
}));

const CTATitle = styled(Typography)(({ theme }) => ({
  fontWeight: 800,
  marginBottom: theme.spacing(2),
  color: theme.palette.common.white,
  [theme.breakpoints.down('md')]: {
    fontSize: '2.5rem',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '2rem',
  },
}));

const CTASubtitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  color: alpha(theme.palette.common.white, 0.8),
  maxWidth: 600,
}));

const CTAButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(1.5, 4),
  borderRadius: theme.shape.borderRadius,
  fontWeight: 600,
  fontSize: '1rem',
  backgroundColor: theme.palette.secondary.main,
  color: theme.palette.secondary.contrastText,
  border: `2px solid ${theme.palette.secondary.main}`,
  '&:hover': {
    backgroundColor: alpha(theme.palette.secondary.main, 0.9),
    transform: 'translateY(-3px)',
    boxShadow: `0 10px 20px ${alpha(theme.palette.common.black, 0.3)}`,
  },
}));

const CTASecondaryButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(1.5, 4),
  borderRadius: theme.shape.borderRadius,
  fontWeight: 600,
  fontSize: '1rem',
  backgroundColor: 'transparent',
  color: theme.palette.common.white,
  border: `2px solid ${alpha(theme.palette.common.white, 0.5)}`,
  '&:hover': {
    backgroundColor: alpha(theme.palette.common.white, 0.1),
    borderColor: theme.palette.common.white,
    transform: 'translateY(-3px)',
    boxShadow: `0 10px 20px ${alpha(theme.palette.common.black, 0.2)}`,
  },
}));

const FloatingElement = styled(Box)(({ theme }) => ({
  position: 'absolute',
  borderRadius: '50%',
  filter: 'blur(80px)',
  zIndex: 0,
}));

const CallToAction: React.FC = () => {
  const theme = useTheme();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <CTAContainer>
      <Container maxWidth="lg">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
        >
          <CTAContent>
            {/* Decorative elements */}
            <FloatingElement
              sx={{
                width: '40%',
                height: '60%',
                top: '-20%',
                right: '-10%',
                background: `radial-gradient(circle, ${alpha(theme.palette.secondary.main, 0.3)} 0%, transparent 70%)`,
              }}
            />
            <FloatingElement
              sx={{
                width: '30%',
                height: '40%',
                bottom: '-10%',
                left: '-5%',
                background: `radial-gradient(circle, ${alpha(theme.palette.primary.light, 0.3)} 0%, transparent 70%)`,
              }}
            />

            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={8}>
                <motion.div variants={itemVariants}>
                  <CTATitle variant="h2">
                    Ready to Join the Gaming Revolution?
                  </CTATitle>
                </motion.div>
                <motion.div variants={itemVariants}>
                  <CTASubtitle variant="h6">
                    Create your account today and start exploring the future of gaming with blockchain technology, NFTs, and play-to-earn opportunities. Download our desktop launcher for the best experience.
                  </CTASubtitle>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={4} sx={{ textAlign: { xs: 'center', md: 'right' } }}>
                <motion.div variants={itemVariants}>
                  <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: { xs: 'center', md: 'flex-end' } }}>
                    <Link href="/signup" passHref style={{ textDecoration: 'none' }}>
                      <CTAButton
                        size="large"
                        endIcon={<ArrowForwardIcon />}
                      >
                        Get Started
                      </CTAButton>
                    </Link>
                    <Link href="/download" passHref style={{ textDecoration: 'none' }}>
                      <CTASecondaryButton
                        size="large"
                        startIcon={<DownloadIcon />}
                      >
                        Download Launcher
                      </CTASecondaryButton>
                    </Link>
                  </Box>
                </motion.div>
              </Grid>
            </Grid>

            {/* Stats */}
            <motion.div variants={itemVariants}>
              <Grid container spacing={3} sx={{ mt: 6, pt: 6, borderTop: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>
                <Grid item xs={6} sm={3}>
                  <Typography variant="h3" fontWeight="bold" color="white">
                    100+
                  </Typography>
                  <Typography variant="body2" color={alpha(theme.palette.common.white, 0.7)}>
                    Games Available
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="h3" fontWeight="bold" color="white">
                    1M+
                  </Typography>
                  <Typography variant="body2" color={alpha(theme.palette.common.white, 0.7)}>
                    Active Players
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="h3" fontWeight="bold" color="white">
                    $10M+
                  </Typography>
                  <Typography variant="body2" color={alpha(theme.palette.common.white, 0.7)}>
                    NFT Trading Volume
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="h3" fontWeight="bold" color="white">
                    24/7
                  </Typography>
                  <Typography variant="body2" color={alpha(theme.palette.common.white, 0.7)}>
                    Support Available
                  </Typography>
                </Grid>
              </Grid>
            </motion.div>
          </CTAContent>
        </motion.div>
      </Container>
    </CTAContainer>
  );
};

export default CallToAction;
