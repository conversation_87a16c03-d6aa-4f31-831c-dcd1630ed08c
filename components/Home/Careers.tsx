import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Grid,
  Box,
  Typography,
  TextField,
  CircularProgress,
  useTheme,
  alpha
} from "@mui/material";
import { styled } from "@mui/material/styles";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import WorkIcon from '@mui/icons-material/Work';
import DropdownZone from "./DropdownZone";
import { toast } from 'react-toastify';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

// Styled components for better performance
const SectionContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  padding: theme.spacing(10, 0),
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
}));

// Removed unused StyledCardMedia component

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  width: "100%",
  height: '100%',
  position: "relative",
  background: "none",
  boxShadow: "none",
  overflow: 'hidden',
}));

const TitleBox = styled(Box)(({ theme }) => ({
  color: theme.palette.secondary.main,
  marginBottom: theme.spacing(2),
  fontSize: "1rem",
  fontWeight: 600,
  fontFamily: "Exo",
  width: "156px",
  display: "flex",
  alignItems: "center",
  "&:before": {
    content: "''",
    flexGrow: 1,
    background: theme.palette.secondary.main,
    height: "2px",
    fontSize: "0px",
    lineHeight: "0px",
    marginRight: theme.spacing(2),
    marginBottom: "3px",
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: "0.875rem",
    width: "120px",
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(6),
    "&:before": {
      marginRight: theme.spacing(1),
    }
  }
}));

const HighlightSpan = styled('span')(({ theme }) => ({
  position: 'relative',
  paddingRight: theme.spacing(3),
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '5px',
    left: '0',
    width: '100%',
    height: '30%',
    backgroundColor: alpha(theme.palette.primary.main, 0.3),
    zIndex: -1,
    borderRadius: '4px',
  }
}));

const MainContentBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(10, 0),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: '10%',
    width: '80%',
    height: '1px',
    background: `linear-gradient(90deg, transparent 0%, ${theme.palette.secondary.main} 50%, transparent 100%)`,
  },
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(6, 0),
  }
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& label": {
    fontFamily: "Exo",
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "1.125rem",
    lineHeight: "150%",
    color: theme.palette.text.secondary,
    transition: 'all 0.2s ease-in-out',
  },
  "& label.Mui-focused": {
    color: theme.palette.primary.main,
  },
  "& .MuiInput-underline:before": {
    borderBottomColor: alpha(theme.palette.secondary.main, 0.5),
    transition: 'all 0.2s ease-in-out',
  },
  "& .MuiInput-underline:hover:not(.Mui-disabled):before": {
    borderBottomColor: theme.palette.secondary.main,
  },
  "& .MuiInputBase-input": {
    color: theme.palette.text.primary,
    padding: theme.spacing(1, 0),
  },
  "& .MuiInput-underline:after": {
    borderBottomColor: theme.palette.primary.main,
  },
  width: "100%",
  marginBottom: theme.spacing(3),
}));

const DropzoneLabel = styled(Typography)(({ theme }) => ({
  fontFamily: 'Exo',
  fontStyle: "normal",
  fontWeight: 500,
  fontSize: "1.125rem",
  lineHeight: "150%",
  color: theme.palette.text.secondary,
  marginTop: theme.spacing(3),
  marginBottom: theme.spacing(2),
}));

const ConfirmImage = styled('img')(({ theme }) => ({
  filter: `drop-shadow(0 16px 24px ${alpha(theme.palette.common.black, 0.3)})`,
  marginBottom: theme.spacing(4),
  maxWidth: '80%',
  height: 'auto',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'scale(1.05)',
  }
}));

const SubmittedText = styled(Typography)(({ theme }) => ({
  fontFamily: 'Exo',
  fontStyle: "normal",
  fontWeight: 800,
  fontSize: "1.5rem",
  lineHeight: "130%",
  textAlign: "center",
  color: theme.palette.text.primary,
  marginBottom: theme.spacing(4),
}));

const SendAgainButton = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  fontFamily: 'Exo',
  justifyContent: "center",
  fontStyle: "normal",
  fontWeight: 600,
  fontSize: "1rem",
  lineHeight: "24px",
  color: theme.palette.secondary.main,
  borderBottom: `1px solid ${theme.palette.secondary.main}`,
  paddingBottom: theme.spacing(0.5),
  cursor: "pointer",
  transition: "all 0.3s ease",
  "&:hover": {
    transform: "translateY(-3px)",
    color: theme.palette.secondary.light,
    borderBottomColor: theme.palette.secondary.light,
  },
  "& svg": {
    marginRight: theme.spacing(1),
    transition: "transform 0.3s ease",
  },
  "&:hover svg": {
    transform: "translateX(-3px)",
  }
}));

interface FormErrors {
  name: boolean;
  lastname: boolean;
  email: boolean;
  cv: boolean;
}

const Careers: React.FC = () => {
  const theme = useTheme();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // State management with React hooks
  const [submitted, setSubmitted] = useState<boolean>(false);
  const [name, setName] = useState<string>('');
  const [lastname, setLastname] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [cv, setCv] = useState<File | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({
    name: false,
    lastname: false,
    email: false,
    cv: false
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const imageVariants = {
    hidden: { scale: 0.9, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  // Handle file drop from DropdownZone component
  const handleDrop = useCallback((file: File) => {
    setCv(file);
    setFormErrors(prev => ({ ...prev, cv: false }));
  }, []);

  // Validate email format
  const validateEmail = (email: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Form submission handler
  const submit = async (): Promise<void> => {
    // Form validation
    const errors: FormErrors = {
      name: !name,
      lastname: !lastname,
      email: !email || !validateEmail(email),
      cv: !cv
    };

    setFormErrors(errors);

    if (Object.values(errors).some(error => error)) {
      toast.error(`Please fill all required fields correctly.`);
      return;
    }

    // Create form data for submission
    const formData = new FormData();
    formData.append('name', name);
    formData.append('lastname', lastname);
    formData.append('email', email);
    if (cv) {
      formData.append('cv', cv);
    }

    setUploading(true);

    try {
      // Simulate API call for demo purposes
      setTimeout(() => {
        toast.success(`Your application has been successfully submitted!`);
        setUploading(false);
        setSubmitted(true);
      }, 2000);

      // In a real application, you would use the following code:
      /*
      const { data } = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/register`, formData, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          'Content-Type': 'multipart/form-data'
        }
      });

      if (data.success) {
        toast.success(`Your application has been successfully submitted!`);
        setUploading(false);
        setSubmitted(true);
      } else {
        setUploading(false);
        toast.error(data.message || `Submission failed. Please try again.`);
      }
      */
    } catch (err) {
      setUploading(false);
      toast.error(`An error occurred. Please try again later.`);
      console.error("Form submission error:", err);
    }
  };

  // Reset form to submit again
  const sendAgain = (): void => {
    setSubmitted(false);
    setName('');
    setLastname('');
    setEmail('');
    setCv(null);
    setFormErrors({
      name: false,
      lastname: false,
      email: false,
      cv: false
    });
  };

  return (
    <SectionContainer ref={ref}>
      {/* Background elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          left: '-10%',
          width: '25%',
          height: '40%',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 70%)`,
          filter: 'blur(60px)',
          zIndex: 0,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '10%',
          right: '-5%',
          width: '20%',
          height: '30%',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.secondary.main, 0.15)} 0%, transparent 70%)`,
          filter: 'blur(60px)',
          zIndex: 0,
        }}
      />

      <MainContentBox>
        <Container maxWidth="lg">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
          >
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <WorkIcon sx={{ color: theme.palette.secondary.main, mr: 1 }} />
                    <TitleBox>CAREERS</TitleBox>
                  </Box>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <Typography
                    variant="h2"
                    sx={{
                      fontWeight: 800,
                      mb: 3,
                      fontSize: { xs: "2.5rem", sm: "3.5rem" }
                    }}
                  >
                    Are you the<br /> <HighlightSpan>chosen one!?</HighlightSpan>
                  </Typography>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontSize: { xs: "1rem", sm: "1.125rem" },
                      color: theme.palette.text.secondary,
                      fontWeight: "normal",
                      mb: 4,
                      lineHeight: 1.6
                    }}
                  >
                    Do you have a passion for gaming or blockchain and are ready to join a tight knit team in revolutionizing the gaming space? Think you have some value you can bring to the gamestorme ecosystem? If so, we would love to hear from you! Come and join our team of talented artists, developers, engineers, marketers, writers, and legal experts.
                  </Typography>
                </motion.div>

                <motion.div variants={itemVariants} style={{ width: '100%' }}>
                  {submitted ? (
                    <Box textAlign='center'>
                      <ConfirmImage src="confirm.png" alt="Application submitted" />
                      <SubmittedText>Your application has been successfully submitted!</SubmittedText>
                      <Box display="flex" justifyContent="center">
                        <SendAgainButton onClick={sendAgain}>
                          <ArrowBackIcon />
                          Send again
                        </SendAgainButton>
                      </Box>
                    </Box>
                  ) : (
                    <Box>
                      {uploading ? (
                        <Box
                          height="400px"
                          display="flex"
                          flexDirection="column"
                          justifyContent="center"
                          alignItems="center"
                          gap={3}
                        >
                          <CircularProgress size={80} color="secondary" />
                          <Typography variant="h6" color="text.secondary">
                            Submitting your application...
                          </Typography>
                        </Box>
                      ) : (
                        <>
                          <StyledTextField
                            id="name-input"
                            variant="standard"
                            type="text"
                            label="First name"
                            value={name}
                            onChange={(e) => {
                              setName(e.target.value);
                              setFormErrors(prev => ({ ...prev, name: false }));
                            }}
                            error={formErrors.name}
                            helperText={formErrors.name ? "First name is required" : ""}
                            fullWidth
                            margin="normal"
                          />
                          <StyledTextField
                            id="lastname-input"
                            variant="standard"
                            type="text"
                            label="Last name"
                            value={lastname}
                            onChange={(e) => {
                              setLastname(e.target.value);
                              setFormErrors(prev => ({ ...prev, lastname: false }));
                            }}
                            error={formErrors.lastname}
                            helperText={formErrors.lastname ? "Last name is required" : ""}
                            fullWidth
                            margin="normal"
                          />
                          <StyledTextField
                            id="email-input"
                            variant="standard"
                            type="email"
                            label="E-mail"
                            value={email}
                            onChange={(e) => {
                              setEmail(e.target.value);
                              setFormErrors(prev => ({ ...prev, email: false }));
                            }}
                            error={formErrors.email}
                            helperText={formErrors.email ? "Valid email is required" : ""}
                            fullWidth
                            margin="normal"
                          />
                          <DropzoneLabel variant="subtitle1">
                            Attach your CV / Or Portfolio Files
                          </DropzoneLabel>
                          <DropdownZone
                            handleDrop={handleDrop}
                          />
                          {formErrors.cv && (
                            <Typography
                              color="error"
                              variant="caption"
                              sx={{ display: 'block', mt: -4, mb: 2 }}
                            >
                              Please attach your CV or portfolio
                            </Typography>
                          )}
                          <Box sx={{ mt: 4 }}>
                            <Button
                              variant="contained"
                              color="primary"
                              size="large"
                              onClick={submit}
                              sx={{
                                width: { xs: "100%", sm: "auto" }
                              }}
                            >
                              Submit Application
                            </Button>
                          </Box>
                        </>
                      )}
                    </Box>
                  )}
                </motion.div>
              </Grid>

              <Grid item xs={12} md={6}>
                <motion.div variants={imageVariants}>
                  <StyledCard>
                    <Box
                      component="img"
                      src="dream-come.png"
                      alt="Gamestorme Careers"
                      sx={{
                        width: '100%',
                        height: 'auto',
                        borderRadius: theme.shape.borderRadius * 2,
                        boxShadow: `0 20px 40px ${alpha(theme.palette.common.black, 0.3)}`,
                        transition: "transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)",
                        '&:hover': {
                          transform: 'scale(1.03)',
                          boxShadow: `0 30px 60px ${alpha(theme.palette.common.black, 0.4)}, 0 0 20px ${alpha(theme.palette.primary.main, 0.4)}`,
                        }
                      }}
                    />
                  </StyledCard>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </MainContentBox>
    </SectionContainer>
  );
};

export default Careers;
