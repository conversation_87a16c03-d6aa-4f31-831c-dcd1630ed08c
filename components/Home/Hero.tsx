import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  useTheme,
  alpha,
  useMediaQuery,
  Tooltip,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';
import Link from 'next/link';
import DownloadIcon from '@mui/icons-material/Download';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import DownloadLauncher from '../DownloadLauncher';

// Enhanced Styled components
const HeroContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  overflow: 'hidden',
  paddingTop: theme.spacing(20),
  paddingBottom: theme.spacing(20),
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `radial-gradient(circle at 30% 20%, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 50%),
                 radial-gradient(circle at 70% 80%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 50%),
                 radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.light, 0.08)} 0%, transparent 50%)`,
    zIndex: -1,
  },
  [theme.breakpoints.down('md')]: {
    paddingTop: theme.spacing(15),
    paddingBottom: theme.spacing(15),
    minHeight: 'auto',
  },
}));

const HeroContent = styled(Box)(({ theme }) => ({
  position: 'relative',
  zIndex: 3,
}));

const HeroTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 900,
  marginBottom: theme.spacing(3),
  background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.light} 50%, ${theme.palette.secondary.main} 100%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  textShadow: `0 0 60px ${alpha(theme.palette.primary.main, 0.4)}`,
  lineHeight: 1.1,
  [theme.breakpoints.down('md')]: {
    fontSize: '3.5rem',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '2.8rem',
  },
}));

const HeroSubtitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(5),
  maxWidth: 650,
  lineHeight: 1.6,
  color: theme.palette.text.secondary,
  fontSize: '1.3rem',
  [theme.breakpoints.down('md')]: {
    fontSize: '1.2rem',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.1rem',
  },
}));

const HeroButtonGroup = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(3),
  flexWrap: 'wrap',
  marginBottom: theme.spacing(6),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: theme.spacing(2),
  },
}));

const HeroImageContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const FloatingElement = styled(motion.div)(({ theme }) => ({
  position: 'absolute',
  borderRadius: '50%',
  filter: 'blur(100px)',
  zIndex: 1,
}));

const ParticleContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  pointerEvents: 'none',
  zIndex: 2,
}));

const Particle = styled(motion.div)(({ theme }) => ({
  position: 'absolute',
  width: '4px',
  height: '4px',
  background: theme.palette.primary.main,
  borderRadius: '50%',
  boxShadow: `0 0 10px ${theme.palette.primary.main}`,
}));

const StatsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(6),
  alignItems: 'center',
  flexWrap: 'wrap',
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(4),
    justifyContent: 'center',
  },
}));

const StatItem = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  [theme.breakpoints.down('sm')]: {
    minWidth: '120px',
  },
}));

const Hero: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [downloadDialogOpen, setDownloadDialogOpen] = useState(false);
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number; delay: number }>>([]);

  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Generate particles
  useEffect(() => {
    const particleArray = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5,
    }));
    setParticles(particleArray);
  }, []);

  // Enhanced Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  const titleVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 1,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  const imageVariants = {
    hidden: { scale: 0.8, opacity: 0, rotateY: -15 },
    visible: {
      scale: 1,
      opacity: 1,
      rotateY: 0,
      transition: {
        duration: 1.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-20, 20, -20],
      x: [-10, 10, -10],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  const particleVariants = {
    animate: {
      y: [0, -100, -200],
      opacity: [0, 1, 0],
      scale: [0, 1, 0],
      transition: {
        duration: 8,
        repeat: Infinity,
        ease: "linear",
      },
    },
  };

  return (
    <HeroContainer ref={ref}>
      {/* Enhanced Background elements */}
      <FloatingElement
        variants={floatingVariants}
        animate="animate"
        style={{
          width: '50%',
          height: '50%',
          top: '5%',
          left: '0%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.3)} 0%, ${alpha(theme.palette.primary.light, 0.1)} 40%, transparent 70%)`,
        }}
      />
      <FloatingElement
        variants={floatingVariants}
        animate="animate"
        style={{
          width: '40%',
          height: '40%',
          bottom: '5%',
          right: '0%',
          background: `radial-gradient(circle, ${alpha(theme.palette.secondary.main, 0.25)} 0%, ${alpha(theme.palette.secondary.light, 0.1)} 40%, transparent 70%)`,
          animationDelay: '2s',
        }}
      />
      <FloatingElement
        variants={floatingVariants}
        animate="animate"
        style={{
          width: '35%',
          height: '35%',
          top: '40%',
          right: '10%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.light, 0.2)} 0%, transparent 60%)`,
          animationDelay: '4s',
        }}
      />

      {/* Particle System */}
      <ParticleContainer>
        {particles.map((particle) => (
          <Particle
            key={particle.id}
            variants={particleVariants}
            animate="animate"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              animationDelay: `${particle.delay}s`,
            }}
          />
        ))}
      </ParticleContainer>

      <Container maxWidth="lg">
        <Grid container spacing={6} alignItems="center">
          <Grid item xs={12} md={6}>
            <HeroContent>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate={inView ? "visible" : "hidden"}
              >
                <motion.div variants={itemVariants}>
                  <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Chip
                      icon={<AutoAwesomeIcon />}
                      label="AI-Powered"
                      variant="outlined"
                      sx={{
                        borderColor: theme.palette.primary.main,
                        color: theme.palette.primary.light,
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        backdropFilter: 'blur(10px)',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.2),
                        }
                      }}
                    />
                    <Chip
                      icon={<RocketLaunchIcon />}
                      label="Next-Gen Platform"
                      variant="outlined"
                      sx={{
                        borderColor: theme.palette.secondary.main,
                        color: theme.palette.secondary.light,
                        backgroundColor: alpha(theme.palette.secondary.main, 0.1),
                        backdropFilter: 'blur(10px)',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.secondary.main, 0.2),
                        }
                      }}
                    />
                  </Box>
                </motion.div>

                <motion.div variants={titleVariants}>
                  <HeroTitle variant="h1">
                    The Future of Gaming is Here
                  </HeroTitle>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <HeroSubtitle>
                    Empowering Game Developers Through Innovative Solutions. Our digital game store features AI-powered marketing tools to boost game sales, with blockchain technology enhancing the experience.
                  </HeroSubtitle>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <HeroButtonGroup>
                    <Link href="/marketing-ai" passHref style={{ textDecoration: 'none' }}>
                      <Button
                        variant="contained"
                        color="primary"
                        size="large"
                        startIcon={<AutoAwesomeIcon />}
                        sx={{
                          minWidth: '200px',
                          height: '56px',
                          fontSize: '1.1rem',
                        }}
                      >
                        Try AI Marketing
                      </Button>
                    </Link>
                    <Link href="/games" passHref style={{ textDecoration: 'none' }}>
                      <Button
                        variant="outlined"
                        color="secondary"
                        size="large"
                        startIcon={<TrendingUpIcon />}
                        sx={{
                          minWidth: '180px',
                          height: '56px',
                          fontSize: '1.1rem',
                        }}
                      >
                        Explore Games
                      </Button>
                    </Link>
                    <Tooltip title="Download our desktop launcher for the best gaming experience" arrow>
                      <Button
                        variant="outlined"
                        color="primary"
                        size="large"
                        startIcon={<DownloadIcon />}
                        onClick={() => setDownloadDialogOpen(true)}
                        sx={{
                          minWidth: '200px',
                          height: '56px',
                          fontSize: '1.1rem',
                          borderColor: alpha(theme.palette.primary.main, 0.5),
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.1),
                            borderColor: theme.palette.primary.main,
                          }
                        }}
                      >
                        Download Launcher
                      </Button>
                    </Tooltip>
                  </HeroButtonGroup>
                </motion.div>

                <motion.div
                  variants={itemVariants}
                  style={{ marginTop: theme.spacing(6) }}
                >
                  <StatsContainer>
                    <StatItem>
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Typography
                          variant="h2"
                          fontWeight="bold"
                          sx={{
                            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            mb: 1,
                          }}
                        >
                          AI
                        </Typography>
                        <Typography variant="body1" color="text.secondary" fontWeight={500}>
                          Marketing Tools
                        </Typography>
                      </motion.div>
                    </StatItem>
                    <StatItem>
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Typography
                          variant="h2"
                          fontWeight="bold"
                          sx={{
                            background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.light} 100%)`,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            mb: 1,
                          }}
                        >
                          500+
                        </Typography>
                        <Typography variant="body1" color="text.secondary" fontWeight={500}>
                          Games Available
                        </Typography>
                      </motion.div>
                    </StatItem>
                    <StatItem>
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Typography
                          variant="h2"
                          fontWeight="bold"
                          sx={{
                            background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.light} 100%)`,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            mb: 1,
                          }}
                        >
                          24/7
                        </Typography>
                        <Typography variant="body1" color="text.secondary" fontWeight={500}>
                          Developer Support
                        </Typography>
                      </motion.div>
                    </StatItem>
                  </StatsContainer>
                </motion.div>
              </motion.div>
            </HeroContent>
          </Grid>

          <Grid item xs={12} md={6}>
            <HeroImageContainer>
              <motion.div
                variants={imageVariants}
                initial="hidden"
                animate={inView ? "visible" : "hidden"}
                style={{
                  position: 'relative',
                  width: '100%',
                  height: isMobile ? '400px' : '600px',
                }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    width: '100%',
                    height: '100%',
                    borderRadius: '24px',
                    overflow: 'hidden',
                    border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                    boxShadow: `0 30px 100px ${alpha(theme.palette.primary.main, 0.4)},
                               0 0 60px ${alpha(theme.palette.primary.glow, 0.6)},
                               inset 0 1px 0 ${alpha('#fff', 0.1)}`,
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-10px) rotateY(5deg)',
                      borderColor: alpha(theme.palette.primary.light, 0.6),
                      boxShadow: `0 40px 120px ${alpha(theme.palette.primary.main, 0.5)},
                                 0 0 80px ${alpha(theme.palette.primary.glow, 0.8)},
                                 inset 0 1px 0 ${alpha('#fff', 0.2)}`,
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      background: `linear-gradient(135deg, ${alpha(theme.palette.background.glass, 0.1)} 0%, ${alpha(
                        theme.palette.background.paper,
                        0.2
                      )} 100%)`,
                      backdropFilter: 'blur(2px)',
                      zIndex: 1,
                      borderRadius: '22px',
                    },
                  }}
                >
                  <Image
                    src="/assets/images/support/Gamestorme Experience.jpg"
                    alt="Gaming Experience"
                    fill
                    style={{
                      objectFit: 'cover',
                      borderRadius: '22px',
                    }}
                    priority
                  />

                  {/* Enhanced Floating game elements */}
                  <motion.div
                    initial={{ y: 30, opacity: 0, scale: 0.8 }}
                    animate={{ y: 0, opacity: 1, scale: 1 }}
                    transition={{ delay: 1.2, duration: 1 }}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    style={{
                      position: 'absolute',
                      top: '8%',
                      right: '8%',
                      zIndex: 3,
                    }}
                  >
                    <Box
                      sx={{
                        border: `2px solid ${alpha(theme.palette.primary.main, 0.4)}`,
                        borderRadius: '16px',
                        overflow: 'hidden',
                        boxShadow: `0 15px 30px ${alpha('#000', 0.4)}, 0 0 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          borderColor: alpha(theme.palette.primary.light, 0.8),
                          boxShadow: `0 20px 40px ${alpha('#000', 0.5)}, 0 0 30px ${alpha(theme.palette.primary.main, 0.5)}`,
                        }
                      }}
                    >
                      <Image
                        src="/assets/images/games/agueybana.jpg"
                        alt="Agueybana Game"
                        width={90}
                        height={90}
                        style={{
                          objectFit: 'cover',
                          display: 'block',
                        }}
                      />
                    </Box>
                  </motion.div>

                  <motion.div
                    initial={{ y: -30, opacity: 0, scale: 0.8 }}
                    animate={{ y: 0, opacity: 1, scale: 1 }}
                    transition={{ delay: 1.5, duration: 1 }}
                    whileHover={{ scale: 1.1, rotate: -5 }}
                    style={{
                      position: 'absolute',
                      bottom: '12%',
                      left: '8%',
                      zIndex: 3,
                    }}
                  >
                    <Box
                      sx={{
                        border: `2px solid ${alpha(theme.palette.secondary.main, 0.4)}`,
                        borderRadius: '16px',
                        overflow: 'hidden',
                        boxShadow: `0 15px 30px ${alpha('#000', 0.4)}, 0 0 20px ${alpha(theme.palette.secondary.main, 0.3)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          borderColor: alpha(theme.palette.secondary.light, 0.8),
                          boxShadow: `0 20px 40px ${alpha('#000', 0.5)}, 0 0 30px ${alpha(theme.palette.secondary.main, 0.5)}`,
                        }
                      }}
                    >
                      <Image
                        src="/assets/images/games/guaramania.png"
                        alt="Guaramania Game"
                        width={110}
                        height={110}
                        style={{
                          objectFit: 'cover',
                          display: 'block',
                        }}
                      />
                    </Box>
                  </motion.div>

                  {/* Additional floating element */}
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 1.8, duration: 0.8 }}
                    whileHover={{ scale: 1.2 }}
                    style={{
                      position: 'absolute',
                      top: '50%',
                      right: '5%',
                      zIndex: 3,
                    }}
                  >
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)} 0%, ${alpha(theme.palette.primary.light, 0.9)} 100%)`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: `0 10px 20px ${alpha(theme.palette.primary.main, 0.4)}`,
                        border: `2px solid ${alpha('#fff', 0.2)}`,
                      }}
                    >
                      <AutoAwesomeIcon sx={{ color: '#fff', fontSize: 28 }} />
                    </Box>
                  </motion.div>
                </Box>
              </motion.div>
            </HeroImageContainer>
          </Grid>
        </Grid>
      </Container>

      {/* Download Launcher Dialog */}
      <DownloadLauncher
        open={downloadDialogOpen}
        onClose={() => setDownloadDialogOpen(false)}
      />
    </HeroContainer>
  );
};

export default Hero;
