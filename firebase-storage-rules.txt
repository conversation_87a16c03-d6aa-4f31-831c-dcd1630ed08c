rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Game assets (images, screenshots, etc.)
    match /games/{gameId}/{allPaths=**} {
      // Anyone can read game assets
      allow read: if true;
      
      // Authenticated users can upload game assets
      allow write: if request.auth != null;
    }
    
    // News article images
    match /news/{articleId}/{allPaths=**} {
      // Anyone can read news images
      allow read: if true;
      
      // Only admins and editors can upload news images
      allow write: if request.auth != null && 
                      (request.auth.token.admin == true || 
                       request.auth.token.editor == true);
    }
    
    // User profile images and uploads
    match /users/{userId}/{allPaths=**} {
      // Users can read and write their own files
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Public read access to profile images
      allow read: if true;
    }
    
    // Developer assets (logos, banners, etc.)
    match /developers/{developerId}/{allPaths=**} {
      // Anyone can read developer assets
      allow read: if true;
      
      // Developers can upload their own assets
      allow write: if request.auth != null && request.auth.uid == developerId;
      
      // Admins have full access
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Public assets (site images, icons, etc.)
    match /public/{allPaths=**} {
      // Anyone can read public assets
      allow read: if true;
      
      // Only admins can manage public assets
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Launcher files (for download)
    match /launchers/{filename} {
      // Public read access for downloads
      allow read: if true;
      
      // Only admins can upload launcher files
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Temporary uploads
    match /temp/{userId}/{allPaths=**} {
      // Users can upload temporary files
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Auto-delete after 24 hours (handled by Cloud Functions)
    }
    
    // Game downloads (if storing game files)
    match /downloads/{gameId}/{filename} {
      // Authenticated users can download games
      allow read: if request.auth != null;

      // Developers and admins can upload game files
      allow write: if request.auth != null &&
                      (request.auth.token.admin == true ||
                       request.auth.token.developer == true);
    }

    // Game uploads (for developer uploads)
    match /game-uploads/{developerId}/{gameId}/{allPaths=**} {
      // Developers can read their own uploads
      allow read: if request.auth != null && request.auth.uid == developerId;

      // Developers can upload their own game files
      allow write: if request.auth != null &&
                      request.auth.uid == developerId &&
                      request.resource.size < 500 * 1024 * 1024; // 500MB limit

      // Admins have full access
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }

    // Support attachments
    match /support/{ticketId}/{allPaths=**} {
      // Ticket owner and admins can read
      allow read: if request.auth != null;

      // Authenticated users can upload support files
      allow write: if request.auth != null &&
                      request.resource.size < 25 * 1024 * 1024; // 25MB limit
    }

    // Creator content uploads
    match /creator-content/{creatorId}/{allPaths=**} {
      // Public read access
      allow read: if true;

      // Creators can upload their content
      allow write: if request.auth != null &&
                      request.auth.uid == creatorId &&
                      request.resource.size < 50 * 1024 * 1024; // 50MB limit

      // Admins have full access
      allow write: if request.auth != null && request.auth.token.admin == true;
    }

    // User avatars and profile images
    match /avatars/{userId}/{allPaths=**} {
      // Public read access
      allow read: if true;

      // Users can upload their own avatars
      allow write: if request.auth != null &&
                      request.auth.uid == userId &&
                      request.resource.size < 5 * 1024 * 1024 && // 5MB limit
                      request.resource.contentType.matches('image/.*');
    }

    // Screenshots and media
    match /screenshots/{gameId}/{allPaths=**} {
      // Public read access
      allow read: if true;

      // Authenticated users can upload screenshots
      allow write: if request.auth != null &&
                      request.resource.size < 10 * 1024 * 1024 && // 10MB limit
                      request.resource.contentType.matches('image/.*');
    }

    // Backup files
    match /backups/{allPaths=**} {
      // Only admins can access backups
      allow read, write: if request.auth != null && request.auth.token.admin == true;
    }

    // Default rule for any other paths
    match /{allPaths=**} {
      // Authenticated users can read
      allow read: if request.auth != null;

      // Only admins can write to unspecified paths
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
  }
}
