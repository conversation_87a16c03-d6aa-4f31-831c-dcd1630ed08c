# 🧠 Stormie AI Marketing Platform Setup Guide

## Overview
Stormie is GameStorme's AI-powered marketing assistant that provides real-time, intelligent marketing solutions for game developers. Built with open-source AI models and deployed on Heroku.

## 🚀 Features

### Core AI Capabilities
- **Game Analysis**: Comprehensive market positioning and competitive analysis
- **ASO Keywords**: Intelligent app store optimization keyword generation
- **Social Content**: Platform-specific social media content creation
- **Pricing Strategy**: Data-driven pricing recommendations
- **Market Insights**: Real-time market trends and predictions

### Interactive Chat Interface
- **Stormie Chat**: ChatGPT-like conversational AI interface
- **Real-time Responses**: Instant AI-powered marketing advice
- **Context Awareness**: Remembers game details across conversations
- **Multi-format Output**: Text, data visualizations, and actionable insights

## 🛠️ Technical Architecture

### Backend Stack
- **Framework**: Next.js API Routes
- **AI Engine**: Hugging Face Inference API (Open Source)
- **Models**: Microsoft DialoGPT-medium, BART, RoBERTa
- **Deployment**: Heroku-optimized

### Frontend Stack
- **UI Framework**: React + Material-UI
- **Animations**: Framer Motion
- **State Management**: React Hooks
- **Real-time Updates**: WebSocket-ready architecture

## 📦 Installation & Setup

### 1. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local

# Add your API keys
HUGGINGFACE_API_KEY=your_huggingface_token
NEXT_PUBLIC_FIREBASE_PROJECT_ID=gamestorme-faf42
```

### 2. Install Dependencies
```bash
# Install Stormie AI dependencies
npm install @huggingface/inference
npm install cors express helmet express-rate-limit
npm install dotenv

# Development dependencies
npm install --save-dev @types/cors @types/express
```

### 3. Hugging Face Setup
1. Create account at [huggingface.co](https://huggingface.co)
2. Generate API token in Settings > Access Tokens
3. Add token to `.env.local` as `HUGGINGFACE_API_KEY`

### 4. Start Development Server
```bash
npm run dev
```

## 🎯 API Endpoints

### Game Analysis
```typescript
POST /api/stormie/analyze-game
{
  "gameData": {
    "title": "Your Game Title",
    "genre": "Action",
    "description": "Game description",
    "platform": "Mobile",
    "targetAudience": "Core gamers 18-35"
  }
}
```

### ASO Keywords
```typescript
POST /api/stormie/aso-keywords
{
  "gameData": {
    "title": "Your Game Title",
    "genre": "Puzzle",
    "description": "Game description"
  }
}
```

### Social Content
```typescript
POST /api/stormie/social-content
{
  "gameData": { /* game details */ },
  "platform": "twitter", // twitter, instagram, facebook, tiktok, linkedin
  "contentType": "post" // post, story, ad, announcement
}
```

### Pricing Strategy
```typescript
POST /api/stormie/pricing-strategy
{
  "gameData": { /* game details */ },
  "marketData": { /* optional market context */ }
}
```

### Market Insights
```typescript
POST /api/stormie/market-insights
{
  "gameData": { /* game details */ }
}
```

## 🎮 Usage Guide

### Accessing Stormie AI
1. Navigate to Developer Dashboard
2. Click "Marketing AI" in sidebar
3. Access Stormie through multiple interfaces:
   - **AI Features Tab**: Quick access to specific tools
   - **Stormie Chat**: Conversational interface
   - **Game Projects**: Project-specific analysis

### Using Stormie Chat
1. Click "Stormie Chat" feature card or chat button
2. Describe your game or ask marketing questions
3. Stormie will analyze and provide:
   - Market positioning advice
   - ASO keyword suggestions
   - Social media content
   - Pricing recommendations
   - Market insights

### Example Conversations
```
User: "Analyze my puzzle game called 'Mind Maze' for mobile platforms"
Stormie: "I'll analyze Mind Maze for you! Here's what I found..."

User: "Generate Twitter content for my space exploration game"
Stormie: "Here's engaging Twitter content for your space game..."

User: "What's the best pricing strategy for a premium RPG?"
Stormie: "Based on RPG market analysis, here's my recommendation..."
```

## 🔧 Customization

### Adding New AI Models
```typescript
// In api/services/stormieAI.js
const models = {
  textGeneration: 'microsoft/DialoGPT-medium',
  sentiment: 'cardiffnlp/twitter-roberta-base-sentiment-latest',
  classification: 'facebook/bart-large-mnli',
  // Add your custom model
  customModel: 'your-model-name'
};
```

### Extending Chat Capabilities
```typescript
// In components/StormieAI/StormieChat.tsx
const processStormieRequest = async (input: string) => {
  // Add new intent detection
  if (input.includes('your-new-feature')) {
    return await handleNewFeature(input);
  }
  // ... existing logic
};
```

## 🚀 Deployment

### Heroku Deployment
```bash
# Ensure environment variables are set
heroku config:set HUGGINGFACE_API_KEY=your_token

# Deploy
git push heroku main
```

### Environment Variables for Production
```bash
# Required for Stormie AI
HUGGINGFACE_API_KEY=your_production_token
NODE_ENV=production

# Optional performance optimization
AI_CACHE_DURATION=3600
AI_RATE_LIMIT=100
```

## 📊 Performance & Monitoring

### Response Times
- **Game Analysis**: 2-5 seconds
- **Keyword Generation**: 1-3 seconds
- **Social Content**: 1-2 seconds
- **Market Insights**: 3-6 seconds

### Rate Limiting
- **Default**: 100 requests per 15 minutes per IP
- **Authenticated**: 500 requests per hour per user
- **Premium**: Unlimited (future feature)

### Error Handling
- Automatic fallback to pre-generated responses
- Graceful degradation when AI services are unavailable
- User-friendly error messages with retry options

## 🔮 Future Enhancements

### Planned Features
- **Voice Integration**: Voice commands for Stormie
- **Image Analysis**: Screenshot and artwork analysis
- **Competitor Tracking**: Automated competitor monitoring
- **A/B Testing**: Built-in marketing test framework
- **Multi-language**: Support for global markets

### AI Model Upgrades
- **GPT-4 Integration**: Premium tier with advanced models
- **Custom Training**: Game-specific model fine-tuning
- **Real-time Learning**: Adaptive responses based on user feedback

## 🆘 Troubleshooting

### Common Issues

**Stormie not responding:**
```bash
# Check API key
echo $HUGGINGFACE_API_KEY

# Verify model availability
curl -H "Authorization: Bearer $HUGGINGFACE_API_KEY" \
  https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium
```

**Slow responses:**
- Check Hugging Face model status
- Verify network connectivity
- Consider upgrading to paid Hugging Face plan

**Rate limiting errors:**
- Implement request queuing
- Add user authentication
- Upgrade to premium Hugging Face tier

### Debug Mode
```bash
# Enable detailed logging
DEBUG=stormie:* npm run dev
```

## 📚 Resources

- [Hugging Face Documentation](https://huggingface.co/docs)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)
- [Material-UI Components](https://mui.com/components/)
- [GameStorme Developer Guide](./README.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/stormie-enhancement`
3. Test AI functionality thoroughly
4. Submit pull request with detailed description

---

**Stormie AI is ready to revolutionize game marketing! 🎮✨**
