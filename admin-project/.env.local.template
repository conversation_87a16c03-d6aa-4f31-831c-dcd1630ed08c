# 🔥 Gamestorme Admin Dashboard - Environment Variables
# Copy this file to .env.local and fill in your Firebase configuration

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDHz0X-6x78FuzQyGgIeKRy1nfNMw1-_F4
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=gamestorme-faf42.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=gamestorme-faf42
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=gamestorme-faf42.firebasestorage.app
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://gamestorme-faf42-default-rtdb.firebaseio.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=206243766870
NEXT_PUBLIC_FIREBASE_APP_ID=1:206243766870:web:01704242f816095f4711f7
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-CBM026VFVM

# Admin Configuration
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>
NEXT_PUBLIC_APP_NAME=Gamestorme Admin
NEXT_PUBLIC_APP_VERSION=1.0.0

# Security Configuration
NEXT_PUBLIC_ENABLE_SECURITY=true
NEXT_PUBLIC_DEBUG_MODE=false
