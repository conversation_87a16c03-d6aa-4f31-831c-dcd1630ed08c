<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gameGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gameElements" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="gameGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#gameGradient)"/>
  
  <!-- Game world landscape -->
  <g opacity="0.8">
    <!-- Mountains/terrain -->
    <polygon points="0,200 80,120 160,140 240,100 320,130 400,110 400,300 0,300" fill="url(#gameElements)" opacity="0.4"/>
    <polygon points="0,220 60,160 140,180 220,140 300,170 400,150 400,300 0,300" fill="url(#gameElements)" opacity="0.3"/>
    
    <!-- Buildings/structures -->
    <rect x="50" y="160" width="30" height="40" fill="#4ecdc4" opacity="0.7"/>
    <polygon points="50,160 65,140 80,160" fill="#ff6b6b" opacity="0.8"/>
    
    <rect x="150" y="140" width="40" height="60" fill="#45b7d1" opacity="0.7"/>
    <polygon points="150,140 170,120 190,140" fill="#4ecdc4" opacity="0.8"/>
    
    <rect x="280" y="130" width="35" height="70" fill="#ff6b6b" opacity="0.7"/>
    <polygon points="280,130 297.5,110 315,130" fill="#45b7d1" opacity="0.8"/>
  </g>
  
  <!-- Game character/avatar -->
  <g transform="translate(180, 180)" filter="url(#gameGlow)">
    <!-- Character body -->
    <circle cx="0" cy="0" r="15" fill="#4ecdc4"/>
    <rect x="-8" y="0" width="16" height="25" rx="3" fill="#45b7d1"/>
    
    <!-- Character details -->
    <circle cx="-3" cy="-5" r="2" fill="#1a1a2e"/>
    <circle cx="3" cy="-5" r="2" fill="#1a1a2e"/>
    <rect x="-2" y="2" width="4" height="2" fill="#ff6b6b"/>
    
    <!-- Weapon/tool -->
    <rect x="15" y="-2" width="20" height="4" rx="2" fill="#ff6b6b" opacity="0.8"/>
  </g>
  
  <!-- Game UI elements -->
  <g opacity="0.9">
    <!-- Health bar -->
    <rect x="20" y="20" width="100" height="8" rx="4" fill="#1a1a2e" opacity="0.8"/>
    <rect x="22" y="22" width="76" height="4" rx="2" fill="#4ecdc4"/>
    <text x="25" y="45" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12">HP: 76/100</text>
    
    <!-- Score -->
    <text x="300" y="30" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">SCORE: 15,420</text>
    
    <!-- Mini-map -->
    <rect x="320" y="50" width="60" height="60" rx="5" fill="#1a1a2e" opacity="0.8"/>
    <circle cx="350" cy="80" r="2" fill="#4ecdc4"/>
    <rect x="340" y="70" width="3" height="3" fill="#ff6b6b"/>
    <rect x="355" y="85" width="3" height="3" fill="#45b7d1"/>
  </g>
  
  <!-- Floating game elements -->
  <g opacity="0.7">
    <!-- Power-ups -->
    <circle cx="100" cy="100" r="5" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="cy" values="100;80;100" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="3s" repeatCount="indefinite"/>
    </circle>
    <polygon points="300,120 310,110 320,120 310,130" fill="#ff6b6b" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 310 120;360 310 120" dur="4s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Collectibles -->
    <circle cx="250" cy="80" r="3" fill="#45b7d1" opacity="0.8">
      <animate attributeName="cy" values="80;60;80" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Game title -->
  <text x="200" y="270" text-anchor="middle" fill="url(#gameElements)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#gameGlow)">
    ADVENTURE QUEST
  </text>
  <text x="200" y="290" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Level 5: Crystal Caverns
  </text>
</svg>
