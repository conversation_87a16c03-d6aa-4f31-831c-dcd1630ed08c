<svg width="900" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:0.9" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:0.7" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:0.3" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.3" />
    </linearGradient>
    <filter id="bgGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="gridPattern" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4ecdc4" stroke-width="0.5" opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- Base background -->
  <rect width="900" height="600" fill="url(#bgGradient)"/>
  
  <!-- Grid overlay -->
  <rect width="900" height="600" fill="url(#gridPattern)"/>
  
  <!-- Abstract geometric shapes -->
  <g opacity="0.4">
    <!-- Large background shapes -->
    <polygon points="0,0 200,0 150,150 0,100" fill="url(#accentGradient)"/>
    <polygon points="700,0 900,0 900,200 750,150" fill="url(#accentGradient)"/>
    <polygon points="0,400 150,450 100,600 0,600" fill="url(#accentGradient)"/>
    <polygon points="750,400 900,350 900,600 700,600" fill="url(#accentGradient)"/>
    
    <!-- Central design elements -->
    <circle cx="450" cy="300" r="100" fill="none" stroke="url(#accentGradient)" stroke-width="2" opacity="0.3"/>
    <circle cx="450" cy="300" r="150" fill="none" stroke="url(#accentGradient)" stroke-width="1" opacity="0.2"/>
    <circle cx="450" cy="300" r="200" fill="none" stroke="url(#accentGradient)" stroke-width="0.5" opacity="0.1"/>
  </g>
  
  <!-- Floating particles -->
  <g opacity="0.6">
    <circle cx="100" cy="100" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="100;80;100" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="800" cy="150" r="1.5" fill="#ff6b6b">
      <animate attributeName="cy" values="150;130;150" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="500" r="2.5" fill="#45b7d1">
      <animate attributeName="cy" values="500;480;500" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="700" cy="450" r="1.8" fill="#4ecdc4">
      <animate attributeName="cy" values="450;430;450" dur="4.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="300" cy="200" r="1.2" fill="#ff6b6b">
      <animate attributeName="cy" values="200;180;200" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="600" cy="350" r="2.2" fill="#45b7d1">
      <animate attributeName="cy" values="350;330;350" dur="4.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4.2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Gaming-themed elements -->
  <g opacity="0.3" filter="url(#bgGlow)">
    <!-- Controller shapes -->
    <g transform="translate(150, 250)">
      <rect x="0" y="10" width="40" height="20" rx="10" fill="#4ecdc4" opacity="0.4"/>
      <circle cx="10" cy="20" r="3" fill="#45b7d1"/>
      <circle cx="30" cy="20" r="3" fill="#ff6b6b"/>
    </g>
    
    <g transform="translate(700, 100)">
      <rect x="0" y="10" width="40" height="20" rx="10" fill="#ff6b6b" opacity="0.4"/>
      <circle cx="10" cy="20" r="3" fill="#4ecdc4"/>
      <circle cx="30" cy="20" r="3" fill="#45b7d1"/>
    </g>
    
    <!-- Pixel art elements -->
    <g transform="translate(400, 100)">
      <rect x="0" y="0" width="8" height="8" fill="#4ecdc4" opacity="0.5"/>
      <rect x="12" y="0" width="8" height="8" fill="#ff6b6b" opacity="0.5"/>
      <rect x="24" y="0" width="8" height="8" fill="#45b7d1" opacity="0.5"/>
      <rect x="0" y="12" width="8" height="8" fill="#45b7d1" opacity="0.5"/>
      <rect x="12" y="12" width="8" height="8" fill="#4ecdc4" opacity="0.5"/>
      <rect x="24" y="12" width="8" height="8" fill="#ff6b6b" opacity="0.5"/>
    </g>
    
    <g transform="translate(500, 450)">
      <rect x="0" y="0" width="6" height="6" fill="#4ecdc4" opacity="0.4"/>
      <rect x="8" y="0" width="6" height="6" fill="#ff6b6b" opacity="0.4"/>
      <rect x="16" y="0" width="6" height="6" fill="#45b7d1" opacity="0.4"/>
      <rect x="0" y="8" width="6" height="6" fill="#45b7d1" opacity="0.4"/>
      <rect x="8" y="8" width="6" height="6" fill="#4ecdc4" opacity="0.4"/>
      <rect x="16" y="8" width="6" height="6" fill="#ff6b6b" opacity="0.4"/>
    </g>
  </g>
  
  <!-- Energy waves -->
  <g opacity="0.2">
    <ellipse cx="450" cy="300" rx="300" ry="80" fill="none" stroke="#4ecdc4" stroke-width="1">
      <animate attributeName="rx" values="300;350;300" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.05;0.2" dur="6s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="450" cy="300" rx="250" ry="60" fill="none" stroke="#ff6b6b" stroke-width="1">
      <animate attributeName="rx" values="250;300;250" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.05;0.2" dur="5s" repeatCount="indefinite"/>
    </ellipse>
    <ellipse cx="450" cy="300" rx="200" ry="40" fill="none" stroke="#45b7d1" stroke-width="1">
      <animate attributeName="rx" values="200;250;200" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.05;0.2" dur="4s" repeatCount="indefinite"/>
    </ellipse>
  </g>
</svg>
