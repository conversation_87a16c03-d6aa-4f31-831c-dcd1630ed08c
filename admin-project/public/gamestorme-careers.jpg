<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="careersGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="50%" style="stop-color:#0f3460;stop-opacity:0.8" />
      <stop offset="75%" style="stop-color:#533483;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="careersAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="careersGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#careersGradient)"/>
  
  <!-- Office/workspace environment -->
  <g opacity="0.7">
    <!-- Desk setup -->
    <rect x="100" y="250" width="400" height="80" rx="10" fill="url(#careersAccent)" opacity="0.3"/>
    
    <!-- Multiple monitors -->
    <rect x="150" y="200" width="80" height="50" rx="5" fill="#1a1a2e" stroke="url(#careersAccent)" stroke-width="2"/>
    <rect x="155" y="205" width="70" height="40" rx="3" fill="url(#careersAccent)" opacity="0.4"/>
    
    <rect x="250" y="200" width="80" height="50" rx="5" fill="#1a1a2e" stroke="url(#careersAccent)" stroke-width="2"/>
    <rect x="255" y="205" width="70" height="40" rx="3" fill="url(#careersAccent)" opacity="0.4"/>
    
    <rect x="350" y="200" width="80" height="50" rx="5" fill="#1a1a2e" stroke="url(#careersAccent)" stroke-width="2"/>
    <rect x="355" y="205" width="70" height="40" rx="3" fill="url(#careersAccent)" opacity="0.4"/>
    
    <!-- Code on screens -->
    <g opacity="0.8">
      <rect x="160" y="210" width="20" height="2" fill="#4ecdc4"/>
      <rect x="160" y="215" width="30" height="2" fill="#ff6b6b"/>
      <rect x="160" y="220" width="25" height="2" fill="#45b7d1"/>
      
      <rect x="260" y="210" width="25" height="2" fill="#45b7d1"/>
      <rect x="260" y="215" width="20" height="2" fill="#4ecdc4"/>
      <rect x="260" y="220" width="35" height="2" fill="#ff6b6b"/>
      
      <rect x="360" y="210" width="30" height="2" fill="#ff6b6b"/>
      <rect x="360" y="215" width="25" height="2" fill="#45b7d1"/>
      <rect x="360" y="220" width="20" height="2" fill="#4ecdc4"/>
    </g>
  </g>
  
  <!-- Team collaboration elements -->
  <g opacity="0.6" filter="url(#careersGlow)">
    <!-- Team members (simplified) -->
    <circle cx="120" cy="150" r="20" fill="#4ecdc4" opacity="0.7"/>
    <rect x="110" y="150" width="20" height="30" rx="5" fill="#45b7d1" opacity="0.7"/>
    
    <circle cx="200" cy="150" r="20" fill="#ff6b6b" opacity="0.7"/>
    <rect x="190" y="150" width="20" height="30" rx="5" fill="#4ecdc4" opacity="0.7"/>
    
    <circle cx="280" cy="150" r="20" fill="#45b7d1" opacity="0.7"/>
    <rect x="270" y="150" width="20" height="30" rx="5" fill="#ff6b6b" opacity="0.7"/>
    
    <circle cx="360" cy="150" r="20" fill="#4ecdc4" opacity="0.7"/>
    <rect x="350" y="150" width="20" height="30" rx="5" fill="#45b7d1" opacity="0.7"/>
    
    <circle cx="440" cy="150" r="20" fill="#ff6b6b" opacity="0.7"/>
    <rect x="430" y="150" width="20" height="30" rx="5" fill="#4ecdc4" opacity="0.7"/>
  </g>
  
  <!-- Connection lines between team members -->
  <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.4">
    <path d="M 140 160 L 180 160"/>
    <path d="M 220 160 L 260 160"/>
    <path d="M 300 160 L 340 160"/>
    <path d="M 380 160 L 420 160"/>
  </g>
  
  <!-- Career growth elements -->
  <g transform="translate(50, 50)" opacity="0.5">
    <!-- Growth chart -->
    <rect x="0" y="40" width="8" height="20" fill="#45b7d1"/>
    <rect x="12" y="30" width="8" height="30" fill="#45b7d1"/>
    <rect x="24" y="20" width="8" height="40" fill="#45b7d1"/>
    <rect x="36" y="10" width="8" height="50" fill="#45b7d1"/>
    <rect x="48" y="0" width="8" height="60" fill="#45b7d1"/>
    
    <!-- Arrow pointing up -->
    <polygon points="60,10 70,0 80,10 70,5" fill="#4ecdc4"/>
  </g>
  
  <!-- Innovation symbols -->
  <g transform="translate(450, 80)" opacity="0.6">
    <!-- Light bulb -->
    <circle cx="15" cy="20" r="12" fill="none" stroke="#ff6b6b" stroke-width="2"/>
    <rect x="10" y="30" width="10" height="8" rx="2" fill="#ff6b6b"/>
    <path d="M 8 20 L 22 20" stroke="#ff6b6b" stroke-width="1"/>
    <path d="M 10 15 L 20 15" stroke="#ff6b6b" stroke-width="1"/>
    <path d="M 12 25 L 18 25" stroke="#ff6b6b" stroke-width="1"/>
  </g>
  
  <!-- Floating career elements -->
  <g opacity="0.5">
    <!-- Skills/badges -->
    <circle cx="80" cy="300" r="8" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="cy" values="300;280;300" dur="3s" repeatCount="indefinite"/>
    </circle>
    <rect x="500" y="120" width="12" height="12" rx="2" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="y" values="120;100;120" dur="4s" repeatCount="indefinite"/>
    </rect>
    <polygon points="520,300 530,290 540,300 530,310" fill="#45b7d1" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 530 300;360 530 300" dur="5s" repeatCount="indefinite"/>
    </polygon>
  </g>
  
  <!-- Central logo/brand -->
  <g transform="translate(300, 200)" filter="url(#careersGlow)">
    <circle cx="0" cy="0" r="40" fill="none" stroke="url(#careersAccent)" stroke-width="3" opacity="0.8">
      <animate attributeName="r" values="40;45;40" dur="4s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="-5" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="16" font-weight="bold">G</text>
    <text x="0" y="10" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="8">CAREERS</text>
  </g>
  
  <!-- Title -->
  <text x="300" y="350" text-anchor="middle" fill="url(#careersAccent)" font-family="Arial, sans-serif" font-size="24" font-weight="bold" filter="url(#careersGlow)">
    GAMESTORME CAREERS
  </text>
  <text x="300" y="375" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
    Join the Future of Gaming
  </text>
</svg>
