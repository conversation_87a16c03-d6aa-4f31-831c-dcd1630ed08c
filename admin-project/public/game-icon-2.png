<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="innerGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
    </linearGradient>
    <filter id="dropShadow">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main icon background -->
  <circle cx="50" cy="50" r="45" fill="url(#iconGradient)" filter="url(#dropShadow)"/>
  
  <!-- Inner circle -->
  <circle cx="50" cy="50" r="35" fill="none" stroke="url(#innerGlow)" stroke-width="2" opacity="0.8" filter="url(#glow)"/>
  
  <!-- Gaming controller elements -->
  <g transform="translate(25, 35)">
    <!-- Controller body -->
    <rect x="5" y="10" width="40" height="20" rx="10" fill="#1a1a2e" opacity="0.8"/>
    
    <!-- D-pad -->
    <rect x="12" y="15" width="6" height="2" fill="#4ecdc4"/>
    <rect x="14" y="13" width="2" height="6" fill="#4ecdc4"/>
    
    <!-- Action buttons -->
    <circle cx="35" cy="17" r="2.5" fill="#ff6b6b"/>
    <circle cx="40" cy="20" r="2.5" fill="#45b7d1"/>
    
    <!-- Analog sticks -->
    <circle cx="15" cy="25" r="3" fill="#533483" opacity="0.7"/>
    <circle cx="35" cy="25" r="3" fill="#533483" opacity="0.7"/>
  </g>
  
  <!-- Gaming elements around the icon -->
  <g opacity="0.6">
    <!-- Pixel blocks -->
    <rect x="15" y="15" width="4" height="4" fill="#4ecdc4">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="81" y="20" width="4" height="4" fill="#ff6b6b">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="20" y="80" width="4" height="4" fill="#45b7d1">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
    </rect>
    <rect x="75" y="75" width="4" height="4" fill="#4ecdc4">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Power symbol in center -->
  <g transform="translate(50, 50)">
    <circle cx="0" cy="0" r="8" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.8"/>
    <line x1="0" y1="-8" x2="0" y2="-3" stroke="#4ecdc4" stroke-width="2" opacity="0.8"/>
  </g>
  
  <!-- Orbiting elements -->
  <g>
    <circle cx="50" cy="50" r="25" fill="none" stroke="#45b7d1" stroke-width="1" opacity="0.3"/>
    <circle cx="75" cy="50" r="2" fill="#45b7d1" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 50 50;360 50 50" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <g>
    <circle cx="50" cy="50" r="35" fill="none" stroke="#ff6b6b" stroke-width="1" opacity="0.2"/>
    <circle cx="85" cy="50" r="1.5" fill="#ff6b6b" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 50 50;-360 50 50" dur="6s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
