<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="canGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#45b7d1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="labelGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2a2a3e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    <filter id="canGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="400" fill="#1a1a2e"/>
  
  <!-- Can body -->
  <g transform="translate(75, 50)">
    <!-- Main can cylinder -->
    <rect x="0" y="20" width="150" height="280" rx="75" fill="url(#canGradient)" filter="url(#canGlow)"/>
    
    <!-- Can top -->
    <ellipse cx="75" cy="20" rx="75" ry="15" fill="#ff6b6b"/>
    <ellipse cx="75" cy="15" rx="70" ry="12" fill="#ff8a8a"/>
    
    <!-- Can bottom -->
    <ellipse cx="75" cy="300" rx="75" ry="15" fill="#533483"/>
    
    <!-- Label area -->
    <rect x="10" y="80" width="130" height="160" rx="10" fill="url(#labelGradient)" opacity="0.9"/>
    
    <!-- Z-Tea branding -->
    <text x="75" y="120" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="32" font-weight="bold" filter="url(#canGlow)">
      Z-TEA
    </text>
    
    <!-- Gaming elements -->
    <g transform="translate(75, 150)">
      <!-- Controller icon -->
      <rect x="-20" y="-8" width="40" height="16" rx="8" fill="#4ecdc4" opacity="0.8"/>
      <circle cx="-12" cy="0" r="3" fill="#1a1a2e"/>
      <circle cx="12" cy="0" r="3" fill="#1a1a2e"/>
    </g>
    
    <!-- Energy drink elements -->
    <text x="75" y="180" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
      GAMING ENERGY
    </text>
    <text x="75" y="200" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12">
      BOOST YOUR GAME
    </text>
    
    <!-- Decorative elements -->
    <g opacity="0.6">
      <circle cx="30" cy="100" r="3" fill="#4ecdc4">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="120" cy="120" r="2" fill="#ff6b6b">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="40" cy="220" r="2.5" fill="#45b7d1">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
      </circle>
      <circle cx="110" cy="200" r="2" fill="#4ecdc4">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
      </circle>
    </g>
    
    <!-- Nutrition facts style -->
    <rect x="20" y="250" width="110" height="40" fill="#2a2a3e" opacity="0.8"/>
    <text x="75" y="265" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10">
      GAMING FORMULA
    </text>
    <text x="75" y="278" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="8">
      Enhanced Focus • Quick Reflexes
    </text>
  </g>
  
  <!-- Floating energy particles -->
  <g opacity="0.7">
    <circle cx="50" cy="100" r="1.5" fill="#4ecdc4">
      <animate attributeName="cy" values="100;80;100" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="250" cy="150" r="1" fill="#ff6b6b">
      <animate attributeName="cy" values="150;130;150" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="60" cy="300" r="2" fill="#45b7d1">
      <animate attributeName="cy" values="300;280;300" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="240" cy="250" r="1.5" fill="#4ecdc4">
      <animate attributeName="cy" values="250;230;250" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;1;0.7" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
