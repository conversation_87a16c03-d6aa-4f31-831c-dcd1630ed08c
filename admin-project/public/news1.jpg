<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="news1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="news1Accent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.8" />
    </linearGradient>
    <filter id="news1Glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#news1Gradient)"/>
  
  <!-- Developer program elements -->
  <g opacity="0.8">
    <!-- Code editor -->
    <rect x="50" y="80" width="150" height="100" rx="8" fill="#1a1a2e" stroke="url(#news1Accent)" stroke-width="2"/>
    <rect x="55" y="85" width="140" height="90" rx="5" fill="url(#news1Accent)" opacity="0.3"/>
    
    <!-- Code lines -->
    <rect x="65" y="95" width="40" height="3" fill="#4ecdc4"/>
    <rect x="65" y="105" width="60" height="3" fill="#45b7d1"/>
    <rect x="65" y="115" width="35" height="3" fill="#4ecdc4"/>
    <rect x="65" y="125" width="50" height="3" fill="#45b7d1"/>
    <rect x="65" y="135" width="45" height="3" fill="#4ecdc4"/>
    <rect x="65" y="145" width="55" height="3" fill="#45b7d1"/>
    <rect x="65" y="155" width="30" height="3" fill="#4ecdc4"/>
  </g>
  
  <!-- Developer icons -->
  <g transform="translate(250, 120)" filter="url(#news1Glow)">
    <!-- Developer 1 -->
    <circle cx="0" cy="0" r="15" fill="#4ecdc4" opacity="0.8"/>
    <rect x="-8" y="0" width="16" height="20" rx="3" fill="#45b7d1" opacity="0.8"/>
    <circle cx="-3" cy="-5" r="2" fill="#1a1a2e"/>
    <circle cx="3" cy="-5" r="2" fill="#1a1a2e"/>
    
    <!-- Developer 2 -->
    <circle cx="40" cy="0" r="15" fill="#45b7d1" opacity="0.8"/>
    <rect x="32" y="0" width="16" height="20" rx="3" fill="#4ecdc4" opacity="0.8"/>
    <circle cx="37" cy="-5" r="2" fill="#1a1a2e"/>
    <circle cx="43" cy="-5" r="2" fill="#1a1a2e"/>
    
    <!-- Developer 3 -->
    <circle cx="80" cy="0" r="15" fill="#4ecdc4" opacity="0.8"/>
    <rect x="72" y="0" width="16" height="20" rx="3" fill="#45b7d1" opacity="0.8"/>
    <circle cx="77" cy="-5" r="2" fill="#1a1a2e"/>
    <circle cx="83" cy="-5" r="2" fill="#1a1a2e"/>
  </g>
  
  <!-- Program benefits -->
  <g transform="translate(80, 220)" opacity="0.7">
    <!-- Support -->
    <rect x="0" y="0" width="60" height="25" rx="3" fill="url(#news1Accent)" opacity="0.4"/>
    <text x="30" y="16" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">SUPPORT</text>
    
    <!-- Resources -->
    <rect x="80" y="0" width="60" height="25" rx="3" fill="url(#news1Accent)" opacity="0.4"/>
    <text x="110" y="16" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10" font-weight="bold">RESOURCES</text>
    
    <!-- Community -->
    <rect x="160" y="0" width="60" height="25" rx="3" fill="url(#news1Accent)" opacity="0.4"/>
    <text x="190" y="16" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">COMMUNITY</text>
  </g>
  
  <!-- Connection lines -->
  <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.5">
    <path d="M 200 130 L 250 130"/>
    <path d="M 290 130 L 330 130"/>
    <circle cx="200" cy="130" r="2" fill="#4ecdc4"/>
    <circle cx="250" cy="130" r="2" fill="#4ecdc4"/>
    <circle cx="290" cy="130" r="2" fill="#45b7d1"/>
    <circle cx="330" cy="130" r="2" fill="#45b7d1"/>
  </g>
  
  <!-- Floating elements -->
  <g opacity="0.6">
    <circle cx="60" cy="60" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="60;40;60" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="340" cy="80" r="2" fill="#45b7d1">
      <animate attributeName="cy" values="80;60;80" dur="4s" repeatCount="indefinite"/>
    </circle>
    <rect x="320" y="200" width="4" height="4" fill="#4ecdc4">
      <animate attributeName="y" values="200;180;200" dur="2.5s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Title -->
  <text x="200" y="50" text-anchor="middle" fill="url(#news1Accent)" font-family="Arial, sans-serif" font-size="18" font-weight="bold" filter="url(#news1Glow)">
    DEVELOPER PROGRAM
  </text>
  <text x="200" y="280" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Empowering Game Developers
  </text>
</svg>
