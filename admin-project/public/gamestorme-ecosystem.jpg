<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ecosystemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:0.9" />
      <stop offset="70%" style="stop-color:#0f3460;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="ecosystemAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="ecosystemGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#ecosystemGradient)"/>
  
  <!-- Central ecosystem hub -->
  <g transform="translate(300, 200)" filter="url(#ecosystemGlow)">
    <circle cx="0" cy="0" r="60" fill="none" stroke="url(#ecosystemAccent)" stroke-width="4" opacity="0.8">
      <animate attributeName="r" values="60;70;60" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="40" fill="url(#ecosystemAccent)" opacity="0.3">
      <animate attributeName="r" values="40;45;40" dur="4s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="-5" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="18" font-weight="bold">GAMESTORME</text>
    <text x="0" y="15" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="12">ECOSYSTEM</text>
  </g>
  
  <!-- Ecosystem components -->
  <g opacity="0.8">
    <!-- Developers -->
    <g transform="translate(150, 100)">
      <circle cx="0" cy="0" r="30" fill="url(#ecosystemAccent)" opacity="0.6"/>
      <rect x="-10" y="-5" width="20" height="15" rx="2" fill="#1a1a2e"/>
      <rect x="-8" y="-3" width="16" height="11" rx="1" fill="#4ecdc4" opacity="0.8"/>
      <text x="0" y="25" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">DEVELOPERS</text>
    </g>
    
    <!-- Players -->
    <g transform="translate(450, 100)">
      <circle cx="0" cy="0" r="30" fill="url(#ecosystemAccent)" opacity="0.6"/>
      <circle cx="0" cy="-5" r="8" fill="#ff6b6b"/>
      <rect x="-6" y="3" width="12" height="15" rx="3" fill="#45b7d1"/>
      <text x="0" y="25" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10" font-weight="bold">PLAYERS</text>
    </g>
    
    <!-- Investors -->
    <g transform="translate(150, 300)">
      <circle cx="0" cy="0" r="30" fill="url(#ecosystemAccent)" opacity="0.6"/>
      <rect x="-8" y="-8" width="16" height="12" rx="2" fill="#45b7d1"/>
      <rect x="-6" y="-6" width="12" height="8" fill="#1a1a2e"/>
      <text x="0" y="25" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10" font-weight="bold">INVESTORS</text>
    </g>
    
    <!-- Partners -->
    <g transform="translate(450, 300)">
      <circle cx="0" cy="0" r="30" fill="url(#ecosystemAccent)" opacity="0.6"/>
      <polygon points="-8,-8 8,-8 8,8 -8,8" fill="none" stroke="#4ecdc4" stroke-width="2"/>
      <circle cx="0" cy="0" r="4" fill="#ff6b6b"/>
      <text x="0" y="25" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">PARTNERS</text>
    </g>
  </g>
  
  <!-- Connection lines -->
  <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.5">
    <!-- From center to each component -->
    <path d="M 240 160 Q 270 130 300 140"/>
    <path d="M 360 160 Q 390 130 420 140"/>
    <path d="M 240 240 Q 270 270 300 260"/>
    <path d="M 360 240 Q 390 270 420 260"/>
    
    <!-- Between components -->
    <path d="M 180 130 Q 300 50 420 130"/>
    <path d="M 180 270 Q 300 350 420 270"/>
    <path d="M 120 130 Q 80 200 120 270"/>
    <path d="M 480 130 Q 520 200 480 270"/>
  </g>
  
  <!-- Data flow indicators -->
  <g opacity="0.6">
    <circle cx="200" cy="150" r="3" fill="#4ecdc4">
      <animate attributeName="cx" values="200;250;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="150;140;140" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="150" r="3" fill="#ff6b6b">
      <animate attributeName="cx" values="400;350;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="150;140;140" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="250" r="3" fill="#45b7d1">
      <animate attributeName="cx" values="200;250;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="250;260;260" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="250" r="3" fill="#4ecdc4">
      <animate attributeName="cx" values="400;350;300" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="cy" values="250;260;260" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Ecosystem benefits -->
  <g transform="translate(50, 50)" opacity="0.7">
    <rect x="0" y="0" width="100" height="30" rx="5" fill="url(#ecosystemAccent)" opacity="0.4"/>
    <text x="50" y="20" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">COLLABORATION</text>
  </g>
  
  <g transform="translate(450, 50)" opacity="0.7">
    <rect x="0" y="0" width="100" height="30" rx="5" fill="url(#ecosystemAccent)" opacity="0.4"/>
    <text x="50" y="20" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10" font-weight="bold">INNOVATION</text>
  </g>
  
  <g transform="translate(50, 320)" opacity="0.7">
    <rect x="0" y="0" width="100" height="30" rx="5" fill="url(#ecosystemAccent)" opacity="0.4"/>
    <text x="50" y="20" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="10" font-weight="bold">GROWTH</text>
  </g>
  
  <g transform="translate(450, 320)" opacity="0.7">
    <rect x="0" y="0" width="100" height="30" rx="5" fill="url(#ecosystemAccent)" opacity="0.4"/>
    <text x="50" y="20" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">VALUE</text>
  </g>
  
  <!-- Floating elements -->
  <g opacity="0.4">
    <circle cx="80" cy="200" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="200;180;200" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="520" cy="200" r="2" fill="#ff6b6b">
      <animate attributeName="cy" values="200;180;200" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="300" cy="80" r="2" fill="#45b7d1">
      <animate attributeName="cy" values="80;60;80" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="300" cy="320" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="320;300;320" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Title -->
  <text x="300" y="380" text-anchor="middle" fill="url(#ecosystemAccent)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#ecosystemGlow)">
    GAMESTORME ECOSYSTEM
  </text>
</svg>
