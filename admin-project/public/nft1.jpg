<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nft1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#533483;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cosmicGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="nft1Glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="400" fill="url(#nft1Gradient)"/>
  
  <!-- Cosmic Guardian -->
  <g transform="translate(150, 200)" filter="url(#nft1Glow)">
    <!-- Guardian body -->
    <circle cx="0" cy="0" r="40" fill="url(#cosmicGlow)" opacity="0.8"/>
    <rect x="-25" y="0" width="50" height="60" rx="10" fill="#4ecdc4" opacity="0.9"/>
    
    <!-- Guardian head -->
    <circle cx="0" cy="-30" r="25" fill="#45b7d1" opacity="0.9"/>
    
    <!-- Eyes -->
    <circle cx="-8" cy="-35" r="4" fill="#ff6b6b" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="8" cy="-35" r="4" fill="#ff6b6b" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.3;0.9" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Cosmic armor details -->
    <rect x="-20" y="10" width="40" height="8" rx="4" fill="#ff6b6b" opacity="0.8"/>
    <rect x="-15" y="25" width="30" height="6" rx="3" fill="#45b7d1" opacity="0.8"/>
    <rect x="-10" y="40" width="20" height="4" rx="2" fill="#4ecdc4" opacity="0.8"/>
    
    <!-- Cosmic weapon -->
    <rect x="30" y="-10" width="40" height="8" rx="4" fill="url(#cosmicGlow)" opacity="0.8"/>
    <circle cx="75" cy="-6" r="6" fill="#ff6b6b" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.5;0.9" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Guardian cape -->
    <path d="M -25 20 Q -40 40 -30 80 Q -20 90 -10 80 Q 0 70 10 80 Q 20 90 30 80 Q 40 40 25 20" fill="#533483" opacity="0.7"/>
  </g>
  
  <!-- Cosmic effects -->
  <g opacity="0.6">
    <!-- Stars -->
    <circle cx="50" cy="80" r="2" fill="#4ecdc4">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="250" cy="120" r="1.5" fill="#ff6b6b">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="320" r="2.5" fill="#45b7d1">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="220" cy="300" r="2" fill="#4ecdc4">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Cosmic energy -->
    <circle cx="150" cy="100" r="30" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.4">
      <animate attributeName="r" values="30;40;30" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="300" r="25" fill="none" stroke="#ff6b6b" stroke-width="2" opacity="0.4">
      <animate attributeName="r" values="25;35;25" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Guardian powers -->
  <g transform="translate(150, 120)" opacity="0.7">
    <!-- Power orbs -->
    <circle cx="-40" cy="0" r="8" fill="#4ecdc4" opacity="0.8">
      <animate attributeName="cy" values="0;-10;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="40" cy="0" r="8" fill="#ff6b6b" opacity="0.8">
      <animate attributeName="cy" values="0;-10;0" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-30" r="6" fill="#45b7d1" opacity="0.8">
      <animate attributeName="cy" values="-30;-40;-30" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Legendary rarity indicator -->
  <g transform="translate(150, 350)" opacity="0.9">
    <rect x="-50" y="0" width="100" height="25" rx="12" fill="url(#cosmicGlow)" opacity="0.6"/>
    <text x="0" y="16" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">LEGENDARY</text>
  </g>
  
  <!-- Title -->
  <text x="150" y="50" text-anchor="middle" fill="url(#cosmicGlow)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#nft1Glow)">
    COSMIC GUARDIAN
  </text>
</svg>
