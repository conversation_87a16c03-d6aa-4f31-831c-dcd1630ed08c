<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="news3Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0f3460;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="news3Accent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
    </linearGradient>
    <filter id="news3Glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#news3Gradient)"/>
  
  <!-- Platform update elements -->
  <g opacity="0.8">
    <!-- New features showcase -->
    <g transform="translate(100, 100)">
      <!-- Feature 1: AI -->
      <rect x="0" y="0" width="60" height="40" rx="5" fill="url(#news3Accent)" opacity="0.6" filter="url(#news3Glow)"/>
      <circle cx="30" cy="20" r="8" fill="#4ecdc4"/>
      <text x="30" y="24" text-anchor="middle" fill="#1a1a2e" font-family="Arial, sans-serif" font-size="8" font-weight="bold">AI</text>
      <text x="30" y="55" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="8">AI TOOLS</text>
      
      <!-- Feature 2: Blockchain -->
      <rect x="80" y="0" width="60" height="40" rx="5" fill="url(#news3Accent)" opacity="0.6" filter="url(#news3Glow)"/>
      <polygon points="110,12 120,8 130,12 120,16" fill="#ff6b6b"/>
      <polygon points="110,20 120,16 130,20 120,24" fill="#ff6b6b"/>
      <polygon points="110,28 120,24 130,28 120,32" fill="#ff6b6b"/>
      <text x="110" y="55" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="8">BLOCKCHAIN</text>
      
      <!-- Feature 3: Analytics -->
      <rect x="160" y="0" width="60" height="40" rx="5" fill="url(#news3Accent)" opacity="0.6" filter="url(#news3Glow)"/>
      <rect x="170" y="25" width="6" height="10" fill="#4ecdc4"/>
      <rect x="180" y="20" width="6" height="15" fill="#4ecdc4"/>
      <rect x="190" y="15" width="6" height="20" fill="#4ecdc4"/>
      <rect x="200" y="10" width="6" height="25" fill="#4ecdc4"/>
      <text x="190" y="55" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="8">ANALYTICS</text>
    </g>
  </g>
  
  <!-- Update indicators -->
  <g transform="translate(80, 180)" opacity="0.7">
    <!-- Version info -->
    <rect x="0" y="0" width="80" height="25" rx="3" fill="url(#news3Accent)" opacity="0.4"/>
    <text x="40" y="16" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">VERSION 2.0</text>
    
    <rect x="100" y="0" width="80" height="25" rx="3" fill="url(#news3Accent)" opacity="0.4"/>
    <text x="140" y="16" text-anchor="middle" fill="#ff6b6b" font-family="Arial, sans-serif" font-size="10" font-weight="bold">NEW FEATURES</text>
    
    <rect x="200" y="0" width="80" height="25" rx="3" fill="url(#news3Accent)" opacity="0.4"/>
    <text x="240" y="16" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="10" font-weight="bold">ENHANCED UI</text>
  </g>
  
  <!-- Platform connections -->
  <g stroke="#4ecdc4" stroke-width="2" fill="none" opacity="0.5">
    <path d="M 130 140 Q 160 120 190 140"/>
    <path d="M 160 140 Q 190 120 220 140"/>
    <circle cx="130" cy="140" r="2" fill="#4ecdc4"/>
    <circle cx="160" cy="140" r="2" fill="#ff6b6b"/>
    <circle cx="190" cy="140" r="2" fill="#4ecdc4"/>
    <circle cx="220" cy="140" r="2" fill="#ff6b6b"/>
  </g>
  
  <!-- Innovation symbols -->
  <g transform="translate(300, 120)" opacity="0.6">
    <!-- Gear/settings -->
    <circle cx="0" cy="0" r="15" fill="none" stroke="#ff6b6b" stroke-width="2"/>
    <circle cx="0" cy="0" r="8" fill="#ff6b6b" opacity="0.6"/>
    <rect x="-2" y="-12" width="4" height="6" fill="#ff6b6b"/>
    <rect x="-2" y="6" width="4" height="6" fill="#ff6b6b"/>
    <rect x="-12" y="-2" width="6" height="4" fill="#ff6b6b"/>
    <rect x="6" y="-2" width="6" height="4" fill="#ff6b6b"/>
    
    <animateTransform attributeName="transform" type="rotate" values="0 300 120;360 300 120" dur="8s" repeatCount="indefinite"/>
  </g>
  
  <!-- Floating update elements -->
  <g opacity="0.5">
    <circle cx="60" cy="60" r="2" fill="#4ecdc4">
      <animate attributeName="cy" values="60;40;60" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="340" cy="80" r="2" fill="#ff6b6b">
      <animate attributeName="cy" values="80;60;80" dur="4s" repeatCount="indefinite"/>
    </circle>
    <rect x="50" y="240" width="4" height="4" fill="#4ecdc4">
      <animate attributeName="y" values="240;220;240" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <polygon points="350,240 355,235 360,240 355,245" fill="#ff6b6b">
      <animateTransform attributeName="transform" type="rotate" values="0 355 240;360 355 240" dur="6s" repeatCount="indefinite"/>
    </polygon>
  </g>
  
  <!-- Title -->
  <text x="200" y="40" text-anchor="middle" fill="url(#news3Accent)" font-family="Arial, sans-serif" font-size="18" font-weight="bold" filter="url(#news3Glow)">
    PLATFORM UPDATE
  </text>
  <text x="200" y="280" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Enhanced Gaming Experience
  </text>
</svg>
