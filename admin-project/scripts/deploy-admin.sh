#!/bin/bash

# 🚀 Gamestorme Admin Dashboard Deployment Script
# Deploys the admin dashboard to production

echo "🔥 Gamestorme Admin Dashboard Deployment"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the admin project root."
    exit 1
fi

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "⚠️  Warning: .env.local not found. Copying from template..."
    if [ -f ".env.local.template" ]; then
        cp .env.local.template .env.local
        echo "✅ Created .env.local from template"
        echo "📝 Please edit .env.local with your Firebase configuration"
    else
        echo "❌ Error: .env.local.template not found"
        exit 1
    fi
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install dependencies"
    exit 1
fi

# Build the project
echo "🔨 Building admin dashboard..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Error: Build failed"
    exit 1
fi

# Check if Firebase CLI is available
if ! command -v firebase &> /dev/null; then
    echo "⚠️  Firebase CLI not found. Installing..."
    npm install -g firebase-tools
fi

# Login to Firebase (if not already logged in)
echo "🔐 Checking Firebase authentication..."
firebase projects:list > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "🔑 Please login to Firebase..."
    firebase login
fi

# Set Firebase project
echo "🎯 Setting Firebase project..."
firebase use gamestorme-faf42

# Deploy Firestore rules
echo "🔒 Deploying Firestore security rules..."
firebase deploy --only firestore:rules

if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to deploy Firestore rules"
    exit 1
fi

# Deploy to Firebase Hosting (if configured)
if [ -f "firebase.json" ] && grep -q "hosting" firebase.json; then
    echo "🌐 Deploying to Firebase Hosting..."
    firebase deploy --only hosting
    
    if [ $? -eq 0 ]; then
        echo "✅ Admin dashboard deployed successfully!"
        echo "🔗 Access your admin dashboard at: https://gamestorme-faf42.web.app"
    else
        echo "❌ Error: Failed to deploy to hosting"
        exit 1
    fi
else
    echo "ℹ️  Firebase Hosting not configured. Skipping hosting deployment."
    echo "✅ Firestore rules deployed successfully!"
fi

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Access your admin dashboard"
echo "2. <NAME_EMAIL>"
echo "3. Verify all admin functions work correctly"
echo ""
echo "🔒 Security Reminder:"
echo "- Only <EMAIL> has admin access"
echo "- Use strong passwords and enable 2FA"
echo "- Monitor admin logs regularly"
echo ""
echo "🎯 Admin Dashboard Features:"
echo "- 13 Executive administrative sections"
echo "- Real-time Firebase integration"
echo "- Mobile-responsive design"
echo "- Enterprise-level security"
