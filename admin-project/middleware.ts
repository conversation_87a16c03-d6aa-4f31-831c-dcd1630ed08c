// 🛡️ Gamestorme Security Middleware - Advanced Protection Layer
// Protects against DDoS, hacks, and malicious attacks

import { NextRequest, NextResponse } from 'next/server';
import { SECURITY_CONFIG, InputValidator, ThreatDetector, HoneypotSystem } from './lib/security';

// 🔒 IP Tracking and Rate Limiting
class SecurityMiddleware {
  private static requestCounts = new Map<string, { count: number; firstRequest: number; banned: boolean; banExpiry?: number }>();
  private static suspiciousIPs = new Set<string>();
  private static blockedCountries = new Set(['CN', 'RU', 'KP']); // Block high-risk countries
  
  static async processRequest(request: NextRequest): Promise<NextResponse> {
    const ip = this.getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';
    const pathname = request.nextUrl.pathname;
    const hostname = request.nextUrl.hostname;

    console.log(`🔍 Processing request: ${pathname} from IP: ${ip}`);

    // Skip security checks for Firebase and Google services
    if (
      hostname.includes('firebaseapp.com') ||
      hostname.includes('googleapis.com') ||
      hostname.includes('gstatic.com') ||
      pathname.includes('__/auth/') ||
      pathname.includes('__/firebase/')
    ) {
      console.log(`🔥 FIREBASE: Allowing Firebase request: ${pathname}`);
      const response = NextResponse.next();
      this.addSecurityHeaders(response);
      return response;
    }

    // 1. Check if IP is banned
    if (this.isIPBanned(ip)) {
      console.log(`🚫 BLOCKED: Banned IP ${ip} attempted access`);
      return this.createBlockedResponse('IP_BANNED');
    }
    
    // 2. Check honeypot traps
    if (HoneypotSystem.isTrapped(ip)) {
      console.log(`🍯 BLOCKED: Honeypot trapped IP ${ip}`);
      return this.createBlockedResponse('HONEYPOT_TRAPPED');
    }
    
    // 3. Analyze threat level
    const threatAnalysis = ThreatDetector.analyzeRequest(request);
    if (threatAnalysis.threat) {
      console.log(`🚨 THREAT DETECTED: ${threatAnalysis.type} from IP ${ip}`);
      this.flagSuspiciousIP(ip);
      
      if (threatAnalysis.severity === 'HIGH') {
        this.banIP(ip, 'HIGH_THREAT_DETECTED');
        return this.createBlockedResponse('THREAT_DETECTED');
      }
    }
    
    // 4. Rate limiting based on endpoint
    const rateLimit = this.getRateLimit(pathname);
    if (!this.checkRateLimit(ip, rateLimit)) {
      console.log(`⚡ RATE LIMITED: IP ${ip} exceeded ${rateLimit.max} requests`);
      return this.createRateLimitResponse();
    }
    
    // 5. Validate request size and content
    if (!this.validateRequestSize(request)) {
      console.log(`📏 BLOCKED: Request too large from IP ${ip}`);
      return this.createBlockedResponse('REQUEST_TOO_LARGE');
    }
    
    // 6. Check for suspicious patterns in URL
    if (this.containsSuspiciousPatterns(pathname)) {
      console.log(`🔍 BLOCKED: Suspicious URL pattern from IP ${ip}: ${pathname}`);
      this.flagSuspiciousIP(ip);
      return this.createBlockedResponse('SUSPICIOUS_PATTERN');
    }
    
    // 7. Geographic blocking (optional)
    const country = this.getCountryFromIP(ip);
    if (country && this.blockedCountries.has(country)) {
      console.log(`🌍 BLOCKED: Request from blocked country ${country}, IP: ${ip}`);
      return this.createBlockedResponse('GEO_BLOCKED');
    }
    
    // 8. Create secure response with headers
    const response = NextResponse.next();
    this.addSecurityHeaders(response);
    
    // 9. Log successful request
    this.logRequest(ip, pathname, userAgent);
    
    return response;
  }
  
  private static getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const remoteAddr = request.headers.get('remote-addr');
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    return realIP || remoteAddr || request.ip || 'unknown';
  }
  
  private static isIPBanned(ip: string): boolean {
    const record = this.requestCounts.get(ip);
    if (!record || !record.banned) return false;
    
    // Check if ban has expired
    if (record.banExpiry && Date.now() > record.banExpiry) {
      record.banned = false;
      record.banExpiry = undefined;
      return false;
    }
    
    return true;
  }
  
  private static banIP(ip: string, reason: string): void {
    const record = this.requestCounts.get(ip) || { count: 0, firstRequest: Date.now(), banned: false };
    record.banned = true;
    record.banExpiry = Date.now() + SECURITY_CONFIG.DDOS_PROTECTION.BAN_DURATION;
    this.requestCounts.set(ip, record);
    this.suspiciousIPs.add(ip);
    
    console.log(`🚨 SECURITY ALERT: IP ${ip} banned for ${reason}`);
    
    // In production, send alert to security team
    this.sendSecurityAlert('IP_BANNED', { ip, reason, banExpiry: record.banExpiry });
  }
  
  private static flagSuspiciousIP(ip: string): void {
    this.suspiciousIPs.add(ip);
    console.log(`⚠️ IP ${ip} flagged as suspicious`);
  }
  
  private static getRateLimit(pathname: string): { windowMs: number; max: number } {
    // More lenient limits for development
    const devMultiplier = process.env.NODE_ENV === 'development' ? 10 : 1;

    if (pathname.startsWith('/api/auth/')) {
      return {
        windowMs: SECURITY_CONFIG.RATE_LIMITS.AUTH.windowMs,
        max: SECURITY_CONFIG.RATE_LIMITS.AUTH.max * devMultiplier
      };
    }
    if (pathname.startsWith('/api/upload/')) {
      return {
        windowMs: SECURITY_CONFIG.RATE_LIMITS.UPLOAD.windowMs,
        max: SECURITY_CONFIG.RATE_LIMITS.UPLOAD.max * devMultiplier
      };
    }
    if (pathname.startsWith('/api/admin/')) {
      return {
        windowMs: SECURITY_CONFIG.RATE_LIMITS.ADMIN.windowMs,
        max: SECURITY_CONFIG.RATE_LIMITS.ADMIN.max * devMultiplier
      };
    }
    if (pathname.startsWith('/api/')) {
      return {
        windowMs: SECURITY_CONFIG.RATE_LIMITS.API.windowMs,
        max: SECURITY_CONFIG.RATE_LIMITS.API.max * devMultiplier
      };
    }
    return {
      windowMs: SECURITY_CONFIG.RATE_LIMITS.GENERAL.windowMs,
      max: SECURITY_CONFIG.RATE_LIMITS.GENERAL.max * devMultiplier
    };
  }
  
  private static checkRateLimit(ip: string, limit: { windowMs: number; max: number }): boolean {
    const now = Date.now();
    const record = this.requestCounts.get(ip) || { count: 0, firstRequest: now, banned: false };
    
    // Reset counter if window has passed
    if (now - record.firstRequest > limit.windowMs) {
      record.count = 0;
      record.firstRequest = now;
    }
    
    record.count++;
    this.requestCounts.set(ip, record);
    
    // Check if limit exceeded
    if (record.count > limit.max) {
      // Auto-ban if severely exceeding limits
      if (record.count > limit.max * 3) {
        this.banIP(ip, 'SEVERE_RATE_LIMIT_VIOLATION');
      }
      return false;
    }
    
    return true;
  }
  
  private static validateRequestSize(request: NextRequest): boolean {
    const contentLength = request.headers.get('content-length');
    if (contentLength) {
      const size = parseInt(contentLength, 10);
      return size <= SECURITY_CONFIG.INPUT_VALIDATION.MAX_FILE_SIZE;
    }
    return true;
  }
  
  private static containsSuspiciousPatterns(pathname: string): boolean {
    const suspiciousPatterns = [
      /\.\./,  // Directory traversal
      /\/etc\/passwd/,  // System file access
      /\/proc\//,  // Process information
      /\.(php|asp|jsp|cgi)$/,  // Server-side scripts
      /\.(sql|db)$/,  // Database files
      /admin\/phpmyadmin/,  // Admin tools
      /wp-admin/,  // WordPress admin
      /\.env$/,  // Environment files
      /\/\.git\//,  // Git repository
      /\/(config|backup|dump)/,  // Sensitive directories
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(pathname));
  }
  
  private static getCountryFromIP(ip: string): string | null {
    // In production, use a GeoIP service like MaxMind
    // For now, return null to disable geo-blocking
    return null;
  }
  
  static addSecurityHeaders(response: NextResponse): void {
    Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    // Add additional security headers
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
    response.headers.set('X-Powered-By', 'Gamestorme-Security-v2.0');
    response.headers.set('Server', 'Gamestorme');
  }
  
  private static createBlockedResponse(reason: string): NextResponse {
    return new NextResponse(
      JSON.stringify({
        error: 'Access Denied',
        message: 'Your request has been blocked by our security system.',
        code: reason,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
          ...SECURITY_CONFIG.SECURITY_HEADERS,
        },
      }
    );
  }
  
  private static createRateLimitResponse(): NextResponse {
    return new NextResponse(
      JSON.stringify({
        error: 'Rate Limit Exceeded',
        message: 'Too many requests. Please try again later.',
        retryAfter: 60,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': '60',
          ...SECURITY_CONFIG.SECURITY_HEADERS,
        },
      }
    );
  }
  
  private static logRequest(ip: string, pathname: string, userAgent: string): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      ip,
      pathname,
      userAgent,
      suspicious: this.suspiciousIPs.has(ip),
    };
    
    // In production, send to logging service
    console.log('📊 REQUEST LOG:', JSON.stringify(logEntry));
  }
  
  private static sendSecurityAlert(type: string, data: any): void {
    const alert = {
      timestamp: new Date().toISOString(),
      type,
      data,
      severity: 'HIGH',
      platform: 'Gamestorme',
    };
    
    // In production, send to security monitoring service
    console.log('🚨 SECURITY ALERT:', JSON.stringify(alert, null, 2));
  }
}

// 🛡️ Main Middleware Function - TEMPORARILY DISABLED FOR DEBUGGING
export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and internal Next.js routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/_next/') ||
    (pathname.includes('.') && !pathname.includes('/api/'))
  ) {
    return NextResponse.next();
  }

  // TEMPORARILY DISABLED - Just pass through all requests
  console.log(`🔍 MIDDLEWARE DISABLED: Allowing ${pathname}`);
  return NextResponse.next();

  // TODO: Re-enable security after Firebase issue is resolved
  // return SecurityMiddleware.processRequest(request);
}

// 🎯 Configure which routes to protect
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
