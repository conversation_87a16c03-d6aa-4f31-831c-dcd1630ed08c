# 👑 Gamestorme Admin Dashboard

A dedicated administrative dashboard for managing the Gamestorme gaming platform.

## 🔐 Authentication

**IMPORTANT**: This admin dashboard requires authentication with the specific admin email: `<EMAIL>`

### Access Control
- **Admin Email**: `<EMAIL>` (hardcoded for security)
- **Authentication**: Firebase Authentication required
- **Authorization**: Only the specified admin email can access the dashboard
- **Security**: Automatic redirect to login if not authenticated

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.local.template .env.local

# Edit .env.local with your Firebase configuration
# (The template already includes the correct Gamestorme Firebase config)
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Run Development Server
```bash
npm run dev
```

### 4. Access Admin Dashboard
1. Open `http://localhost:3000`
2. You'll be redirected to login page
3. Login with `<EMAIL>` and your password
4. You'll be redirected to the admin dashboard

## 📊 Admin Features

### 🎯 Executive Dashboard (13 Sections)
1. **Executive Dashboard** - Platform overview and KPIs
2. **Content Dashboard** - Content management and oversight
3. **Business Intelligence** - AI-powered analytics and insights
4. **User Management** - Developer and gamer ecosystem
5. **Content Approval** - Game approval workflow
6. **Financial Operations** - Revenue and financial control
7. **Revenue Analytics** - Advanced monetization analysis
8. **Marketing Hub** - Campaign management and analytics
9. **Customer Success** - Support and satisfaction management
10. **HR Management** - Staff and organizational oversight
11. **Executive Reports** - Comprehensive business reporting
12. **Security Center** - Platform security and compliance
13. **System Administration** - Technical infrastructure management

### 🔧 Key Capabilities
- **Game Approval**: Review and approve/reject developer submissions
- **Support Management**: Handle customer support tickets
- **Financial Oversight**: Monitor revenue and platform finances
- **User Analytics**: Track developer and gamer metrics
- **Security Monitoring**: Platform security and compliance
- **Content Moderation**: Manage platform content and quality

## 🛡️ Security Features

### 🔒 Authentication & Authorization
- **Firebase Authentication**: Secure login system
- **Email-Based Access**: Only `<EMAIL>` allowed
- **Session Management**: Secure session handling
- **Auto-Logout**: Automatic logout on session expiry

### 🛡️ Security Middleware
- **DDoS Protection**: Rate limiting and IP blocking
- **Input Validation**: Comprehensive input sanitization
- **XSS Prevention**: Cross-site scripting protection
- **CSRF Protection**: Cross-site request forgery prevention

### 📊 Audit Logging
- **Admin Actions**: All administrative actions logged
- **Access Tracking**: Login/logout events tracked
- **Data Changes**: All data modifications logged
- **Security Events**: Security incidents tracked

## 🔥 Firebase Configuration

### Required Firebase Services
- **Authentication**: User login and session management
- **Firestore**: Real-time database for platform data
- **Storage**: File uploads and media storage
- **Analytics**: Platform usage analytics

### Firestore Collections Used
- `games` - Game submissions and metadata
- `users` - User profiles and data
- `supportTickets` - Customer support tickets
- `adminLogs` - Administrative action logs
- `notifications` - User notifications
- `analytics` - Platform analytics data

## 📱 Mobile Responsive

### Device Support
- **Desktop**: Full-featured admin interface
- **Tablet**: Optimized layout for tablets
- **Mobile**: Touch-friendly mobile interface
- **Orientation**: Supports portrait and landscape

### Responsive Features
- **Adaptive Layout**: Adjusts to screen size
- **Touch Optimization**: Large touch targets
- **Mobile Navigation**: Collapsible sidebar
- **Performance**: Optimized for mobile networks

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

### Firebase Deployment
```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy hosting (if configured)
firebase deploy --only hosting
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Authentication Problems
- **Issue**: Can't login or access denied
- **Solution**: Ensure you're using `<EMAIL>` email
- **Check**: Verify Firebase Authentication is enabled

#### 2. Firebase Connection Issues
- **Issue**: Data not loading or Firebase errors
- **Solution**: Check `.env.local` configuration
- **Verify**: Firebase project settings and API keys

#### 3. Permission Errors
- **Issue**: Firestore permission denied
- **Solution**: Check Firestore security rules
- **Update**: Deploy latest security rules

#### 4. Build Errors
- **Issue**: TypeScript or build errors
- **Solution**: Run `npm install` to update dependencies
- **Check**: Node.js version compatibility

### Debug Mode
Set `NEXT_PUBLIC_DEBUG_MODE=true` in `.env.local` for detailed logging.

## 📞 Support

For technical support or access issues:
- **Email**: <EMAIL>
- **Platform**: Gamestorme Admin Dashboard
- **Version**: 1.0.0

## 🔒 Security Notice

This admin dashboard contains sensitive platform data and administrative controls. 

**Security Requirements**:
- Only authorized personnel should have access
- Use strong passwords and enable 2FA
- Regularly review access logs
- Report any security incidents immediately

---

**🎯 This admin dashboard provides complete control over the Gamestorme platform with enterprise-level security and functionality.**
