import React, { useState } from 'react';
import Image from 'next/image';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';

interface GameImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
}

const FallbackContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.palette.grey[200],
  color: theme.palette.grey[600],
  width: '100%',
  height: '100%',
  minHeight: 200,
}));

const GameImage: React.FC<GameImageProps> = ({ 
  src, 
  alt, 
  width, 
  height, 
  className,
  style 
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  if (imageError) {
    return (
      <FallbackContainer className={className} style={style}>
        <SportsEsportsIcon sx={{ fontSize: 48, mb: 1 }} />
        <Typography variant="body2" textAlign="center">
          {alt}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Image not available
        </Typography>
      </FallbackContainer>
    );
  }

  return (
    <Box className={className} style={style}>
      {imageLoading && (
        <FallbackContainer>
          <SportsEsportsIcon sx={{ fontSize: 48, mb: 1 }} />
          <Typography variant="body2">Loading...</Typography>
        </FallbackContainer>
      )}
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{
          display: imageLoading ? 'none' : 'block',
          width: '100%',
          height: '100%',
          objectFit: 'cover',
        }}
        unoptimized={true}
      />
    </Box>
  );
};

export default GameImage;
