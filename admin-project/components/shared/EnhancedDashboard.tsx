import React, { ReactNode } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  IconButton,
  Avatar,
  Badge,
  useTheme,
  alpha,
  useMediaQ<PERSON>y,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Chip,
  Button,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import LogoutIcon from '@mui/icons-material/Logout';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarItem {
  id: string;
  label: string;
  icon: ReactNode;
  badge?: string | number;
  color?: string;
}

interface EnhancedDashboardProps {
  title: string;
  subtitle?: string;
  userInitial: string;
  notificationCount?: number;
  sidebarItems: SidebarItem[];
  activeSection: string;
  onSectionChange: (section: string) => void;
  children: ReactNode;
  headerActions?: ReactNode;
}

const DashboardContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${alpha(theme.palette.background.paper, 0.8)} 100%)`,
}));

const Sidebar = styled(Box)(({ theme }) => ({
  width: 280,
  background: `linear-gradient(180deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.85)} 100%)`,
  backdropFilter: 'blur(20px)',
  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '1px',
    height: '100%',
    background: `linear-gradient(180deg, transparent 0%, ${alpha(theme.palette.primary.main, 0.3)} 50%, transparent 100%)`,
  },
  [theme.breakpoints.down('md')]: {
    display: 'none',
  },
}));

const MainContent = styled(Box)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `radial-gradient(circle at 20% 20%, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 50%), 
                 radial-gradient(circle at 80% 80%, ${alpha(theme.palette.secondary.main, 0.03)} 0%, transparent 50%)`,
    pointerEvents: 'none',
    zIndex: 0,
  },
}));

const Header = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  padding: theme.spacing(3, 4),
  position: 'relative',
  zIndex: 2,
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: 'translateX(-50%)',
    width: '60%',
    height: '1px',
    background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.main, 0.4)} 50%, transparent 100%)`,
  },
}));

const ContentArea = styled(Box)(({ theme }) => ({
  flex: 1,
  padding: theme.spacing(4),
  position: 'relative',
  zIndex: 1,
}));

const SidebarItem = styled(ListItemButton)(({ theme }) => ({
  borderRadius: 16,
  margin: theme.spacing(0.5, 2),
  padding: theme.spacing(1.5, 2),
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
    transform: 'translateX(4px)',
  },
  '&.active': {
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.15)} 0%, ${alpha(theme.palette.primary.main, 0.08)} 100%)`,
    borderLeft: `3px solid ${theme.palette.primary.main}`,
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
    },
    '& .MuiListItemText-primary': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({
  title,
  subtitle,
  userInitial,
  notificationCount = 0,
  sidebarItems,
  activeSection,
  onSectionChange,
  children,
  headerActions,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const { logout } = useAuth();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      // Optionally, redirect or refresh page after logout
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const sidebarContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 3, borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
        <Typography variant="h6" fontWeight="bold" color="primary">
          GameStorme
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Professional Dashboard
        </Typography>
      </Box>
      
      <List sx={{ flex: 1, py: 2 }}>
        {sidebarItems.map((item) => (
          <SidebarItem
            key={item.id}
            className={activeSection === item.id ? 'active' : ''}
            onClick={() => {
              onSectionChange(item.id);
              if (isMobile) setMobileOpen(false);
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText 
              primary={item.label}
              primaryTypographyProps={{ fontSize: '0.95rem' }}
            />
            {item.badge && (
              <Chip
                label={item.badge}
                size="small"
                sx={{
                  height: 20,
                  fontSize: '0.75rem',
                  bgcolor: item.color ? alpha(item.color, 0.1) : alpha(theme.palette.primary.main, 0.1),
                  color: item.color || theme.palette.primary.main,
                }}
              />
            )}
          </SidebarItem>
        ))}
      </List>
    </Box>
  );

  return (
    <DashboardContainer>
      {/* Desktop Sidebar */}
      <Sidebar>{sidebarContent}</Sidebar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            width: 280,
            background: `linear-gradient(180deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.85)} 100%)`,
            backdropFilter: 'blur(20px)',
          },
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
          <IconButton onClick={handleDrawerToggle}>
            <CloseIcon />
          </IconButton>
        </Box>
        {sidebarContent}
      </Drawer>

      {/* Main Content */}
      <MainContent>
        {/* Header */}
        <Header>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {isMobile && (
                <IconButton onClick={handleDrawerToggle} sx={{ mr: 2 }}>
                  <MenuIcon />
                </IconButton>
              )}
              <Box>
                <Typography variant="h4" fontWeight="bold" sx={{ 
                  background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}>
                  {title}
                </Typography>
                {subtitle && (
                  <Typography variant="body1" color="text.secondary" sx={{ mt: 0.5 }}>
                    {subtitle}
                  </Typography>
                )}
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {headerActions}
              <Button
                variant="outlined"
                startIcon={<LogoutIcon />}
                onClick={handleLogout}
                color="error"
              >
                Logout
              </Button>
              <IconButton>
                <Badge badgeContent={notificationCount} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
              <Avatar 
                sx={{ 
                  bgcolor: theme.palette.primary.main,
                  width: 44,
                  height: 44,
                  fontWeight: 'bold',
                  boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                }}
              >
                {userInitial}
              </Avatar>
            </Box>
          </Box>
        </Header>

        {/* Content */}
        <ContentArea>
          <Container maxWidth="xl" sx={{ px: { xs: 0, sm: 2 } }}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: [0.4, 0, 0.2, 1] }}
            >
              {children}
            </motion.div>
          </Container>
        </ContentArea>
      </MainContent>
    </DashboardContainer>
  );
};

export default EnhancedDashboard;
