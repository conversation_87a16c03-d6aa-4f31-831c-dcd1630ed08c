import React, { useEffect, useState } from 'react';
import { Box, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  delay: number;
  duration: number;
  color: string;
}

interface ParticleSystemProps {
  count?: number;
  colors?: string[];
  minSize?: number;
  maxSize?: number;
  minDuration?: number;
  maxDuration?: number;
  className?: string;
}

const ParticleContainer = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  pointerEvents: 'none',
  overflow: 'hidden',
  zIndex: 1,
});

const ParticleElement = styled(motion.div)<{ size: number; color: string }>(
  ({ size, color }) => ({
    position: 'absolute',
    width: `${size}px`,
    height: `${size}px`,
    background: color,
    borderRadius: '50%',
    boxShadow: `0 0 ${size * 2}px ${color}`,
  })
);

const ParticleSystem: React.FC<ParticleSystemProps> = ({
  count = 15,
  colors,
  minSize = 2,
  maxSize = 6,
  minDuration = 8,
  maxDuration = 15,
  className = '',
}) => {
  const theme = useTheme();
  const [particles, setParticles] = useState<Particle[]>([]);

  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.primary.light,
    theme.palette.secondary.main,
    theme.palette.secondary.light,
  ];

  const particleColors = colors || defaultColors;

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      
      for (let i = 0; i < count; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * (maxSize - minSize) + minSize,
          delay: Math.random() * 5,
          duration: Math.random() * (maxDuration - minDuration) + minDuration,
          color: particleColors[Math.floor(Math.random() * particleColors.length)],
        });
      }
      
      setParticles(newParticles);
    };

    generateParticles();
  }, [count, minSize, maxSize, minDuration, maxDuration, particleColors]);

  const particleVariants = {
    animate: (particle: Particle) => ({
      y: [0, -100, -200, -300],
      x: [0, Math.random() * 50 - 25, Math.random() * 100 - 50],
      opacity: [0, 0.8, 0.6, 0],
      scale: [0, 1, 0.8, 0],
      rotate: [0, 180, 360],
      transition: {
        duration: particle.duration,
        repeat: Infinity,
        ease: 'linear',
        delay: particle.delay,
      },
    }),
  };

  return (
    <ParticleContainer className={className}>
      {particles.map((particle) => (
        <ParticleElement
          key={particle.id}
          size={particle.size}
          color={particle.color}
          variants={particleVariants}
          animate="animate"
          custom={particle}
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
          }}
        />
      ))}
    </ParticleContainer>
  );
};

export default ParticleSystem;
