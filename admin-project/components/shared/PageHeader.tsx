import React from 'react';
import {
  Box,
  Container,
  Typography,
  useTheme,
  alpha,
  Grid,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';

// Styled components
const HeaderContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  padding: theme.spacing(12, 0, 8),
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.7),
  backdropFilter: 'blur(10px)',
  borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'radial-gradient(circle at 20% 25%, rgba(66, 41, 188, 0.15) 0%, transparent 50%)',
    zIndex: 0,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'radial-gradient(circle at 80% 75%, rgba(240, 188, 43, 0.1) 0%, transparent 50%)',
    zIndex: 0,
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 800,
  marginBottom: theme.spacing(2),
  position: 'relative',
  zIndex: 1,
  '& span': {
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
  },
}));

const PageDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  maxWidth: '800px',
  marginBottom: theme.spacing(4),
  position: 'relative',
  zIndex: 1,
}));

interface PageHeaderProps {
  title: string;
  highlightedTitle?: string;
  description: string;
  children?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  highlightedTitle,
  description,
  children,
}) => {
  const theme = useTheme();

  return (
    <HeaderContainer>
      <Container maxWidth="lg">
        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={children ? 6 : 12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <PageTitle variant="h2">
                {title} {highlightedTitle && <span>{highlightedTitle}</span>}
              </PageTitle>
              <PageDescription variant="h6">
                {description}
              </PageDescription>
            </motion.div>
          </Grid>
          {children && (
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {children}
              </motion.div>
            </Grid>
          )}
        </Grid>
      </Container>
    </HeaderContainer>
  );
};

export default PageHeader;
