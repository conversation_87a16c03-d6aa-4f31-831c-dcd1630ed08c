import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  Divider,
  alpha,
  useTheme,
  SelectChangeEvent,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CloudUpload as CloudUploadIcon,
  Visibility as VisibilityIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Sample game data
const sampleGames = [
  {
    id: 1,
    title: 'Cosmic Odyssey',
    description: 'An epic space adventure game with stunning visuals and immersive gameplay.',
    image: '/images/games/game1.jpg',
    status: 'Live',
    category: 'Adventure',
    platform: 'PC, Mobile',
    releaseDate: '2023-05-15',
    sales: 12500,
    revenue: '$125,000',
    rating: 4.8,
  },
  {
    id: 2,
    title: 'Neon Racer',
    description: 'High-speed futuristic racing game with neon aesthetics and multiplayer mode.',
    image: '/images/games/game2.jpg',
    status: 'Live',
    category: 'Racing',
    platform: 'PC, Console',
    releaseDate: '2023-02-28',
    sales: 8700,
    revenue: '$87,000',
    rating: 4.5,
  },
  {
    id: 3,
    title: 'Mystic Realms',
    description: 'Fantasy RPG with rich storytelling, character customization, and magical combat.',
    image: '/images/games/game3.jpg',
    status: 'In Development',
    category: 'RPG',
    platform: 'PC, Console, Mobile',
    releaseDate: 'TBD',
    sales: 0,
    revenue: '$0',
    rating: 0,
  },
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

const MyGames: React.FC = () => {
  const theme = useTheme();
  const [games, setGames] = useState(sampleGames);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('All');
  const [filterStatus, setFilterStatus] = useState('All');
  const [sortBy, setSortBy] = useState('newest');
  const [tabValue, setTabValue] = useState(0);
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [newGame, setNewGame] = useState({
    title: '',
    description: '',
    category: '',
    platform: '',
    releaseDate: '',
  });

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle filter changes
  const handleCategoryChange = (event: SelectChangeEvent) => {
    setFilterCategory(event.target.value);
  };

  const handleStatusChange = (event: SelectChangeEvent) => {
    setFilterStatus(event.target.value);
  };

  const handleSortChange = (event: SelectChangeEvent) => {
    setSortBy(event.target.value);
  };

  // Handle search
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle upload dialog
  const handleOpenUploadDialog = () => {
    setOpenUploadDialog(true);
  };

  const handleCloseUploadDialog = () => {
    setOpenUploadDialog(false);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setNewGame({ ...newGame, [name]: value });
  };

  const handleSelectChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    setNewGame({ ...newGame, [name]: value });
  };

  const handleUploadGame = () => {
    // Add new game to the list
    const newGameEntry = {
      id: games.length + 1,
      ...newGame,
      image: '/images/games/placeholder.jpg',
      status: 'In Development',
      sales: 0,
      revenue: '$0',
      rating: 0,
    };
    setGames([...games, newGameEntry]);
    handleCloseUploadDialog();
    // Reset form
    setNewGame({
      title: '',
      description: '',
      category: '',
      platform: '',
      releaseDate: '',
    });
  };

  // Filter and sort games
  const filteredGames = games.filter((game) => {
    const matchesSearch = game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      game.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'All' || game.category === filterCategory;
    const matchesStatus = filterStatus === 'All' || game.status === filterStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  }).sort((a, b) => {
    if (sortBy === 'newest') {
      return new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime();
    } else if (sortBy === 'oldest') {
      return new Date(a.releaseDate).getTime() - new Date(b.releaseDate).getTime();
    } else if (sortBy === 'alphabetical') {
      return a.title.localeCompare(b.title);
    } else if (sortBy === 'sales') {
      return b.sales - a.sales;
    }
    return 0;
  });

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" fontWeight="bold">
          My Games
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenUploadDialog}
          sx={{
            borderRadius: theme.shape.borderRadius * 1.5,
            textTransform: 'none',
            px: 3,
          }}
        >
          Upload New Game
        </Button>
      </Box>

      <Paper
        sx={{
          p: 3,
          mb: 4,
          borderRadius: theme.shape.borderRadius * 2,
          backgroundColor: alpha(theme.palette.background.paper, 0.8),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        }}
      >
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search games..."
              variant="outlined"
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filterCategory}
                  onChange={handleCategoryChange}
                  label="Category"
                  startAdornment={<FilterListIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  <MenuItem value="All">All Categories</MenuItem>
                  <MenuItem value="Adventure">Adventure</MenuItem>
                  <MenuItem value="RPG">RPG</MenuItem>
                  <MenuItem value="Racing">Racing</MenuItem>
                  <MenuItem value="Puzzle">Puzzle</MenuItem>
                  <MenuItem value="Strategy">Strategy</MenuItem>
                </Select>
              </FormControl>
              <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={handleStatusChange}
                  label="Status"
                  startAdornment={<FilterListIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  <MenuItem value="All">All Statuses</MenuItem>
                  <MenuItem value="Live">Live</MenuItem>
                  <MenuItem value="In Development">In Development</MenuItem>
                  <MenuItem value="Beta">Beta</MenuItem>
                </Select>
              </FormControl>
              <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={sortBy}
                  onChange={handleSortChange}
                  label="Sort By"
                  startAdornment={<SortIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  <MenuItem value="newest">Newest First</MenuItem>
                  <MenuItem value="oldest">Oldest First</MenuItem>
                  <MenuItem value="alphabetical">A-Z</MenuItem>
                  <MenuItem value="sales">Best Selling</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{
          mb: 3,
          '& .MuiTabs-indicator': {
            height: 3,
            borderRadius: 1.5,
          },
        }}
      >
        <Tab label="All Games" />
        <Tab label="Live" />
        <Tab label="In Development" />
        <Tab label="Analytics" />
      </Tabs>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Grid container spacing={3}>
          {filteredGames.map((game) => (
            <Grid item xs={12} sm={6} md={4} key={game.id}>
              <motion.div variants={itemVariants}>
                <Card sx={{
                  borderRadius: theme.shape.borderRadius * 2,
                  overflow: 'hidden',
                  boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.1)}`,
                  transition: 'all 0.3s ease',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.15)}`,
                  },
                }}>
                  <Box sx={{ position: 'relative', paddingTop: '56.25%' }}>
                    <CardMedia
                      component="img"
                      image={game.image || '/images/games/placeholder.jpg'}
                      alt={game.title}
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 10,
                        right: 10,
                        display: 'flex',
                        gap: 1,
                      }}
                    >
                      <Chip
                        label={game.status}
                        size="small"
                        color={game.status === 'Live' ? 'success' : 'warning'}
                        sx={{ fontWeight: 600 }}
                      />
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {game.title}
                      </Typography>
                      <IconButton size="small">
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {game.description.length > 100
                        ? `${game.description.substring(0, 100)}...`
                        : game.description}
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      <Chip label={game.category} size="small" />
                      <Chip label={game.platform} size="small" />
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Sales
                        </Typography>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {game.sales.toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Revenue
                        </Typography>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {game.revenue}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                  <Box sx={{ p: 2, pt: 0, display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<EditIcon />}
                      fullWidth
                    >
                      Edit
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<VisibilityIcon />}
                      fullWidth
                    >
                      View
                    </Button>
                  </Box>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </motion.div>

      {/* Upload Game Dialog */}
      <Dialog
        open={openUploadDialog}
        onClose={handleCloseUploadDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Upload New Game</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Game Title"
                name="title"
                value={newGame.title}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={newGame.description}
                onChange={handleInputChange}
                multiline
                rows={4}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={newGame.category}
                  onChange={handleSelectChange}
                  label="Category"
                  required
                >
                  <MenuItem value="Adventure">Adventure</MenuItem>
                  <MenuItem value="RPG">RPG</MenuItem>
                  <MenuItem value="Racing">Racing</MenuItem>
                  <MenuItem value="Puzzle">Puzzle</MenuItem>
                  <MenuItem value="Strategy">Strategy</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Platform (e.g., PC, Mobile, Console)"
                name="platform"
                value={newGame.platform}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Release Date"
                name="releaseDate"
                type="date"
                value={newGame.releaseDate}
                onChange={handleInputChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<CloudUploadIcon />}
                sx={{ height: '100%', width: '100%' }}
              >
                Upload Game Files
                <input type="file" hidden />
              </Button>
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<CloudUploadIcon />}
                fullWidth
              >
                Upload Game Thumbnail
                <input type="file" accept="image/*" hidden />
              </Button>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={<Switch />}
                label="Publish immediately after upload"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUploadDialog}>Cancel</Button>
          <Button
            onClick={handleUploadGame}
            variant="contained"
            color="primary"
            disabled={!newGame.title || !newGame.description || !newGame.category}
          >
            Upload Game
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MyGames;
