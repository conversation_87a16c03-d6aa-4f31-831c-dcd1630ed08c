import React, { useRef, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Chip,
  useTheme,
  alpha,
  IconButton,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import StarIcon from '@mui/icons-material/Star';
import Link from 'next/link';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// Styled components
const SectionContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  padding: theme.spacing(10, 0),
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  fontWeight: 800,
  textAlign: 'center',
}));

const SectionSubtitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(6),
  textAlign: 'center',
  maxWidth: 700,
  margin: '0 auto',
}));

const GameCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius,
  overflow: 'hidden',
  transition: 'all 0.3s ease-in-out',
  background: 'transparent',
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  '&:hover': {
    transform: 'translateY(-10px)',
    boxShadow: `0 20px 30px ${alpha(theme.palette.common.black, 0.3)}`,
    '& .game-image': {
      transform: 'scale(1.05)',
    },
  },
}));

const GameImage = styled(CardMedia)(({ theme }) => ({
  height: 240,
  transition: 'transform 0.5s ease-in-out',
}));

const GameTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  marginBottom: theme.spacing(1),
}));

const GameDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(2),
}));

const GameChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0, 0.5, 0.5, 0),
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.light,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
}));

const NavigationButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  color: theme.palette.text.primary,
  boxShadow: theme.shadows[3],
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  },
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 10,
}));

// Gamestorme's featured games
const games = [
  {
    id: 1,
    title: 'Agueybana',
    description: 'An epic adventure inspired by Puerto Rican history and mythology, featuring the legendary Taíno cacique.',
    image: '/assets/images/games/agueybana.jpg',
    rating: 4.8,
    categories: ['Adventure', 'Historical', 'Mythology'],
    platforms: ['PC', 'Mobile'],
  },
  {
    id: 2,
    title: 'Guaramania',
    description: 'Experience the vibrant culture and music of Puerto Rico in this rhythm-based adventure game.',
    image: '/assets/images/games/guaramania.png',
    rating: 4.7,
    categories: ['Rhythm', 'Cultural', 'Music'],
    platforms: ['PC', 'Mobile', 'Console'],
  },
  {
    id: 3,
    title: 'Driadan',
    description: 'Embark on a mystical journey through ancient realms filled with magic and wonder.',
    image: '/assets/images/games/driadan.jpg',
    rating: 4.6,
    categories: ['Fantasy', 'RPG', 'Adventure'],
    platforms: ['PC', 'Console'],
  },
  {
    id: 4,
    title: 'Hajimari',
    description: 'A beautiful story of beginnings and new adventures in a stunning anime-inspired world.',
    image: '/assets/images/games/hajimari.jpg',
    rating: 4.9,
    categories: ['Anime', 'Story', 'Adventure'],
    platforms: ['PC', 'Mobile', 'Console'],
  },
  {
    id: 5,
    title: 'Skybound Game',
    description: 'Soar through the skies in this thrilling aerial adventure with breathtaking views.',
    image: '/assets/images/games/skybound-game.jpg',
    rating: 4.5,
    categories: ['Flying', 'Adventure', 'Action'],
    platforms: ['PC', 'Console'],
  },
  {
    id: 6,
    title: 'Tygran',
    description: 'Command mighty forces in this strategic warfare game set in a fantasy universe.',
    image: '/assets/images/games/tygran.jpg',
    rating: 4.4,
    categories: ['Strategy', 'Fantasy', 'War'],
    platforms: ['PC'],
  },
  {
    id: 7,
    title: 'Z-Tea',
    description: 'A relaxing and fun casual game about running your own tea shop and serving customers.',
    image: '/assets/images/games/z-tea.png',
    rating: 4.3,
    categories: ['Casual', 'Simulation', 'Management'],
    platforms: ['Mobile', 'PC'],
  },
];

const GamesShowcase: React.FC = () => {
  const theme = useTheme();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const swiperRef = useRef<any>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <SectionContainer>
      {/* Background elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          right: '5%',
          width: '30%',
          height: '40%',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 70%)`,
          filter: 'blur(80px)',
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
        >
          <SectionTitle variant="h2">
            Featured Games
          </SectionTitle>
          <SectionSubtitle variant="h6" color="text.secondary">
            Explore our collection of cutting-edge blockchain games that offer immersive experiences and real-world value.
          </SectionSubtitle>
        </motion.div>

        <Box sx={{ position: 'relative', px: { xs: 0, md: 5 } }} ref={ref}>
          <NavigationButton
            onClick={() => swiperRef.current?.slidePrev()}
            sx={{ left: { xs: -5, md: 0 } }}
            disabled={isBeginning}
          >
            <ArrowBackIcon />
          </NavigationButton>

          <NavigationButton
            onClick={() => swiperRef.current?.slideNext()}
            sx={{ right: { xs: -5, md: 0 } }}
            disabled={isEnd}
          >
            <ArrowForwardIcon />
          </NavigationButton>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
          >
            <Swiper
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              modules={[Navigation, Pagination, Autoplay]}
              spaceBetween={24}
              slidesPerView={1}
              pagination={{ clickable: true }}
              autoplay={{ delay: 5000, disableOnInteraction: false }}
              breakpoints={{
                640: {
                  slidesPerView: 2,
                },
                1024: {
                  slidesPerView: 3,
                },
              }}
              style={{ padding: '20px 10px 50px' }}
            >
              {games.map((game) => (
                <SwiperSlide key={game.id}>
                  <motion.div variants={itemVariants} style={{ height: '100%' }}>
                    <GameCard>
                      <GameImage
                        className="game-image"
                        image={game.image}
                        title={game.title}
                      />
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <GameTitle variant="h5">{game.title}</GameTitle>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <StarIcon sx={{ color: theme.palette.secondary.main, mr: 0.5 }} />
                            <Typography variant="body2" fontWeight="bold">
                              {game.rating}
                            </Typography>
                          </Box>
                        </Box>
                        <GameDescription variant="body2">
                          {game.description}
                        </GameDescription>
                        <Box sx={{ mb: 2 }}>
                          {game.categories.map((category) => (
                            <GameChip key={category} label={category} size="small" />
                          ))}
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          Available on: {game.platforms.join(', ')}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 3, pt: 0 }}>
                        <Link href={`/games/${game.id}`} passHref style={{ textDecoration: 'none', width: '100%' }}>
                          <Button
                            variant="contained"
                            color="primary"
                            fullWidth
                            endIcon={<ArrowForwardIcon />}
                          >
                            Play Now
                          </Button>
                        </Link>
                      </CardActions>
                    </GameCard>
                  </motion.div>
                </SwiperSlide>
              ))}
            </Swiper>
          </motion.div>
        </Box>

        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Link href="/games" passHref style={{ textDecoration: 'none' }}>
            <Button
              variant="outlined"
              color="secondary"
              size="large"
              endIcon={<ArrowForwardIcon />}
            >
              View All Games
            </Button>
          </Link>
        </Box>
      </Container>
    </SectionContainer>
  );
};

export default GamesShowcase;
