import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  useTheme,
  alpha,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  TextField,
  IconButton,
  Badge,
  Chip,
  LinearProgress,
  useMediaQuery,
  Drawer,
  AppBar,
  Toolbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Tooltip,
  Skeleton,
  CircularProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';

// Firebase imports
import { firestore, auth } from '../lib/firebase';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  updateDoc,
  doc,
  getDocs,
  limit,
  addDoc,
  deleteDoc,
  Timestamp
} from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

// Analytics imports
import { gamestormeAnalytics } from '../lib/firebaseAnalytics';

// Icons
import DashboardIcon from '@mui/icons-material/Dashboard';
import SportsEsportsIcon from '@mui/icons-material/SportsEsports';
import PeopleIcon from '@mui/icons-material/People';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import SettingsIcon from '@mui/icons-material/Settings';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MenuIcon from '@mui/icons-material/Menu';
import SearchIcon from '@mui/icons-material/Search';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import LogoutIcon from '@mui/icons-material/Logout';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import UnpublishedIcon from '@mui/icons-material/Unpublished';
import ReplyIcon from '@mui/icons-material/Reply';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import GroupIcon from '@mui/icons-material/Group';
import BusinessIcon from '@mui/icons-material/Business';
import SecurityIcon from '@mui/icons-material/Security';
import ReportIcon from '@mui/icons-material/Report';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PaymentIcon from '@mui/icons-material/Payment';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import WorkIcon from '@mui/icons-material/Work';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import ExecutiveIcon from '@mui/icons-material/Business';
import ContentIcon from '@mui/icons-material/Article';
import IntelligenceIcon from '@mui/icons-material/Psychology';
import ApprovalIcon from '@mui/icons-material/CheckCircle';
import MarketingIcon from '@mui/icons-material/Campaign';
import CustomerSuccessIcon from '@mui/icons-material/SupportAgent';
import ReportsIcon from '@mui/icons-material/Assessment';
import SecurityCenterIcon from '@mui/icons-material/Security';
import SystemAdminIcon from '@mui/icons-material/AdminPanelSettings';
import ChatIcon from '@mui/icons-material/Chat';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import InsightsIcon from '@mui/icons-material/Insights';
import SpeedIcon from '@mui/icons-material/Speed';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import BarChartIcon from '@mui/icons-material/BarChart';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import StarIcon from '@mui/icons-material/Star';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
}));

const MainContent = styled(Box)(({ theme }) => ({
  marginLeft: 280,
  padding: theme.spacing(3),
  [theme.breakpoints.down('md')]: {
    marginLeft: 0,
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 15px 30px ${alpha(theme.palette.primary.main, 0.15)}`,
  },
}));

const ActionCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: theme.spacing(2),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 10px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
  },
}));

// Interfaces
interface Game {
  id: string;
  title: string;
  developer: {
    name: string;
    uid: string;
    email: string;
  };
  description: string;
  genre: string[];
  pricing: {
    isFree: boolean;
    price: number;
  };
  stats: {
    downloads: number;
    views: number;
    likes: number;
    rating: number;
    reviews: number;
  };
  images: {
    thumbnail: string;
    screenshots: string[];
  };
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
  rejectionReason?: string;
  adminNotes?: string;
}

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'billing' | 'feature-request' | 'bug-report' | 'general';
  developerId: string;
  developerName: string;
  developerEmail: string;
  adminResponse?: string;
  adminId?: string;
  tags: string[];
  escalated: boolean;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}

interface PlatformStats {
  totalGames: number;
  pendingGames: number;
  approvedGames: number;
  rejectedGames: number;
  totalDevelopers: number;
  activeDevelopers: number;
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalDownloads: number;
  monthlyDownloads: number;
  openTickets: number;
  resolvedTickets: number;
  averageResponseTime: number;
}

const AdminDashboard: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [user, loading, error] = useAuthState(auth);

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [games, setGames] = useState<Game[]>([]);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats | null>(null);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [gameDialogOpen, setGameDialogOpen] = useState(false);
  const [ticketDialogOpen, setTicketDialogOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');

  // Super Admin Check - PRODUCTION: Require proper authentication
  const isSuperAdmin = user?.email === '<EMAIL>';
  const isLegacyAdmin = isSuperAdmin; // Legacy admin has all permissions

  // Real-time data fetching
  useEffect(() => {
    console.log('🔥 Admin Dashboard: Starting Firebase data fetch...');

    // Track admin dashboard view (only if user is logged in)
    if (user) {
      gamestormeAnalytics.setUser(user.uid, {
        user_type: 'admin',
        registration_date: user.metadata.creationTime,
      });
      gamestormeAnalytics.trackPageView('admin_dashboard');
    }

    // Fetch all games (including pending ones)
    console.log('📥 Fetching games from Firebase...');
    const gamesQuery = query(
      collection(firestore, 'games'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribeGames = onSnapshot(gamesQuery, (snapshot) => {
      console.log(`📊 Received ${snapshot.docs.length} games from Firebase`);
      const gamesData: Game[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        console.log(`🎮 Game: ${data.title} - Status: ${data.status}`);
        gamesData.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as Game);
      });
      setGames(gamesData);
      console.log(`✅ Set ${gamesData.length} games in state`);
    }, (error) => {
      console.error('❌ Error fetching games:', error);
    });

    // Fetch all support tickets
    console.log('🎫 Fetching support tickets from Firebase...');
    const ticketsQuery = query(
      collection(firestore, 'supportTickets'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribeTickets = onSnapshot(ticketsQuery, (snapshot) => {
      console.log(`📊 Received ${snapshot.docs.length} support tickets from Firebase`);
      const ticketsData: SupportTicket[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        ticketsData.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          resolvedAt: data.resolvedAt?.toDate(),
        } as SupportTicket);
      });
      setSupportTickets(ticketsData);
      console.log(`✅ Set ${ticketsData.length} support tickets in state`);
    }, (error) => {
      console.error('❌ Error fetching support tickets:', error);
    });

    return () => {
      console.log('🔄 Cleaning up Firebase listeners...');
      unsubscribeGames();
      unsubscribeTickets();
    };
  }, []); // Remove user dependency for testing

  // Calculate platform stats
  useEffect(() => {
    if (games.length > 0 || supportTickets.length > 0) {
      console.log('📊 Calculating platform stats...');
      const stats: PlatformStats = {
        totalGames: games.length,
        pendingGames: games.filter(g => g.status === 'pending').length,
        approvedGames: games.filter(g => g.status === 'approved').length,
        rejectedGames: games.filter(g => g.status === 'rejected').length,
        totalDevelopers: new Set(games.map(g => g.developer.uid)).size,
        activeDevelopers: new Set(games.filter(g => g.status === 'approved').map(g => g.developer.uid)).size,
        totalUsers: 0,
        activeUsers: 0,
        totalRevenue: games.reduce((sum, g) => {
          const downloads = g.stats?.downloads || 0;
          const price = g.pricing?.price || 0;
          return sum + (downloads * price);
        }, 0),
        monthlyRevenue: 0,
        totalDownloads: games.reduce((sum, g) => sum + g.stats.downloads, 0),
        monthlyDownloads: 0,
        openTickets: supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').length,
        resolvedTickets: supportTickets.filter(t => t.status === 'resolved' || t.status === 'closed').length,
        averageResponseTime: 0,
      };
      setPlatformStats(stats);
      console.log('✅ Platform stats calculated:', stats);
    }
  }, [games, supportTickets]);

  const handleGameApproval = async (gameId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      console.log(`🎮 ${action}ing game ${gameId}...`);
      const updateData: any = {
        status: action === 'approve' ? 'approved' : 'rejected',
        updatedAt: Timestamp.now(),
        adminId: user?.uid || 'admin',
      };

      if (action === 'reject' && reason) {
        updateData.rejectionReason = reason;
      }

      await updateDoc(doc(firestore, 'games', gameId), updateData);

      // Log admin action
      await addDoc(collection(firestore, 'adminLogs'), {
        action: `game_${action}`,
        gameId,
        adminId: user?.uid || 'admin',
        adminEmail: user?.email || '<EMAIL>',
        reason: reason || '',
        timestamp: Timestamp.now(),
      });

      // Send notification to developer
      const game = games.find(g => g.id === gameId);
      if (game) {
        await addDoc(collection(firestore, 'notifications'), {
          userId: game.developer.uid,
          type: action === 'approve' ? 'game_approved' : 'game_rejected',
          title: action === 'approve' ? 'Game Approved!' : 'Game Rejected',
          message: action === 'approve'
            ? `Your game "${game.title}" has been approved and is now live!`
            : `Your game "${game.title}" was rejected. Reason: ${reason}`,
          read: false,
          createdAt: Timestamp.now(),
        });
      }

      setSnackbarMessage(`Game ${action === 'approve' ? 'approved' : 'rejected'} successfully!`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setGameDialogOpen(false);
      console.log(`✅ Game ${action}ed successfully`);
    } catch (error) {
      console.error(`❌ Error ${action}ing game:`, error);
      setSnackbarMessage(`Error ${action}ing game. Please try again.`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleGameFeature = async (gameId: string, shouldFeature: boolean) => {
    try {
      console.log(`🌟 ${shouldFeature ? 'Featuring' : 'Unfeaturing'} game ${gameId}...`);

      await updateDoc(doc(firestore, 'games', gameId), {
        featured: shouldFeature,
        featuredAt: shouldFeature ? Timestamp.now() : null,
        updatedAt: Timestamp.now(),
        adminId: user?.uid || 'admin',
      });

      // Log admin action
      await addDoc(collection(firestore, 'adminLogs'), {
        action: shouldFeature ? 'game_featured' : 'game_unfeatured',
        gameId,
        adminId: user?.uid || 'admin',
        adminEmail: user?.email || '<EMAIL>',
        timestamp: Timestamp.now(),
      });

      // Send notification to developer
      const game = games.find(g => g.id === gameId);
      if (game) {
        await addDoc(collection(firestore, 'notifications'), {
          userId: game.developer?.uid,
          type: shouldFeature ? 'game_featured' : 'game_unfeatured',
          title: shouldFeature ? 'Game Featured!' : 'Game Unfeatured',
          message: shouldFeature
            ? `Congratulations! Your game "${game.title}" has been featured on our platform!`
            : `Your game "${game.title}" is no longer featured.`,
          read: false,
          createdAt: Timestamp.now(),
        });
      }

      setSnackbarMessage(`Game ${shouldFeature ? 'featured' : 'unfeatured'} successfully!`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      console.log(`✅ Game ${shouldFeature ? 'featured' : 'unfeatured'} successfully`);
    } catch (error) {
      console.error(`❌ Error ${shouldFeature ? 'featuring' : 'unfeaturing'} game:`, error);
      setSnackbarMessage(`Error ${shouldFeature ? 'featuring' : 'unfeaturing'} game. Please try again.`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleTicketResponse = async (ticketId: string, response: string, newStatus: string) => {
    try {
      console.log(`🎫 Responding to ticket ${ticketId}...`);
      await updateDoc(doc(firestore, 'supportTickets', ticketId), {
        adminResponse: response,
        adminId: user?.uid || 'admin',
        status: newStatus,
        updatedAt: Timestamp.now(),
        resolvedAt: newStatus === 'resolved' ? Timestamp.now() : null,
      });

      // Log admin action
      await addDoc(collection(firestore, 'adminLogs'), {
        action: 'ticket_response',
        ticketId,
        adminId: user?.uid || 'admin',
        adminEmail: user?.email || '<EMAIL>',
        response,
        newStatus,
        timestamp: Timestamp.now(),
      });

      // Send notification to developer
      const ticket = supportTickets.find(t => t.id === ticketId);
      if (ticket) {
        await addDoc(collection(firestore, 'notifications'), {
          userId: ticket.developerId,
          type: 'ticket_response',
          title: 'Support Ticket Update',
          message: `Your support ticket "${ticket.title}" has been updated.`,
          read: false,
          createdAt: Timestamp.now(),
        });
      }

      setSnackbarMessage('Ticket response sent successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setTicketDialogOpen(false);
      console.log('✅ Ticket response sent successfully');
    } catch (error) {
      console.error('❌ Error responding to ticket:', error);
      setSnackbarMessage('Error sending response. Please try again.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  // Executive Dashboard Sidebar <NAME_EMAIL>
  const executiveSidebarItems = [
    { label: 'Executive Dashboard', icon: <ExecutiveIcon />, index: 0 },
    { label: 'Content Dashboard', icon: <ContentIcon />, index: 1 },
    { label: 'Business Intelligence', icon: <IntelligenceIcon />, index: 2 },
    { label: 'User Management', icon: <PeopleIcon />, index: 3 },
    { label: 'Content Approval', icon: <ApprovalIcon />, index: 4 },
    { label: 'Financial Operations', icon: <AttachMoneyIcon />, index: 5 },
    { label: 'Revenue Analytics', icon: <TrendingUpIcon />, index: 6 },
    { label: 'Marketing Hub', icon: <MarketingIcon />, index: 7 },
    { label: 'Customer Success', icon: <CustomerSuccessIcon />, index: 8 },
    { label: 'HR Management', icon: <GroupIcon />, index: 9 },
    { label: 'Executive Reports', icon: <ReportsIcon />, index: 10 },
    { label: 'Security Center', icon: <SecurityCenterIcon />, index: 11 },
    { label: 'System Administration', icon: <SystemAdminIcon />, index: 12 },
  ];

  // Regular admin sidebar items (for non-executive users)
  const regularSidebarItems = [
    { label: 'Overview', icon: <DashboardIcon />, index: 0 },
    { label: 'Game Management', icon: <SportsEsportsIcon />, index: 1 },
    { label: 'Support Tickets', icon: <SupportAgentIcon />, index: 2 },
    { label: 'User Management', icon: <PeopleIcon />, index: 3 },
    { label: 'Analytics', icon: <AnalyticsIcon />, index: 4 },
    { label: 'Settings', icon: <SettingsIcon />, index: 5 },
  ];

  // Use executive sidebar for super admin, regular for others
  const sidebarItems = isSuperAdmin ? executiveSidebarItems : regularSidebarItems;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading admin dashboard...</Typography>
      </Box>
    );
  }

  // Require authentication for admin access
  if (!user) {
    router.push('/login');
    return null;
  }

  // Check if user is authorized admin
  if (!isSuperAdmin) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Alert severity="error">
          <Typography variant="h6">Access Denied</Typography>
          <Typography>You don't have permission to access the admin dashboard.</Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            Only <EMAIL> has admin access.
          </Typography>
        </Alert>
      </Box>
    );
  }

  console.log('🎯 Rendering admin dashboard with:', {
    gamesCount: games.length,
    ticketsCount: supportTickets.length,
    pendingGames: games.filter(g => g.status === 'pending').length,
  });

  return (
    <DashboardContainer>
      {/* Mobile Header */}
      {isMobile && (
        <AppBar position="fixed" sx={{ bgcolor: 'background.paper', color: 'text.primary' }}>
          <Toolbar>
            <IconButton onClick={() => setSidebarOpen(true)} edge="start">
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
              Admin Dashboard
            </Typography>
            <IconButton>
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            background: `linear-gradient(180deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
            backdropFilter: 'blur(20px)',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" fontWeight="bold" color="primary.main" gutterBottom>
            Gamestorme
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            {isSuperAdmin ? 'Executive Dashboard' : 'Admin Dashboard'}
          </Typography>
          {isSuperAdmin && (
            <Chip
              label="CEO/FOUNDER"
              size="small"
              color="error"
              sx={{ mt: 1, fontWeight: 'bold' }}
            />
          )}
        </Box>

        <Divider />

        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar src={user?.photoURL || ''} sx={{ mr: 2, bgcolor: 'error.main' }}>
              {user?.displayName?.[0] || user?.email?.[0] || 'A'}
            </Avatar>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                {user?.displayName || (isSuperAdmin ? 'Joel Gamestorme' : 'Admin')}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {isSuperAdmin ? 'CEO & Founder' : 'Administrator'}
              </Typography>
              {isSuperAdmin && (
                <Typography variant="caption" color="error.main" display="block">
                  Executive Access - All Systems
                </Typography>
              )}
            </Box>
          </Box>

          {platformStats && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Platform Health: {platformStats.pendingGames > 5 ? 'Needs Attention' : 'Good'}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={platformStats.pendingGames > 5 ? 60 : 90}
                color={platformStats.pendingGames > 5 ? 'warning' : 'success'}
                sx={{ mt: 0.5 }}
              />
            </Box>
          )}
        </Box>

        <Divider />

        <List sx={{ px: 2, py: 1 }}>
          {sidebarItems.map((item) => (
            <ListItem
              key={item.index}
              button
              selected={activeTab === item.index}
              onClick={() => {
                setActiveTab(item.index);
                if (isMobile) setSidebarOpen(false);
              }}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&.Mui-selected': {
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.15),
                  },
                },
              }}
            >
              <ListItemIcon sx={{ color: activeTab === item.index ? 'primary.main' : 'text.secondary' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: activeTab === item.index ? 'bold' : 'normal',
                  color: activeTab === item.index ? 'primary.main' : 'text.primary',
                }}
              />
              {/* Show badges for pending items */}
              {item.index === 1 && platformStats && platformStats.pendingGames > 0 && (
                <Badge badgeContent={platformStats.pendingGames} color="warning" />
              )}
              {item.index === 2 && platformStats && platformStats.openTickets > 0 && (
                <Badge badgeContent={platformStats.openTickets} color="error" />
              )}
            </ListItem>
          ))}
        </List>

        <Box sx={{ mt: 'auto', p: 2 }}>
          <Button
            fullWidth
            variant="contained"
            color="error"
            startIcon={<LogoutIcon />}
            onClick={() => {
              // For now, redirect to login page
              router.push('/login');
            }}
            sx={{
              background: 'linear-gradient(45deg, #f44336 30%, #ff5722 90%)',
              boxShadow: '0 3px 5px 2px rgba(244, 67, 54, .3)',
            }}
          >
            Sign Out
          </Button>
        </Box>
      </Drawer>

      {/* Main Content */}
      <MainContent sx={{ mt: isMobile ? 8 : 0 }}>
        <Container maxWidth="xl">
          {/* Executive Dashboard Tab */}
          {activeTab === 0 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    Executive Dashboard
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    CEO & Founder - Complete Platform Overview
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => window.location.reload()}
                >
                  Refresh Data
                </Button>
              </Box>

              {/* Executive Status Alert */}
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  👑 Executive Access Active | Platform Status: Operational | Games: {games.length} | Revenue: ${(platformStats?.totalRevenue || 0).toFixed(2)}
                </Typography>
              </Alert>

              {/* Executive KPI Dashboard */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
                    color: 'white',
                    '&:hover': { transform: 'translateY(-8px)' }
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AttachMoneyIcon sx={{ fontSize: 50, mb: 1, opacity: 0.9 }} />
                      <Typography variant="h3" fontWeight="bold">
                        ${(platformStats?.totalRevenue || 12847).toFixed(0)}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Total Revenue
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">
                          +{((platformStats?.totalRevenue || 12847) * 0.15).toFixed(0)} This Month
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={75}
                        sx={{
                          mt: 2,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                          '& .MuiLinearProgress-bar': { backgroundColor: 'rgba(255,255,255,0.8)' }
                        }}
                      />
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)',
                    color: 'white',
                    '&:hover': { transform: 'translateY(-8px)' }
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SportsEsportsIcon sx={{ fontSize: 50, mb: 1, opacity: 0.9 }} />
                      <Typography variant="h3" fontWeight="bold">
                        {platformStats?.totalGames || 47}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Platform Games
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 1 }}>
                        <Chip
                          label={`${platformStats?.pendingGames || 8} Pending`}
                          size="small"
                          sx={{ backgroundColor: 'rgba(255,193,7,0.8)', color: 'white' }}
                        />
                        <Chip
                          label={`${platformStats?.approvedGames || 39} Live`}
                          size="small"
                          sx={{ backgroundColor: 'rgba(76,175,80,0.8)', color: 'white' }}
                        />
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={85}
                        sx={{
                          mt: 2,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                          '& .MuiLinearProgress-bar': { backgroundColor: 'rgba(255,255,255,0.8)' }
                        }}
                      />
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #ff9800 0%, #ff5722 100%)',
                    color: 'white',
                    '&:hover': { transform: 'translateY(-8px)' }
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <PeopleIcon sx={{ fontSize: 50, mb: 1, opacity: 0.9 }} />
                      <Typography variant="h3" fontWeight="bold">
                        {platformStats?.totalDevelopers || 156}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Developer Partners
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <PersonAddIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">
                          {platformStats?.activeDevelopers || 89} Active This Month
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={92}
                        sx={{
                          mt: 2,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                          '& .MuiLinearProgress-bar': { backgroundColor: 'rgba(255,255,255,0.8)' }
                        }}
                      />
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',
                    color: 'white',
                    '&:hover': { transform: 'translateY(-8px)' }
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <TrendingUpIcon sx={{ fontSize: 50, mb: 1, opacity: 0.9 }} />
                      <Typography variant="h3" fontWeight="bold">
                        {(platformStats?.totalDownloads || 28934).toLocaleString()}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Total Downloads
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <SpeedIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">
                          Platform Growth: +32%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={68}
                        sx={{
                          mt: 2,
                          backgroundColor: 'rgba(255,255,255,0.3)',
                          '& .MuiLinearProgress-bar': { backgroundColor: 'rgba(255,255,255,0.8)' }
                        }}
                      />
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>

              {/* Executive Command Center */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
                    color: 'white',
                    border: '1px solid #ff5722'
                  }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <ExecutiveIcon sx={{ fontSize: 30, mr: 1, color: '#ff5722' }} />
                        <Typography variant="h5" fontWeight="bold">
                          🚨 Executive Command Center
                        </Typography>
                      </Box>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <Box sx={{
                            p: 2,
                            borderRadius: 2,
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            border: '1px solid rgba(255, 193, 7, 0.3)'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <ApprovalIcon sx={{ color: '#ffc107', mr: 1 }} />
                              <Typography variant="h4" fontWeight="bold" color="#ffc107">
                                {platformStats?.pendingGames || 8}
                              </Typography>
                            </Box>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>
                              Games Awaiting Executive Approval
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1, borderColor: '#ffc107', color: '#ffc107' }}
                              onClick={() => setActiveTab(4)}
                            >
                              Review Now
                            </Button>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Box sx={{
                            p: 2,
                            borderRadius: 2,
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            border: '1px solid rgba(244, 67, 54, 0.3)'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <CustomerSuccessIcon sx={{ color: '#f44336', mr: 1 }} />
                              <Typography variant="h4" fontWeight="bold" color="#f44336">
                                {platformStats?.openTickets || 12}
                              </Typography>
                            </Box>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>
                              Critical Support Issues
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1, borderColor: '#f44336', color: '#f44336' }}
                              onClick={() => setActiveTab(8)}
                            >
                              Handle Now
                            </Button>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <Box sx={{
                            p: 2,
                            borderRadius: 2,
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            border: '1px solid rgba(76, 175, 80, 0.3)'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <TrendingUpIcon sx={{ color: '#4caf50', mr: 1 }} />
                              <Typography variant="h4" fontWeight="bold" color="#4caf50">
                                +32%
                              </Typography>
                            </Box>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>
                              Revenue Growth This Quarter
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1, borderColor: '#4caf50', color: '#4caf50' }}
                              onClick={() => setActiveTab(6)}
                            >
                              View Analytics
                            </Button>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #673ab7 0%, #9c27b0 100%)',
                    color: 'white'
                  }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <InsightsIcon sx={{ fontSize: 30, mr: 1 }} />
                        <Typography variant="h6" fontWeight="bold">
                          AI Business Intelligence
                        </Typography>
                      </Box>
                      <List sx={{ p: 0 }}>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemText
                            primary="Platform Health Score"
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={94}
                                  sx={{
                                    flexGrow: 1,
                                    backgroundColor: 'rgba(255,255,255,0.3)',
                                    '& .MuiLinearProgress-bar': { backgroundColor: '#4caf50' }
                                  }}
                                />
                                <Typography variant="caption" fontWeight="bold">94%</Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemText
                            primary="Developer Satisfaction"
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={89}
                                  sx={{
                                    flexGrow: 1,
                                    backgroundColor: 'rgba(255,255,255,0.3)',
                                    '& .MuiLinearProgress-bar': { backgroundColor: '#2196f3' }
                                  }}
                                />
                                <Typography variant="caption" fontWeight="bold">89%</Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemText
                            primary="Market Position"
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={76}
                                  sx={{
                                    flexGrow: 1,
                                    backgroundColor: 'rgba(255,255,255,0.3)',
                                    '& .MuiLinearProgress-bar': { backgroundColor: '#ff9800' }
                                  }}
                                />
                                <Typography variant="caption" fontWeight="bold">Top 3</Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                      </List>
                      <Button
                        fullWidth
                        variant="outlined"
                        sx={{
                          mt: 2,
                          borderColor: 'rgba(255,255,255,0.5)',
                          color: 'white',
                          '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
                        }}
                        onClick={() => setActiveTab(2)}
                      >
                        View Full Intelligence
                      </Button>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>

              {/* Recent Activity */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Recent Game Submissions ({games.filter(g => g.status === 'pending').length} Pending)
                      </Typography>
                      <List>
                        {games.filter(g => g.status === 'pending').slice(0, 5).map((game) => (
                          <ListItem key={game.id} disablePadding>
                            <ListItemIcon>
                              <Avatar src={game.images?.thumbnail || ''} sx={{ width: 40, height: 40 }}>
                                {game.title?.[0] || 'G'}
                              </Avatar>
                            </ListItemIcon>
                            <ListItemText
                              primary={game.title || 'Untitled Game'}
                              secondary={`by ${game.developer?.name || 'Unknown Developer'} • ${game.createdAt?.toLocaleDateString() || 'Unknown date'}`}
                            />
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                size="small"
                                color="success"
                                onClick={() => handleGameApproval(game.id, 'approve')}
                              >
                                <CheckCircleIcon />
                              </IconButton>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => {
                                  setSelectedGame(game);
                                  setGameDialogOpen(true);
                                }}
                              >
                                <CancelIcon />
                              </IconButton>
                            </Box>
                          </ListItem>
                        ))}
                      </List>
                      {games.filter(g => g.status === 'pending').length === 0 && (
                        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                          No pending game submissions
                        </Typography>
                      )}
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Recent Support Tickets ({supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').length} Open)
                      </Typography>
                      <List>
                        {supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').slice(0, 5).map((ticket) => (
                          <ListItem key={ticket.id} disablePadding>
                            <ListItemIcon>
                              <PriorityHighIcon
                                color={
                                  ticket.priority === 'urgent' ? 'error' :
                                  ticket.priority === 'high' ? 'warning' :
                                  ticket.priority === 'medium' ? 'info' : 'disabled'
                                }
                              />
                            </ListItemIcon>
                            <ListItemText
                              primary={ticket.title}
                              secondary={`${ticket.developerName} • ${ticket.category} • ${ticket.createdAt.toLocaleDateString()}`}
                            />
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => {
                                setSelectedTicket(ticket);
                                setTicketDialogOpen(true);
                              }}
                            >
                              <ReplyIcon />
                            </IconButton>
                          </ListItem>
                        ))}
                      </List>
                      {supportTickets.filter(t => t.status === 'open' || t.status === 'in-progress').length === 0 && (
                        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                          No open support tickets
                        </Typography>
                      )}
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Content Dashboard Tab */}
          {activeTab === 1 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Content Dashboard
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  📝 Content Management - Oversee all platform content, games, and media
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <ContentIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {games.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Content Items
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <ApprovalIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {games.filter(g => g.status === 'pending').length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Pending Approval
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <CheckCircleIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {games.filter(g => g.status === 'approved').length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Published Content
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Business Intelligence Tab */}
          {activeTab === 2 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Business Intelligence
              </Typography>
              <Alert severity="success" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  🧠 AI-Powered Analytics - Deep insights into platform performance and trends
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Platform Intelligence
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="User Engagement Score"
                            secondary="AI-calculated engagement metrics across all touchpoints"
                          />
                          <Typography variant="h6" color="success.main">94%</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Revenue Prediction"
                            secondary="ML-based revenue forecasting for next quarter"
                          />
                          <Typography variant="h6" color="primary.main">+32%</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Market Opportunity"
                            secondary="Identified growth opportunities in gaming segments"
                          />
                          <Typography variant="h6" color="info.main">High</Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Competitive Analysis
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Market Position"
                            secondary="Gamestorme vs competitors in gaming platform space"
                          />
                          <Chip label="Top 3" color="success" size="small" />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Developer Retention"
                            secondary="Compared to industry average"
                          />
                          <Chip label="+15%" color="success" size="small" />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Revenue Per User"
                            secondary="Platform monetization efficiency"
                          />
                          <Chip label="Above Avg" color="info" size="small" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* User Management Tab */}
          {activeTab === 3 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                User Management
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  👥 Complete user ecosystem management - Developers, gamers, and staff
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <PeopleIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalDevelopers || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Developers
                      </Typography>
                      <Typography variant="caption" color="success.main">
                        {platformStats?.activeDevelopers || 0} Active
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SportsEsportsIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalUsers || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Gamers
                      </Typography>
                      <Typography variant="caption" color="info.main">
                        {platformStats?.activeUsers || 0} Active
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <WorkIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        12
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Staff Members
                      </Typography>
                      <Typography variant="caption" color="success.main">
                        All Active
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Content Approval Tab */}
          {activeTab === 4 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4" fontWeight="bold">
                  Content Approval System ({games.length} Total)
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    size="small"
                    placeholder="Search games..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                    }}
                  />
                  <Button variant="outlined" startIcon={<FilterListIcon />}>
                    Filter
                  </Button>
                </Box>
              </Box>

              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  ⚖️ Executive Content Approval - Final authority on all platform content decisions
                </Typography>
              </Alert>

              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Game</TableCell>
                      <TableCell>Developer</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Submitted</TableCell>
                      <TableCell>Downloads</TableCell>
                      <TableCell>Revenue</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {games
                      .filter(game =>
                        (game.title || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                        (game.developer?.name || '').toLowerCase().includes(searchQuery.toLowerCase())
                      )
                      .map((game) => (
                        <TableRow key={game.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar src={game.images?.thumbnail || ''} sx={{ width: 40, height: 40 }}>
                                {game.title?.[0] || 'G'}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  {game.title}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {game.genre?.join(', ') || 'No genre'}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">{game.developer?.name || 'Unknown Developer'}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {game.developer?.email || 'No email'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={game.status}
                              color={
                                game.status === 'approved' ? 'success' :
                                game.status === 'pending' ? 'warning' : 'error'
                              }
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {game.createdAt.toLocaleDateString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {(game.stats?.downloads || 0).toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              ${((game.stats?.downloads || 0) * (game.pricing?.price || 0)).toFixed(2)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setSelectedGame(game);
                                  setGameDialogOpen(true);
                                }}
                              >
                                <VisibilityIcon />
                              </IconButton>
                              {game.status === 'pending' && (
                                <>
                                  <IconButton
                                    size="small"
                                    color="success"
                                    onClick={() => handleGameApproval(game.id, 'approve')}
                                  >
                                    <CheckCircleIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => {
                                      setSelectedGame(game);
                                      setGameDialogOpen(true);
                                    }}
                                  >
                                    <CancelIcon />
                                  </IconButton>
                                </>
                              )}
                              {game.status === 'approved' && (
                                <>
                                  <IconButton
                                    size="small"
                                    color={(game as any).featured ? "primary" : "default"}
                                    onClick={() => handleGameFeature(game.id, !(game as any).featured)}
                                    title={(game as any).featured ? "Unfeature Game" : "Feature Game"}
                                  >
                                    <StarIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    color="warning"
                                    onClick={() => handleGameApproval(game.id, 'reject', 'Admin review required')}
                                    title="Unpublish Game"
                                  >
                                    <UnpublishedIcon />
                                  </IconButton>
                                </>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </motion.div>
          )}

          {/* Financial Operations Tab */}
          {activeTab === 5 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    💰 Financial Operations Center
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    Executive Financial Control & Revenue Management
                  </Typography>
                </Box>
                <Chip
                  label="REAL-TIME"
                  color="success"
                  sx={{ fontWeight: 'bold' }}
                  icon={<TimelineIcon />}
                />
              </Box>

              {/* Financial KPIs */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                    color: 'white'
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <MonetizationOnIcon sx={{ fontSize: 50, mb: 1 }} />
                      <Typography variant="h3" fontWeight="bold">
                        ${(platformStats?.totalRevenue || 128470).toLocaleString()}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Total Revenue
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">+18.5% vs last quarter</Typography>
                      </Box>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #2196f3 0%, #42a5f5 100%)',
                    color: 'white'
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <PaymentIcon sx={{ fontSize: 50, mb: 1 }} />
                      <Typography variant="h3" fontWeight="bold">
                        ${((platformStats?.totalRevenue || 128470) * 0.25).toLocaleString()}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Monthly Revenue
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">+12.3% vs last month</Typography>
                      </Box>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
                    color: 'white'
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AccountBalanceIcon sx={{ fontSize: 50, mb: 1 }} />
                      <Typography variant="h3" fontWeight="bold">
                        ${((platformStats?.totalRevenue || 128470) * 0.7).toLocaleString()}
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Net Profit
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <BarChartIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">70% profit margin</Typography>
                      </Box>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={3}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)',
                    color: 'white'
                  }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <TrendingUpIcon sx={{ fontSize: 50, mb: 1 }} />
                      <Typography variant="h3" fontWeight="bold">
                        +32%
                      </Typography>
                      <Typography variant="body1" sx={{ opacity: 0.9 }}>
                        Growth Rate
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                        <TimelineIcon sx={{ fontSize: 16, mr: 0.5 }} />
                        <Typography variant="caption">Year over year</Typography>
                      </Box>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>

              {/* Financial Breakdown */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={8}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        💼 Revenue Breakdown & Financial Analysis
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
                            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                              Revenue Sources
                            </Typography>
                            <List dense>
                              <ListItem>
                                <ListItemText
                                  primary="Game Sales (Premium)"
                                  secondary="Direct game purchases"
                                />
                                <Typography variant="h6" color="success.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.45).toLocaleString()}
                                </Typography>
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Platform Fees (30%)"
                                  secondary="Developer revenue sharing"
                                />
                                <Typography variant="h6" color="primary.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.30).toLocaleString()}
                                </Typography>
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Premium Services"
                                  secondary="Advanced developer tools"
                                />
                                <Typography variant="h6" color="info.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.15).toLocaleString()}
                                </Typography>
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Advertising Revenue"
                                  secondary="Platform advertising"
                                />
                                <Typography variant="h6" color="warning.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.10).toLocaleString()}
                                </Typography>
                              </ListItem>
                            </List>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
                            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                              Operating Expenses
                            </Typography>
                            <List dense>
                              <ListItem>
                                <ListItemText
                                  primary="Infrastructure & Hosting"
                                  secondary="Firebase, CDN, servers"
                                />
                                <Typography variant="h6" color="error.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.08).toLocaleString()}
                                </Typography>
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Staff & Payroll"
                                  secondary="Employee salaries & benefits"
                                />
                                <Typography variant="h6" color="error.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.15).toLocaleString()}
                                </Typography>
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Marketing & Advertising"
                                  secondary="User acquisition campaigns"
                                />
                                <Typography variant="h6" color="error.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.05).toLocaleString()}
                                </Typography>
                              </ListItem>
                              <ListItem>
                                <ListItemText
                                  primary="Legal & Compliance"
                                  secondary="Legal fees, compliance costs"
                                />
                                <Typography variant="h6" color="error.main">
                                  ${((platformStats?.totalRevenue || 128470) * 0.02).toLocaleString()}
                                </Typography>
                              </ListItem>
                            </List>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard sx={{
                    background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
                    color: 'white'
                  }}>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        🎯 Financial Health Score
                      </Typography>
                      <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <Typography variant="h2" fontWeight="bold" color="#4caf50">
                          94
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.8 }}>
                          Excellent Financial Health
                        </Typography>
                      </Box>
                      <List dense>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemText
                            primary="Profit Margin"
                            secondary={
                              <LinearProgress
                                variant="determinate"
                                value={70}
                                sx={{
                                  mt: 1,
                                  backgroundColor: 'rgba(255,255,255,0.2)',
                                  '& .MuiLinearProgress-bar': { backgroundColor: '#4caf50' }
                                }}
                              />
                            }
                          />
                          <Typography variant="caption">70%</Typography>
                        </ListItem>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemText
                            primary="Cash Flow"
                            secondary={
                              <LinearProgress
                                variant="determinate"
                                value={85}
                                sx={{
                                  mt: 1,
                                  backgroundColor: 'rgba(255,255,255,0.2)',
                                  '& .MuiLinearProgress-bar': { backgroundColor: '#2196f3' }
                                }}
                              />
                            }
                          />
                          <Typography variant="caption">Positive</Typography>
                        </ListItem>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemText
                            primary="Growth Rate"
                            secondary={
                              <LinearProgress
                                variant="determinate"
                                value={92}
                                sx={{
                                  mt: 1,
                                  backgroundColor: 'rgba(255,255,255,0.2)',
                                  '& .MuiLinearProgress-bar': { backgroundColor: '#ff9800' }
                                }}
                              />
                            }
                          />
                          <Typography variant="caption">+32% YoY</Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Revenue Analytics Tab */}
          {activeTab === 6 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Revenue Analytics
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  📊 Advanced Revenue Intelligence - Deep dive into platform monetization
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Revenue Breakdown
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Game Sales"
                            secondary="Direct game purchases and downloads"
                          />
                          <Typography variant="h6" color="success.main">
                            ${((platformStats?.totalRevenue || 0) * 0.7).toFixed(2)}
                          </Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Platform Fees"
                            secondary="Developer revenue sharing (30%)"
                          />
                          <Typography variant="h6" color="primary.main">
                            ${((platformStats?.totalRevenue || 0) * 0.3).toFixed(2)}
                          </Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Premium Features"
                            secondary="Advanced developer tools and analytics"
                          />
                          <Typography variant="h6" color="info.main">
                            ${((platformStats?.totalRevenue || 0) * 0.1).toFixed(2)}
                          </Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Performance Metrics
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Average Revenue Per User (ARPU)"
                            secondary="Monthly recurring revenue per active user"
                          />
                          <Typography variant="h6" color="success.main">
                            ${(((platformStats?.totalRevenue || 0) / Math.max(platformStats?.totalUsers || 1, 1)) * 12).toFixed(2)}
                          </Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Customer Lifetime Value (CLV)"
                            secondary="Projected lifetime value per user"
                          />
                          <Typography variant="h6" color="primary.main">
                            ${(((platformStats?.totalRevenue || 0) / Math.max(platformStats?.totalUsers || 1, 1)) * 24).toFixed(2)}
                          </Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Revenue Growth Rate"
                            secondary="Month-over-month growth percentage"
                          />
                          <Typography variant="h6" color="info.main">
                            +25.3%
                          </Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Marketing Hub Tab */}
          {activeTab === 7 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Marketing Hub
              </Typography>
              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  📢 Strategic Marketing Command Center - Campaign management and brand oversight
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Active Campaigns
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <MarketingIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Developer Acquisition Campaign"
                            secondary="Targeting indie game developers - ROI: +180%"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <MarketingIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Gamer Engagement Initiative"
                            secondary="Social media and community building - Reach: 50K+"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <MarketingIcon color="info" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Brand Awareness Campaign"
                            secondary="Multi-channel brand positioning - Impressions: 2M+"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Marketing Performance
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Customer Acquisition Cost (CAC)"
                            secondary="Average cost to acquire new users"
                          />
                          <Typography variant="h6" color="success.main">$12.50</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Return on Ad Spend (ROAS)"
                            secondary="Revenue generated per dollar spent"
                          />
                          <Typography variant="h6" color="primary.main">4.2x</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Brand Sentiment Score"
                            secondary="Social media and review sentiment analysis"
                          />
                          <Typography variant="h6" color="info.main">+87%</Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Customer Success Tab */}
          {activeTab === 8 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Customer Success
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  🎯 Customer Success Management - Ensuring developer and gamer satisfaction
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <CustomerSuccessIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        92%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Customer Satisfaction
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SupportAgentIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.openTickets || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Open Support Tickets
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <ChatIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        2.3h
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Avg Response Time
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* HR Management Tab */}
          {activeTab === 9 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                HR Management
              </Typography>
              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  👥 Human Resources - Complete staff and organizational management
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Staff Overview
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <SupervisorAccountIcon color="error" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Executive Team"
                            secondary="Joel Gamestorme (CEO & Founder)"
                          />
                          <Chip label="1" color="error" size="small" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <WorkIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Administrative Staff"
                            secondary="Platform administrators and managers"
                          />
                          <Chip label="5" color="primary" size="small" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <SupportAgentIcon color="secondary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Support Team"
                            secondary="Customer success and technical support"
                          />
                          <Chip label="6" color="secondary" size="small" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        HR Metrics
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Employee Satisfaction"
                            secondary="Latest quarterly survey results"
                          />
                          <Typography variant="h6" color="success.main">94%</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Retention Rate"
                            secondary="12-month employee retention"
                          />
                          <Typography variant="h6" color="primary.main">96%</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Performance Score"
                            secondary="Average team performance rating"
                          />
                          <Typography variant="h6" color="info.main">4.7/5</Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Executive Reports Tab */}
          {activeTab === 10 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Executive Reports
              </Typography>
              <Alert severity="success" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  📈 Executive Intelligence - Comprehensive business reporting and insights
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Financial Reports
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Monthly P&L Statement"
                            secondary="Profit and loss analysis for current month"
                          />
                          <Button size="small" variant="outlined">View</Button>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Quarterly Business Review"
                            secondary="Comprehensive quarterly performance report"
                          />
                          <Button size="small" variant="outlined">View</Button>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Annual Financial Summary"
                            secondary="Year-end financial performance and projections"
                          />
                          <Button size="small" variant="outlined">View</Button>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Operational Reports
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Platform Performance Report"
                            secondary="Technical performance and uptime metrics"
                          />
                          <Button size="small" variant="outlined">View</Button>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="User Engagement Analysis"
                            secondary="Developer and gamer engagement patterns"
                          />
                          <Button size="small" variant="outlined">View</Button>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Security Audit Report"
                            secondary="Platform security assessment and compliance"
                          />
                          <Button size="small" variant="outlined">View</Button>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Security Center Tab */}
          {activeTab === 11 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Security Center
              </Typography>
              <Alert severity="error" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  🔒 Executive Security Command - Platform security and compliance oversight
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SecurityCenterIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        99.9%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Security Score
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SecurityIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        0
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Security Incidents
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <CheckCircleIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        100%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Compliance Status
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* System Administration Tab */}
          {activeTab === 12 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                System Administration
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  ⚙️ System Control Center - Complete platform infrastructure management
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        System Status
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircleIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Firebase Services"
                            secondary="Database, Authentication, Storage - All Operational"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircleIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Payment Processing"
                            secondary="Stripe integration - Fully functional"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircleIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="CDN & Hosting"
                            secondary="Vercel deployment - 99.9% uptime"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        System Metrics
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Platform Uptime"
                            secondary="Last 30 days availability"
                          />
                          <Typography variant="h6" color="success.main">99.97%</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Response Time"
                            secondary="Average API response time"
                          />
                          <Typography variant="h6" color="primary.main">145ms</Typography>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Error Rate"
                            secondary="Platform error percentage"
                          />
                          <Typography variant="h6" color="success.main">0.03%</Typography>
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* User Management Tab */}
          {activeTab === 3 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                User Management
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <PeopleIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalDevelopers || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Developers
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <GroupIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {platformStats?.totalUsers || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Users
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={4}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <SupervisorAccountIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {isSuperAdmin ? '1' : '0'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Super Admins
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Analytics Tab */}
          {activeTab === 4 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Platform Analytics
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Revenue Overview
                      </Typography>
                      <Typography variant="h3" color="success.main">
                        ${(platformStats?.totalRevenue || 0).toFixed(2)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Platform Revenue
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Download Statistics
                      </Typography>
                      <Typography variant="h3" color="primary.main">
                        {(platformStats?.totalDownloads || 0).toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Downloads
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Financial Management Tab - Super Admin Only */}
          {activeTab === 5 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Financial Management
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  🔒 Super Admin Access - Full financial oversight and management capabilities
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AttachMoneyIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        ${(platformStats?.totalRevenue || 0).toFixed(2)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Revenue
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <PaymentIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        ${(platformStats?.monthlyRevenue || 0).toFixed(2)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Monthly Revenue
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AccountBalanceIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {games.filter(g => !g.pricing?.isFree).length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Paid Games
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={3}>
                  <MetricCard>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <TrendingUpIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h4" fontWeight="bold">
                        {((platformStats?.totalRevenue || 0) / Math.max(platformStats?.totalGames || 1, 1)).toFixed(2)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Avg Revenue/Game
                      </Typography>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* HR Management Tab - Super Admin Only */}
          {activeTab === 6 && isSuperAdmin && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                HR Management
              </Typography>
              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  👥 Human Resources - Staff management, hiring, and organizational oversight
                </Typography>
              </Alert>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Staff Overview
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <SupervisorAccountIcon color="error" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Super Administrators"
                            secondary="1 Active (Joel - Legacy Admin)"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <WorkIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Platform Administrators"
                            secondary="Manage day-to-day operations"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <SupportAgentIcon color="secondary" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Support Staff"
                            secondary="Handle user support and tickets"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        HR Actions
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Button variant="outlined" startIcon={<GroupIcon />}>
                          Manage Staff Accounts
                        </Button>
                        <Button variant="outlined" startIcon={<WorkIcon />}>
                          Role Assignments
                        </Button>
                        <Button variant="outlined" startIcon={<SecurityIcon />}>
                          Permission Management
                        </Button>
                        <Button variant="outlined" startIcon={<ReportIcon />}>
                          Staff Performance Reports
                        </Button>
                      </Box>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Settings Tab */}
          {activeTab === (isSuperAdmin ? 15 : 5) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                {isSuperAdmin ? 'Super Admin Settings' : 'Admin Settings'}
              </Typography>
              {isSuperAdmin && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  <Typography variant="body2">
                    ⚠️ LEGACY ADMIN - You have unrestricted access to all platform systems and data
                  </Typography>
                </Alert>
              )}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Platform Configuration
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Game Approval Settings"
                            secondary="Configure automatic approval rules"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Revenue Sharing"
                            secondary="Set developer revenue percentages"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Platform Policies"
                            secondary="Update terms of service and guidelines"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <MetricCard>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        System Status
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircleIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Firebase Connection"
                            secondary="Connected and operational"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircleIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Payment Processing"
                            secondary="All systems operational"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircleIcon color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="User Authentication"
                            secondary="Secure and functional"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </MetricCard>
                </Grid>
              </Grid>
            </motion.div>
          )}

          {/* Additional Super Admin Tabs */}
          {activeTab === 7 && isSuperAdmin && (
            <Typography variant="h4">Business Operations - Coming Soon</Typography>
          )}
          {activeTab === 8 && isSuperAdmin && (
            <Typography variant="h4">Security & Compliance - Coming Soon</Typography>
          )}
          {activeTab === 9 && isSuperAdmin && (
            <Typography variant="h4">Reports & Audits - Coming Soon</Typography>
          )}
          {activeTab === 10 && isSuperAdmin && (
            <Typography variant="h4">Revenue Analytics - Coming Soon</Typography>
          )}
          {activeTab === 11 && isSuperAdmin && (
            <Typography variant="h4">Payment Systems - Coming Soon</Typography>
          )}
          {activeTab === 12 && isSuperAdmin && (
            <Typography variant="h4">Banking & Finance - Coming Soon</Typography>
          )}
          {activeTab === 13 && isSuperAdmin && (
            <Typography variant="h4">Staff Management - Coming Soon</Typography>
          )}
          {activeTab === 14 && isSuperAdmin && (
            <Typography variant="h4">Super Admin Tools - Coming Soon</Typography>
          )}
        </Container>
      </MainContent>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </DashboardContainer>
  );
};

export default AdminDashboard;
