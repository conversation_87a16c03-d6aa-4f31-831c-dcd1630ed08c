import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Divider,
  InputAdornment,
  IconButton,
  useTheme,
  alpha,
  Link as MuiLink,
  CircularProgress,
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Layout from '../components/layout/Layout';
import { motion } from 'framer-motion';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import GoogleIcon from '@mui/icons-material/Google';
import FacebookIcon from '@mui/icons-material/Facebook';
import AppleIcon from '@mui/icons-material/Apple';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';

// Styled components
const LoginContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(8, 0),
  minHeight: 'calc(100vh - 200px)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const LoginPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(6),
  borderRadius: theme.shape.borderRadius * 2,
  maxWidth: 500,
  width: '100%',
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 10px 30px ${alpha(theme.palette.common.black, 0.1)}`,
}));

const SocialButton = styled(Button)(({ theme }) => ({
  padding: theme.spacing(1.5),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
  '&:hover': {
    backgroundColor: alpha(theme.palette.background.paper, 0.8),
  },
}));

const OrDivider = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  margin: theme.spacing(3, 0),
}));

const Login: React.FC = () => {
  const theme = useTheme();
  const router = useRouter();
  const { login, currentUser, userProfile, setIsAuthPage } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Set isAuthPage to true when component mounts
  useEffect(() => {
    setIsAuthPage(true);
    return () => {
      setIsAuthPage(false);
    };
  }, [setIsAuthPage]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Redirect if already logged in, but only if not on an auth page
  useEffect(() => {
    // Only redirect if user is logged in and we're on the login page intentionally
    if (currentUser && userProfile) {
      console.log('User already logged in, redirecting based on user type:', userProfile.userType);
      // Check URL query parameters for redirect
      const { redirect } = router.query;
      if (redirect && typeof redirect === 'string') {
        router.push(redirect);
      } else {
        // Default redirect based on user type
        if (userProfile.userType === 'developer') {
          router.push('/developer/dashboard');
        } else if (userProfile.userType === 'gamer') {
          router.push('/gamer/dashboard');
        } else {
          // Fallback for unknown user types
          router.push('/gamer/dashboard');
        }
      }
    }
  }, [currentUser, userProfile, router]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset errors
    setEmailError('');
    setPasswordError('');
    setError('');

    // Simple validation
    let isValid = true;

    if (!email) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    }

    if (isValid) {
      setLoading(true);
      try {
        await login(email, password);
        // Redirect will happen in the useEffect hook when currentUser and userProfile are updated
      } catch (err: any) {
        console.error('Login error:', err);
        setError(err.message || 'Failed to log in. Please check your credentials.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Layout>
      <LoginContainer>
        <Container maxWidth="sm">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <LoginPaper elevation={3}>
              <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
                Welcome Back
              </Typography>
              <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
                Sign in to your Gamestorme account
              </Typography>

              {/* Social Login Buttons */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2, mb: 3 }}>
                <SocialButton fullWidth startIcon={<GoogleIcon />}>
                  Google
                </SocialButton>
                <SocialButton fullWidth startIcon={<FacebookIcon />}>
                  Facebook
                </SocialButton>
                <SocialButton fullWidth startIcon={<AppleIcon />}>
                  Apple
                </SocialButton>
              </Box>

              {/* Divider */}
              <OrDivider>
                <Divider sx={{ flexGrow: 1 }} />
                <Typography variant="body2" color="text.secondary" sx={{ px: 2 }}>
                  OR
                </Typography>
                <Divider sx={{ flexGrow: 1 }} />
              </OrDivider>

              {/* Login Form */}
              <Box component="form" onSubmit={handleLogin}>
                {error && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  fullWidth
                  label="Email Address"
                  variant="outlined"
                  margin="normal"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  error={!!emailError}
                  helperText={emailError}
                  disabled={loading}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  label="Password"
                  variant="outlined"
                  margin="normal"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  error={!!passwordError}
                  helperText={passwordError}
                  disabled={loading}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                          disabled={loading}
                        >
                          {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1, mb: 3 }}>
                  <Link href="/forgot-password" passHref>
                    <MuiLink variant="body2" color="primary" underline="hover">
                      Forgot password?
                    </MuiLink>
                  </Link>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="primary"
                  size="large"
                  sx={{ py: 1.5, mb: 3 }}
                  disabled={loading}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'Sign In'
                  )}
                </Button>

                <Typography variant="body2" align="center">
                  Don&apos;t have an account?{' '}
                  <Link href="/signup" passHref>
                    <MuiLink color="primary" underline="hover">
                      Sign up
                    </MuiLink>
                  </Link>
                </Typography>
              </Box>
            </LoginPaper>
          </motion.div>
        </Container>
      </LoginContainer>
    </Layout>
  );
};

export default Login;
