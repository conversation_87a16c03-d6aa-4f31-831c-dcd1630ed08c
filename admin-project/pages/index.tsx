import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { Box, Typography, CircularProgress } from '@mui/material';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '../lib/firebase';

const AdminIndex: React.FC = () => {
  const router = useRouter();
  const [user, loading, error] = useAuthState(auth);

  useEffect(() => {
    if (!loading) {
      if (user && user.email === '<EMAIL>') {
        // Redirect authenticated admin to dashboard
        router.push('/admin');
      } else {
        // Redirect to login for authentication
        router.push('/login');
      }
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <Box 
        display="flex" 
        flexDirection="column"
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        sx={{ background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)' }}
      >
        <CircularProgress size={60} sx={{ color: '#ff5722', mb: 2 }} />
        <Typography variant="h6" color="white">
          Loading Gamestorme Admin...
        </Typography>
        <Typography variant="body2" color="rgba(255,255,255,0.7)" sx={{ mt: 1 }}>
          Checking authentication status
        </Typography>
      </Box>
    );
  }

  return null;
};

export default AdminIndex;
