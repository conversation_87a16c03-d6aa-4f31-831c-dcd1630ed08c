import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb, adminRealtimeDb } from '../../../lib/firebaseAdmin';

interface AnalyticsEvent {
  type: 'page_view' | 'game_view' | 'game_download' | 'game_like' | 'search' | 'user_signup' | 'purchase' | 'session_start' | 'session_end';
  userId?: string;
  gameId?: string;
  developerId?: string;
  sessionId: string;
  timestamp: Date;
  metadata?: {
    page?: string;
    referrer?: string;
    userAgent?: string;
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
    device?: {
      type: 'desktop' | 'mobile' | 'tablet';
      os?: string;
      browser?: string;
    };
    searchQuery?: string;
    purchaseAmount?: number;
    sessionDuration?: number;
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
    });
  }

  try {
    const eventData: AnalyticsEvent = req.body;

    // Validate required fields
    if (!eventData.type || !eventData.sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: type, sessionId',
      });
    }

    // Add server timestamp
    eventData.timestamp = new Date();

    // Store in Firestore for long-term analytics
    await adminDb.collection('analyticsEvents').add(eventData);

    // Update real-time counters in Realtime Database
    await updateRealTimeCounters(eventData);

    // Update game-specific metrics if applicable
    if (eventData.gameId) {
      await updateGameMetrics(eventData);
    }

    // Update developer metrics if applicable
    if (eventData.developerId) {
      await updateDeveloperMetrics(eventData);
    }

    // Update platform-wide metrics
    await updatePlatformMetrics(eventData);

    return res.status(200).json({
      success: true,
      message: 'Analytics event tracked successfully',
    });
  } catch (error) {
    console.error('Analytics tracking error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to track analytics event',
    });
  }
}

async function updateRealTimeCounters(event: AnalyticsEvent) {
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const hour = new Date().getHours();

  const updates: { [key: string]: any } = {};

  // Platform-wide counters
  updates[`platform/daily/${today}/${event.type}`] = adminRealtimeDb.ref().child(`platform/daily/${today}/${event.type}`).transaction((current) => (current || 0) + 1);
  updates[`platform/hourly/${today}/${hour}/${event.type}`] = adminRealtimeDb.ref().child(`platform/hourly/${today}/${hour}/${event.type}`).transaction((current) => (current || 0) + 1);

  // Game-specific counters
  if (event.gameId) {
    updates[`games/${event.gameId}/daily/${today}/${event.type}`] = adminRealtimeDb.ref().child(`games/${event.gameId}/daily/${today}/${event.type}`).transaction((current) => (current || 0) + 1);
    updates[`games/${event.gameId}/total/${event.type}`] = adminRealtimeDb.ref().child(`games/${event.gameId}/total/${event.type}`).transaction((current) => (current || 0) + 1);
  }

  // Developer-specific counters
  if (event.developerId) {
    updates[`developers/${event.developerId}/daily/${today}/${event.type}`] = adminRealtimeDb.ref().child(`developers/${event.developerId}/daily/${today}/${event.type}`).transaction((current) => (current || 0) + 1);
    updates[`developers/${event.developerId}/total/${event.type}`] = adminRealtimeDb.ref().child(`developers/${event.developerId}/total/${event.type}`).transaction((current) => (current || 0) + 1);
  }

  // Execute all updates
  await Promise.all(Object.values(updates));
}

async function updateGameMetrics(event: AnalyticsEvent) {
  if (!event.gameId) return;

  const gameRef = adminDb.collection('games').doc(event.gameId);

  try {
    await adminDb.runTransaction(async (transaction) => {
      const gameDoc = await transaction.get(gameRef);
      
      if (!gameDoc.exists) {
        console.warn(`Game ${event.gameId} not found for metrics update`);
        return;
      }

      const gameData = gameDoc.data();
      const currentStats = gameData?.stats || {
        downloads: 0,
        views: 0,
        likes: 0,
        reviews: 0,
      };

      const updates: any = {
        updatedAt: new Date(),
      };

      switch (event.type) {
        case 'game_view':
          updates['stats.views'] = currentStats.views + 1;
          break;
        case 'game_download':
          updates['stats.downloads'] = currentStats.downloads + 1;
          break;
        case 'game_like':
          updates['stats.likes'] = currentStats.likes + 1;
          break;
      }

      if (Object.keys(updates).length > 1) { // More than just updatedAt
        transaction.update(gameRef, updates);
      }
    });
  } catch (error) {
    console.error('Error updating game metrics:', error);
  }
}

async function updateDeveloperMetrics(event: AnalyticsEvent) {
  if (!event.developerId) return;

  const developerRef = adminDb.collection('developers').doc(event.developerId);

  try {
    await adminDb.runTransaction(async (transaction) => {
      const developerDoc = await transaction.get(developerRef);
      
      if (!developerDoc.exists) {
        // Create developer profile if it doesn't exist
        const newDeveloperData = {
          uid: event.developerId,
          stats: {
            totalViews: event.type === 'game_view' ? 1 : 0,
            totalDownloads: event.type === 'game_download' ? 1 : 0,
            totalLikes: event.type === 'game_like' ? 1 : 0,
            totalRevenue: event.type === 'purchase' ? (event.metadata?.purchaseAmount || 0) : 0,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        transaction.set(developerRef, newDeveloperData);
        return;
      }

      const developerData = developerDoc.data();
      const currentStats = developerData?.stats || {
        totalViews: 0,
        totalDownloads: 0,
        totalLikes: 0,
        totalRevenue: 0,
      };

      const updates: any = {
        updatedAt: new Date(),
      };

      switch (event.type) {
        case 'game_view':
          updates['stats.totalViews'] = currentStats.totalViews + 1;
          break;
        case 'game_download':
          updates['stats.totalDownloads'] = currentStats.totalDownloads + 1;
          break;
        case 'game_like':
          updates['stats.totalLikes'] = currentStats.totalLikes + 1;
          break;
        case 'purchase':
          updates['stats.totalRevenue'] = currentStats.totalRevenue + (event.metadata?.purchaseAmount || 0);
          break;
      }

      if (Object.keys(updates).length > 1) { // More than just updatedAt
        transaction.update(developerRef, updates);
      }
    });
  } catch (error) {
    console.error('Error updating developer metrics:', error);
  }
}

async function updatePlatformMetrics(event: AnalyticsEvent) {
  const today = new Date().toISOString().split('T')[0];
  const platformRef = adminDb.collection('platformMetrics').doc(today);

  try {
    await adminDb.runTransaction(async (transaction) => {
      const platformDoc = await transaction.get(platformRef);
      
      const currentData = platformDoc.exists ? platformDoc.data() : {
        date: today,
        totalViews: 0,
        totalDownloads: 0,
        totalUsers: 0,
        totalSessions: 0,
        totalRevenue: 0,
        uniqueUsers: new Set(),
        activeSessions: new Set(),
      };

      const updates: any = {
        updatedAt: new Date(),
      };

      switch (event.type) {
        case 'page_view':
        case 'game_view':
          updates.totalViews = (currentData.totalViews || 0) + 1;
          break;
        case 'game_download':
          updates.totalDownloads = (currentData.totalDownloads || 0) + 1;
          break;
        case 'user_signup':
          updates.totalUsers = (currentData.totalUsers || 0) + 1;
          break;
        case 'session_start':
          updates.totalSessions = (currentData.totalSessions || 0) + 1;
          break;
        case 'purchase':
          updates.totalRevenue = (currentData.totalRevenue || 0) + (event.metadata?.purchaseAmount || 0);
          break;
      }

      // Track unique users and sessions
      if (event.userId) {
        const uniqueUsers = new Set(currentData.uniqueUsers || []);
        uniqueUsers.add(event.userId);
        updates.uniqueUsers = Array.from(uniqueUsers);
      }

      if (event.sessionId) {
        const activeSessions = new Set(currentData.activeSessions || []);
        if (event.type === 'session_start') {
          activeSessions.add(event.sessionId);
        } else if (event.type === 'session_end') {
          activeSessions.delete(event.sessionId);
        }
        updates.activeSessions = Array.from(activeSessions);
      }

      if (!platformDoc.exists) {
        updates.date = today;
        transaction.set(platformRef, updates);
      } else {
        transaction.update(platformRef, updates);
      }
    });
  } catch (error) {
    console.error('Error updating platform metrics:', error);
  }
}
