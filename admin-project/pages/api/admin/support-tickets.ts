import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';

interface AdminSupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'technical' | 'billing' | 'general' | 'feature-request' | 'bug-report';
  developerId: string;
  developerName: string;
  developerEmail: string;
  assignedTo?: string;
  assignedAt?: Date;
  adminResponse?: string;
  adminId?: string;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  tags: string[];
  internalNotes?: string;
  escalated: boolean;
  responseTime?: number; // in hours
  resolutionTime?: number; // in hours
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === 'GET') {
      return await getAdminTickets(req, res);
    } else if (req.method === 'PUT') {
      return await updateAdminTicket(req, res);
    } else if (req.method === 'POST') {
      return await createAdminAction(req, res);
    } else {
      res.setHeader('Allow', ['GET', 'PUT', 'POST']);
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Admin support tickets API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
}

async function getAdminTickets(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { 
      status, 
      priority, 
      category, 
      assignedTo, 
      escalated,
      limit = '20',
      page = '1' 
    } = req.query;

    let query = adminDb.collection('supportTickets');

    // Apply filters
    if (status) {
      query = query.where('status', '==', status);
    }

    if (priority) {
      query = query.where('priority', '==', priority);
    }

    if (category) {
      query = query.where('category', '==', category);
    }

    if (assignedTo) {
      query = query.where('assignedTo', '==', assignedTo);
    }

    if (escalated === 'true') {
      query = query.where('escalated', '==', true);
    }

    // Order by priority and creation date
    query = query.orderBy('priority', 'desc').orderBy('createdAt', 'desc');

    // Apply pagination
    const limitNum = parseInt(limit as string);
    const pageNum = parseInt(page as string);
    const offset = (pageNum - 1) * limitNum;

    if (offset > 0) {
      // For pagination, we'd need to implement cursor-based pagination
      // For now, we'll use limit
      query = query.limit(limitNum);
    } else {
      query = query.limit(limitNum);
    }

    const snapshot = await query.get();

    const tickets: AdminSupportTicket[] = [];
    snapshot.forEach((doc) => {
      const data = doc.data();
      
      // Calculate response and resolution times
      const createdAt = data.createdAt?.toDate() || new Date();
      const updatedAt = data.updatedAt?.toDate() || new Date();
      const resolvedAt = data.resolvedAt?.toDate();

      let responseTime: number | undefined;
      let resolutionTime: number | undefined;

      if (data.adminResponse) {
        responseTime = Math.round((updatedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60));
      }

      if (resolvedAt) {
        resolutionTime = Math.round((resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60));
      }

      tickets.push({
        id: doc.id,
        ...data,
        createdAt,
        updatedAt,
        assignedAt: data.assignedAt?.toDate(),
        resolvedAt,
        responseTime,
        resolutionTime,
        escalated: data.escalated || false,
      } as AdminSupportTicket);
    });

    // Get summary statistics
    const summarySnapshot = await adminDb.collection('supportTickets').get();
    const allTickets = summarySnapshot.docs.map(doc => doc.data());

    const summary = {
      total: allTickets.length,
      open: allTickets.filter(t => t.status === 'open').length,
      inProgress: allTickets.filter(t => t.status === 'in-progress').length,
      resolved: allTickets.filter(t => t.status === 'resolved').length,
      urgent: allTickets.filter(t => t.priority === 'urgent').length,
      escalated: allTickets.filter(t => t.escalated === true).length,
      averageResponseTime: calculateAverageResponseTime(allTickets),
      averageResolutionTime: calculateAverageResolutionTime(allTickets),
    };

    return res.status(200).json({
      success: true,
      data: {
        tickets,
        summary,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: allTickets.length,
          totalPages: Math.ceil(allTickets.length / limitNum),
        },
      },
    });
  } catch (error) {
    console.error('Get admin tickets error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch support tickets',
    });
  }
}

async function updateAdminTicket(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { ticketId } = req.query;
    const updateData = req.body;

    if (!ticketId) {
      return res.status(400).json({
        success: false,
        error: 'Ticket ID is required',
      });
    }

    // Add admin information and timestamps
    updateData.updatedAt = new Date();
    
    if (updateData.adminResponse && !updateData.adminId) {
      // In a real app, you'd get this from the authenticated admin user
      updateData.adminId = 'admin_user_id';
    }

    if (updateData.status === 'resolved' && !updateData.resolvedAt) {
      updateData.resolvedAt = new Date();
    }

    if (updateData.assignedTo && !updateData.assignedAt) {
      updateData.assignedAt = new Date();
    }

    await adminDb.collection('supportTickets').doc(ticketId as string).update(updateData);

    // Create notification for developer
    const ticketDoc = await adminDb.collection('supportTickets').doc(ticketId as string).get();
    const ticketData = ticketDoc.data();

    if (ticketData && updateData.adminResponse) {
      await adminDb.collection('notifications').add({
        userId: ticketData.developerId,
        type: 'support_response',
        title: 'Support Ticket Update',
        message: `Your support ticket "${ticketData.title}" has been updated by our support team.`,
        ticketId: ticketId,
        read: false,
        createdAt: new Date(),
      });
    }

    // Log admin action
    await adminDb.collection('adminLogs').add({
      action: 'update_support_ticket',
      ticketId: ticketId,
      adminId: updateData.adminId || 'unknown',
      changes: updateData,
      timestamp: new Date(),
    });

    return res.status(200).json({
      success: true,
      message: 'Support ticket updated successfully',
    });
  } catch (error) {
    console.error('Update admin ticket error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to update support ticket',
    });
  }
}

async function createAdminAction(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { action, ticketId, data } = req.body;

    if (!action || !ticketId) {
      return res.status(400).json({
        success: false,
        error: 'Action and ticket ID are required',
      });
    }

    const ticketRef = adminDb.collection('supportTickets').doc(ticketId);
    const ticketDoc = await ticketRef.get();

    if (!ticketDoc.exists) {
      return res.status(404).json({
        success: false,
        error: 'Support ticket not found',
      });
    }

    const ticketData = ticketDoc.data();
    let updateData: any = {
      updatedAt: new Date(),
    };

    switch (action) {
      case 'escalate':
        updateData.escalated = true;
        updateData.priority = 'urgent';
        
        // Notify senior support team
        await adminDb.collection('adminNotifications').add({
          type: 'ticket_escalated',
          title: 'Ticket Escalated',
          message: `Support ticket "${ticketData?.title}" has been escalated`,
          ticketId: ticketId,
          priority: 'urgent',
          read: false,
          createdAt: new Date(),
        });
        break;

      case 'assign':
        updateData.assignedTo = data.adminId;
        updateData.assignedAt = new Date();
        updateData.status = 'in-progress';
        break;

      case 'close':
        updateData.status = 'closed';
        updateData.resolvedAt = new Date();
        break;

      case 'reopen':
        updateData.status = 'open';
        updateData.resolvedAt = null;
        break;

      case 'add_note':
        const currentNotes = ticketData?.internalNotes || '';
        updateData.internalNotes = currentNotes + `\n[${new Date().toISOString()}] ${data.note}`;
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid action',
        });
    }

    await ticketRef.update(updateData);

    // Log admin action
    await adminDb.collection('adminLogs').add({
      action: `support_ticket_${action}`,
      ticketId: ticketId,
      adminId: data.adminId || 'unknown',
      details: data,
      timestamp: new Date(),
    });

    return res.status(200).json({
      success: true,
      message: `Ticket ${action} completed successfully`,
    });
  } catch (error) {
    console.error('Create admin action error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to perform admin action',
    });
  }
}

function calculateAverageResponseTime(tickets: any[]): number {
  const ticketsWithResponse = tickets.filter(t => t.adminResponse && t.createdAt && t.updatedAt);
  
  if (ticketsWithResponse.length === 0) return 0;

  const totalResponseTime = ticketsWithResponse.reduce((sum, ticket) => {
    const createdAt = ticket.createdAt?.toDate ? ticket.createdAt.toDate() : new Date(ticket.createdAt);
    const updatedAt = ticket.updatedAt?.toDate ? ticket.updatedAt.toDate() : new Date(ticket.updatedAt);
    return sum + (updatedAt.getTime() - createdAt.getTime());
  }, 0);

  return Math.round(totalResponseTime / (ticketsWithResponse.length * 1000 * 60 * 60)); // Convert to hours
}

function calculateAverageResolutionTime(tickets: any[]): number {
  const resolvedTickets = tickets.filter(t => t.status === 'resolved' && t.createdAt && t.resolvedAt);
  
  if (resolvedTickets.length === 0) return 0;

  const totalResolutionTime = resolvedTickets.reduce((sum, ticket) => {
    const createdAt = ticket.createdAt?.toDate ? ticket.createdAt.toDate() : new Date(ticket.createdAt);
    const resolvedAt = ticket.resolvedAt?.toDate ? ticket.resolvedAt.toDate() : new Date(ticket.resolvedAt);
    return sum + (resolvedAt.getTime() - createdAt.getTime());
  }, 0);

  return Math.round(totalResolutionTime / (resolvedTickets.length * 1000 * 60 * 60)); // Convert to hours
}
