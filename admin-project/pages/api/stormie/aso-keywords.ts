import type { NextApiRequest, NextApiResponse } from 'next';
import { HfInference } from '@huggingface/inference';

const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

interface GameData {
  title: string;
  description?: string;
  genre?: string;
  platform?: string;
  targetAudience?: string;
  features?: string[];
}

interface KeywordsResponse {
  success: boolean;
  keywords?: {
    primary: string[];
    secondary: string[];
    longTail: string[];
    trending: string[];
    difficulty: string;
    recommendations: string[];
  };
  error?: string;
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<KeywordsResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { gameData }: { gameData: GameData } = req.body;

    if (!gameData || !gameData.title) {
      return res.status(400).json({
        success: false,
        error: 'Game title is required',
        timestamp: new Date().toISOString()
      });
    }

    const keywords = await generateASOKeywords(gameData);
    
    res.status(200).json({
      success: true,
      keywords,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('ASO keywords error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate ASO keywords',
      timestamp: new Date().toISOString()
    });
  }
}

async function generateASOKeywords(gameData: GameData) {
  const prompt = `Generate ASO (App Store Optimization) keywords for this mobile game:

Title: ${gameData.title}
Genre: ${gameData.genre || 'Mobile Game'}
Description: ${gameData.description || 'Engaging mobile game experience'}
Platform: ${gameData.platform || 'Mobile'}
Target Audience: ${gameData.targetAudience || 'Mobile gamers'}

Create a comprehensive keyword strategy with:
1. Primary keywords (5-7 high-impact terms)
2. Secondary keywords (10-15 supporting terms)
3. Long-tail keywords (5-8 specific phrases)
4. Current trending keywords in the genre

Focus on keywords with good search volume and achievable competition levels.`;

  try {
    const response = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: prompt,
      parameters: {
        max_new_tokens: 300,
        temperature: 0.6,
        return_full_text: false
      }
    });

    return parseKeywordResponse(response.generated_text, gameData);
  } catch (error) {
    console.error('AI keyword generation failed:', error);
    return generateFallbackKeywords(gameData);
  }
}

function parseKeywordResponse(aiText: string, gameData: GameData) {
  try {
    // Extract keywords from AI response
    const primary = extractKeywordSection(aiText, 'primary') || generatePrimaryKeywords(gameData);
    const secondary = extractKeywordSection(aiText, 'secondary') || generateSecondaryKeywords(gameData);
    const longTail = extractKeywordSection(aiText, 'long.tail|long-tail') || generateLongTailKeywords(gameData);
    const trending = extractKeywordSection(aiText, 'trending') || getTrendingKeywords(gameData.genre);

    return {
      primary,
      secondary,
      longTail,
      trending,
      difficulty: assessKeywordDifficulty(gameData),
      recommendations: generateKeywordRecommendations(gameData)
    };
  } catch (error) {
    return generateFallbackKeywords(gameData);
  }
}

function generateFallbackKeywords(gameData: GameData) {
  return {
    primary: generatePrimaryKeywords(gameData),
    secondary: generateSecondaryKeywords(gameData),
    longTail: generateLongTailKeywords(gameData),
    trending: getTrendingKeywords(gameData.genre),
    difficulty: assessKeywordDifficulty(gameData),
    recommendations: generateKeywordRecommendations(gameData)
  };
}

function generatePrimaryKeywords(gameData: GameData): string[] {
  const baseKeywords = [
    gameData.title.toLowerCase(),
    gameData.genre?.toLowerCase() || 'game',
    'mobile game',
    'app',
    'free'
  ];

  // Add genre-specific primary keywords
  const genreKeywords = getGenreKeywords(gameData.genre);
  return [...new Set([...baseKeywords, ...genreKeywords.slice(0, 3)])].slice(0, 7);
}

function generateSecondaryKeywords(gameData: GameData): string[] {
  const genre = gameData.genre?.toLowerCase() || 'game';
  const platform = gameData.platform?.toLowerCase() || 'mobile';
  
  const secondaryKeywords = [
    `${genre} game`,
    'entertainment',
    'fun',
    'play',
    'gaming',
    'adventure',
    'challenge',
    'strategy',
    'puzzle',
    'arcade',
    'casual',
    'multiplayer',
    'offline',
    `${platform} game`,
    'download',
    'install'
  ];

  // Add genre-specific secondary keywords
  const genreKeywords = getGenreKeywords(gameData.genre);
  return [...new Set([...secondaryKeywords, ...genreKeywords])].slice(0, 15);
}

function generateLongTailKeywords(gameData: GameData): string[] {
  const genre = gameData.genre?.toLowerCase() || 'game';
  const title = gameData.title.toLowerCase();
  
  return [
    `best ${genre} game`,
    `free ${genre} app`,
    `${genre} mobile game`,
    `offline ${genre} game`,
    `${genre} adventure game`,
    `new ${genre} 2025`,
    `${title} download`,
    `${title} free game`
  ].slice(0, 8);
}

function getTrendingKeywords(genre?: string): string[] {
  const trendingByGenre: { [key: string]: string[] } = {
    'action': ['battle royale', 'fps', 'shooter', 'combat'],
    'puzzle': ['brain training', 'logic puzzle', 'mind game', 'iq test'],
    'strategy': ['tower defense', 'city builder', 'war game', 'empire'],
    'rpg': ['fantasy rpg', 'character building', 'quest game', 'adventure rpg'],
    'casual': ['match 3', 'time management', 'relaxing game', 'easy game'],
    'racing': ['car racing', 'speed racing', 'drift game', 'racing simulator'],
    'sports': ['football game', 'basketball', 'soccer', 'sports simulation']
  };

  const baseTrending = ['new game 2025', 'viral game', 'trending game', 'popular app'];
  const genreTrending = trendingByGenre[genre?.toLowerCase() || 'action'] || [];
  
  return [...baseTrending, ...genreTrending].slice(0, 6);
}

function getGenreKeywords(genre?: string): string[] {
  const keywordMap: { [key: string]: string[] } = {
    'action': ['action', 'combat', 'fighting', 'shooter', 'battle', 'war', 'hero'],
    'puzzle': ['puzzle', 'brain', 'logic', 'mind', 'challenge', 'solve', 'think'],
    'strategy': ['strategy', 'tactical', 'planning', 'empire', 'build', 'manage'],
    'rpg': ['rpg', 'adventure', 'quest', 'fantasy', 'character', 'story', 'magic'],
    'casual': ['casual', 'easy', 'relaxing', 'simple', 'fun', 'family', 'kids'],
    'arcade': ['arcade', 'retro', 'classic', 'score', 'high-score', 'vintage'],
    'racing': ['racing', 'car', 'speed', 'drive', 'fast', 'drift', 'track'],
    'sports': ['sports', 'football', 'soccer', 'basketball', 'tennis', 'golf'],
    'simulation': ['simulation', 'sim', 'realistic', 'life', 'world', 'virtual']
  };

  return keywordMap[genre?.toLowerCase() || 'action'] || ['game', 'mobile', 'entertainment'];
}

function assessKeywordDifficulty(gameData: GameData): string {
  // Simple difficulty assessment based on genre and features
  const popularGenres = ['action', 'puzzle', 'casual', 'strategy'];
  const isPopularGenre = popularGenres.includes(gameData.genre?.toLowerCase() || '');
  
  if (isPopularGenre) {
    return 'High - Popular genre with strong competition';
  } else if (gameData.features && gameData.features.length > 3) {
    return 'Medium - Unique features may help differentiation';
  } else {
    return 'Medium-High - Standard features in competitive market';
  }
}

function generateKeywordRecommendations(gameData: GameData): string[] {
  const recommendations = [
    'Focus on long-tail keywords for better conversion rates',
    'Include your game title in keyword combinations',
    'Monitor competitor keywords and adapt strategy',
    'Test different keyword combinations in app store listings',
    'Update keywords based on seasonal trends and events'
  ];

  // Add genre-specific recommendations
  if (gameData.genre) {
    const genreRec = getGenreRecommendations(gameData.genre);
    recommendations.push(...genreRec);
  }

  return recommendations.slice(0, 6);
}

function getGenreRecommendations(genre: string): string[] {
  const recMap: { [key: string]: string[] } = {
    'action': ['Emphasize fast-paced gameplay in keywords', 'Include weapon/combat terms'],
    'puzzle': ['Use brain-training related terms', 'Target educational keywords'],
    'strategy': ['Focus on planning and tactical keywords', 'Include empire/building terms'],
    'rpg': ['Highlight story and character elements', 'Use fantasy/adventure keywords'],
    'casual': ['Target family-friendly keywords', 'Use accessibility terms']
  };

  return recMap[genre.toLowerCase()] || ['Optimize for your specific game features'];
}

function extractKeywordSection(text: string, sectionPattern: string): string[] | null {
  const regex = new RegExp(`${sectionPattern}[^:]*:([^\\n]*(?:\\n[^\\n:]*)*?)(?=\\n\\w+:|$)`, 'i');
  const match = text.match(regex);
  
  if (match) {
    const content = match[1];
    // Extract keywords from various formats (comma-separated, bullet points, etc.)
    const keywords = content
      .split(/[,\n•\-\*]/)
      .map(keyword => keyword.trim().replace(/^\d+\.?\s*/, ''))
      .filter(keyword => keyword.length > 2 && keyword.length < 30)
      .slice(0, 15);
    
    return keywords.length > 0 ? keywords : null;
  }
  
  return null;
}
