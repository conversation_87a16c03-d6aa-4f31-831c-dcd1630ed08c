import type { NextApiRequest, NextApiResponse } from 'next';
import { HfInference } from '@huggingface/inference';

const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

interface GameData {
  title: string;
  description?: string;
  genre?: string;
  platform?: string;
  targetAudience?: string;
  features?: string[];
}

interface SocialContentResponse {
  success: boolean;
  content?: {
    text: string;
    hashtags: string[];
    bestTime: string;
    engagement: string[];
    visualSuggestions: string[];
    callToAction: string;
    platform: string;
    contentType: string;
  };
  error?: string;
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SocialContentResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { gameData, platform, contentType }: { 
      gameData: GameData; 
      platform: string; 
      contentType: string; 
    } = req.body;

    if (!gameData || !gameData.title) {
      return res.status(400).json({
        success: false,
        error: 'Game title is required',
        timestamp: new Date().toISOString()
      });
    }

    const content = await generateSocialContent(gameData, platform || 'twitter', contentType || 'post');
    
    res.status(200).json({
      success: true,
      content,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Social content error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate social content',
      timestamp: new Date().toISOString()
    });
  }
}

async function generateSocialContent(gameData: GameData, platform: string, contentType: string) {
  const platformSpecs = getPlatformSpecs(platform);
  
  const prompt = `Create ${contentType} content for ${platform} to promote this game:

Game: ${gameData.title}
Genre: ${gameData.genre || 'Mobile Game'}
Description: ${gameData.description || 'Engaging gaming experience'}
Key Features: ${gameData.features?.join(', ') || 'Fun gameplay, great graphics'}
Target Audience: ${gameData.targetAudience || 'Gamers'}

Platform Requirements:
- Character limit: ${platformSpecs.characterLimit}
- Tone: ${platformSpecs.tone}
- Hashtag limit: ${platformSpecs.hashtags}

Create engaging content that:
1. Captures attention immediately
2. Highlights unique game features
3. Includes a strong call-to-action
4. Uses platform-appropriate language
5. Encourages downloads/engagement

Make it ${platformSpecs.tone} and exciting!`;

  try {
    const response = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: prompt,
      parameters: {
        max_new_tokens: 150,
        temperature: 0.8,
        return_full_text: false
      }
    });

    return formatSocialContent(response.generated_text, gameData, platform, contentType);
  } catch (error) {
    console.error('AI content generation failed:', error);
    return generateFallbackSocialContent(gameData, platform, contentType);
  }
}

function formatSocialContent(aiText: string, gameData: GameData, platform: string, contentType: string) {
  const platformSpecs = getPlatformSpecs(platform);
  
  // Clean and format AI-generated content
  let content = aiText.trim();
  
  // Ensure content fits platform limits
  if (content.length > platformSpecs.characterLimit - 50) { // Leave room for hashtags
    content = content.substring(0, platformSpecs.characterLimit - 50) + '...';
  }

  // If AI content is too short or generic, enhance it
  if (content.length < 50 || !content.includes(gameData.title)) {
    content = generatePlatformSpecificContent(gameData, platform, contentType);
  }

  return {
    text: content,
    hashtags: generateHashtags(gameData, platform),
    bestTime: getBestPostingTime(platform),
    engagement: getEngagementTips(platform),
    visualSuggestions: getVisualSuggestions(gameData, platform),
    callToAction: getCallToAction(platform, contentType),
    platform,
    contentType
  };
}

function generateFallbackSocialContent(gameData: GameData, platform: string, contentType: string) {
  const content = generatePlatformSpecificContent(gameData, platform, contentType);
  
  return {
    text: content,
    hashtags: generateHashtags(gameData, platform),
    bestTime: getBestPostingTime(platform),
    engagement: getEngagementTips(platform),
    visualSuggestions: getVisualSuggestions(gameData, platform),
    callToAction: getCallToAction(platform, contentType),
    platform,
    contentType
  };
}

function generatePlatformSpecificContent(gameData: GameData, platform: string, contentType: string): string {
  const templates = getContentTemplates(platform, contentType);
  const template = templates[Math.floor(Math.random() * templates.length)];
  
  return template
    .replace('{title}', gameData.title)
    .replace('{genre}', gameData.genre || 'game')
    .replace('{description}', gameData.description || 'amazing gaming experience')
    .replace('{feature}', gameData.features?.[0] || 'exciting gameplay');
}

function getContentTemplates(platform: string, contentType: string): string[] {
  const templates: { [key: string]: { [key: string]: string[] } } = {
    twitter: {
      post: [
        '🎮 Ready for an epic adventure? {title} brings you the ultimate {genre} experience! {feature} awaits. Download now! 🚀',
        '🔥 NEW GAME ALERT! 🔥 {title} is here and it\'s absolutely incredible! Experience {description} like never before. Get it now! 📱',
        '⚡ Level up your gaming with {title}! This {genre} masterpiece features {feature} that will blow your mind! Try it today! 🎯'
      ],
      announcement: [
        '📢 BIG ANNOUNCEMENT! {title} is officially launching! Get ready for the most exciting {genre} adventure of the year! 🎉',
        '🚨 LAUNCH DAY! 🚨 {title} is now available! Join thousands of players already enjoying this incredible {genre} experience! 🎮'
      ]
    },
    instagram: {
      post: [
        '🎮✨ Dive into the world of {title}! This stunning {genre} game offers {description} that will keep you hooked for hours. Swipe to see amazing gameplay screenshots! 📸\n\nReady to start your adventure? Link in bio! 🔗',
        '🌟 GAME SPOTLIGHT 🌟\n\n{title} is redefining the {genre} genre with {feature} and breathtaking visuals. Whether you\'re a casual gamer or hardcore enthusiast, this game has something special for you!\n\nDownload now and join our growing community! 🎯'
      ],
      story: [
        '🎮 Playing {title} right now and it\'s AMAZING! This {genre} game is exactly what I needed today! 🔥',
        '⚡ Quick gaming session with {title}! The {feature} is incredible! Anyone else playing? 🎯'
      ]
    },
    facebook: {
      post: [
        '🎮 Exciting News for Gamers! 🎮\n\nWe\'re thrilled to introduce {title}, a groundbreaking {genre} game that delivers {description}. With {feature} and stunning graphics, this game sets a new standard for mobile gaming.\n\n✅ Download now and discover why players are calling it "the game of the year"\n✅ Join our community of passionate gamers\n✅ Experience gaming like never before\n\nReady to play? Get {title} today!',
        '🚀 GAME LAUNCH ANNOUNCEMENT 🚀\n\n{title} is now available! This incredible {genre} experience features {feature} and offers hours of entertainment for players of all skill levels.\n\nWhat makes {title} special:\n• {description}\n• Stunning visual design\n• Engaging gameplay mechanics\n• Regular content updates\n\nDownload now and start your adventure!'
      ]
    },
    tiktok: {
      post: [
        '🎮 POV: You discover the most addictive {genre} game ever! {title} hits different 🔥 #gaming #viral',
        '⚡ This {title} gameplay is INSANE! Who else is obsessed? 🎯 #gaminglife #mobile'
      ]
    },
    linkedin: {
      post: [
        'Excited to share the launch of {title}, an innovative {genre} game that showcases cutting-edge mobile gaming technology. The development team has created {description} that demonstrates the future of interactive entertainment. Congratulations to the team on this achievement! #GameDevelopment #Innovation #MobileGaming',
        'The gaming industry continues to evolve with releases like {title}. This {genre} title features {feature} and represents the high-quality experiences that modern players expect. Impressive work from the development team! #Gaming #Technology #Innovation'
      ]
    }
  };

  return templates[platform]?.[contentType] || templates.twitter.post;
}

function generateHashtags(gameData: GameData, platform: string): string[] {
  const platformSpecs = getPlatformSpecs(platform);
  const maxHashtags = platformSpecs.hashtags;
  
  const baseHashtags = ['#gaming', '#mobilegame', '#gamedev'];
  const genreHashtags = gameData.genre ? [`#${gameData.genre.toLowerCase()}game`, `#${gameData.genre.toLowerCase()}`] : [];
  const platformHashtags = getPlatformSpecificHashtags(platform);
  const gameHashtags = [`#${gameData.title.replace(/\s+/g, '').toLowerCase()}`];
  
  const allHashtags = [...baseHashtags, ...genreHashtags, ...platformHashtags, ...gameHashtags];
  return [...new Set(allHashtags)].slice(0, maxHashtags);
}

function getPlatformSpecificHashtags(platform: string): string[] {
  const hashtags: { [key: string]: string[] } = {
    twitter: ['#gamedev', '#indiegame', '#newgame'],
    instagram: ['#gamer', '#gameplay', '#gameaddict', '#mobilegaming'],
    facebook: ['#gaming', '#entertainment'],
    tiktok: ['#viral', '#fyp', '#gaminglife'],
    linkedin: ['#gamedevelopment', '#innovation', '#technology']
  };
  
  return hashtags[platform] || hashtags.twitter;
}

function getBestPostingTime(platform: string): string {
  const times: { [key: string]: string } = {
    twitter: '9:00 AM - 10:00 AM, 7:00 PM - 9:00 PM EST',
    instagram: '11:00 AM - 1:00 PM, 7:00 PM - 9:00 PM EST',
    facebook: '1:00 PM - 3:00 PM, 7:00 PM - 9:00 PM EST',
    tiktok: '6:00 AM - 10:00 AM, 7:00 PM - 9:00 PM EST',
    linkedin: '8:00 AM - 10:00 AM, 12:00 PM - 2:00 PM EST'
  };
  
  return times[platform] || times.twitter;
}

function getEngagementTips(platform: string): string[] {
  const tips: { [key: string]: string[] } = {
    twitter: [
      'Respond to comments quickly',
      'Use relevant trending hashtags',
      'Retweet user-generated content',
      'Post consistently during peak hours'
    ],
    instagram: [
      'Use high-quality visuals',
      'Post Stories regularly',
      'Engage with comments and DMs',
      'Collaborate with gaming influencers'
    ],
    facebook: [
      'Create engaging video content',
      'Use Facebook Groups for community building',
      'Share behind-the-scenes content',
      'Run targeted ad campaigns'
    ],
    tiktok: [
      'Follow trending sounds and challenges',
      'Create short, engaging gameplay clips',
      'Use popular gaming hashtags',
      'Post multiple times per day'
    ],
    linkedin: [
      'Share industry insights',
      'Connect with other game developers',
      'Post about development process',
      'Engage with gaming industry content'
    ]
  };
  
  return tips[platform] || tips.twitter;
}

function getVisualSuggestions(gameData: GameData, platform: string): string[] {
  const base = ['Game screenshots', 'Gameplay videos', 'Character art'];
  
  const platformSpecific: { [key: string]: string[] } = {
    twitter: ['GIFs of key moments', 'Infographics with game stats'],
    instagram: ['Behind-the-scenes photos', 'Developer team shots', 'Concept art'],
    facebook: ['Longer gameplay videos', 'Developer interviews', 'Community highlights'],
    tiktok: ['Quick gameplay clips', 'Before/after comparisons', 'Trending challenge videos'],
    linkedin: ['Professional team photos', 'Development process images', 'Industry event photos']
  };
  
  return [...base, ...(platformSpecific[platform] || [])];
}

function getCallToAction(platform: string, contentType: string): string {
  const ctas: { [key: string]: { [key: string]: string } } = {
    twitter: {
      post: 'Download now! 🎮',
      announcement: 'Get it today! 🚀'
    },
    instagram: {
      post: 'Link in bio to download! 🔗',
      story: 'Swipe up to play! ⬆️'
    },
    facebook: {
      post: 'Click the link to download and start playing!'
    },
    tiktok: {
      post: 'Link in bio! 🎮'
    },
    linkedin: {
      post: 'Learn more about this innovative gaming experience.'
    }
  };
  
  return ctas[platform]?.[contentType] || ctas[platform]?.post || 'Download now!';
}

function getPlatformSpecs(platform: string) {
  const specs: { [key: string]: { characterLimit: number; tone: string; hashtags: number } } = {
    twitter: { characterLimit: 280, tone: 'conversational and energetic', hashtags: 3 },
    instagram: { characterLimit: 2200, tone: 'visual-focused and inspiring', hashtags: 10 },
    facebook: { characterLimit: 500, tone: 'community-focused and informative', hashtags: 5 },
    tiktok: { characterLimit: 150, tone: 'trendy and fun', hashtags: 5 },
    linkedin: { characterLimit: 700, tone: 'professional and insightful', hashtags: 3 }
  };
  
  return specs[platform] || specs.twitter;
}
