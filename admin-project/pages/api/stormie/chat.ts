import type { NextApiRequest, NextApiResponse } from 'next';
import { HfInference } from '@huggingface/inference';

const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

interface ChatRequest {
  message: string;
  context?: string;
  gameData?: any;
}

interface ChatResponse {
  success: boolean;
  response?: string;
  type?: string;
  data?: any;
  error?: string;
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ChatResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      timestamp: new Date().toISOString()
    });
  }

  try {
    const { message, context, gameData }: ChatRequest = req.body;

    if (!message || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Message is required',
        timestamp: new Date().toISOString()
      });
    }

    const response = await generateChatResponse(message, context, gameData);
    
    res.status(200).json({
      success: true,
      response: response.content,
      type: response.type,
      data: response.data,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate response',
      timestamp: new Date().toISOString()
    });
  }
}

async function generateChatResponse(message: string, context?: string, gameData?: any) {
  const lowerMessage = message.toLowerCase();
  
  // Enhanced context for gaming and marketing
  const systemPrompt = `You are Stormie, an AI marketing assistant for game developers. You help with:
- Game market analysis and positioning
- ASO (App Store Optimization) keywords
- Social media content creation
- Pricing strategies
- Market insights and trends
- General gaming industry advice

Be helpful, professional, and provide actionable advice. Keep responses concise but informative.

${context ? `Context: ${context}` : ''}
${gameData ? `Game Info: ${JSON.stringify(gameData)}` : ''}

User message: ${message}

Respond as Stormie AI:`;

  try {
    // Try to get AI response from Hugging Face
    const aiResponse = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: systemPrompt,
      parameters: {
        max_new_tokens: 200,
        temperature: 0.7,
        return_full_text: false,
        do_sample: true
      }
    });

    let response = aiResponse.generated_text?.trim() || '';
    
    // Clean up the response
    response = cleanAIResponse(response);
    
    // If response is too short or generic, enhance it
    if (response.length < 20) {
      response = generateEnhancedResponse(message, gameData);
    }

    // Determine response type and generate additional data if needed
    const responseType = determineResponseType(message);
    let additionalData = null;

    if (responseType !== 'general') {
      additionalData = await generateAdditionalData(responseType, message, gameData);
    }

    return {
      content: response,
      type: responseType,
      data: additionalData
    };

  } catch (error) {
    console.error('AI generation failed:', error);
    // Fallback to intelligent responses
    return generateFallbackResponse(message, gameData);
  }
}

function cleanAIResponse(response: string): string {
  // Remove common AI artifacts
  response = response.replace(/^(AI:|Assistant:|Stormie:|Response:)/i, '').trim();
  response = response.replace(/\n+/g, ' ').trim();
  
  // Ensure it starts with capital letter
  if (response.length > 0) {
    response = response.charAt(0).toUpperCase() + response.slice(1);
  }
  
  return response;
}

function generateEnhancedResponse(message: string, gameData?: any): string {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    return "Hello! I'm Stormie, your AI marketing assistant. I can help you with game analysis, ASO keywords, social media content, pricing strategies, and market insights. What would you like to work on today?";
  }
  
  if (lowerMessage.includes('help')) {
    return "I can assist you with: 🎮 Game market analysis, 🔍 ASO keyword optimization, 📱 Social media content creation, 💰 Pricing strategies, and 📊 Market insights. Just tell me about your game or ask any marketing question!";
  }
  
  if (lowerMessage.includes('game') && gameData) {
    return `I'd be happy to help with ${gameData.title || 'your game'}! I can analyze market positioning, suggest keywords, create social content, optimize pricing, or provide market insights. What specific area interests you most?`;
  }
  
  if (lowerMessage.includes('analyze') || lowerMessage.includes('analysis')) {
    return "I can provide comprehensive game analysis including market positioning, competitive landscape, target audience insights, and success probability scoring. Please share your game's title, genre, and description for a detailed analysis.";
  }
  
  if (lowerMessage.includes('keyword') || lowerMessage.includes('aso')) {
    return "I'll help you generate optimized ASO keywords! I can create primary keywords for maximum impact, secondary keywords for broader reach, and long-tail keywords for specific targeting. What's your game's genre and platform?";
  }
  
  if (lowerMessage.includes('social') || lowerMessage.includes('content')) {
    return "I can create engaging social media content for Twitter, Instagram, Facebook, TikTok, and LinkedIn. Each platform gets optimized content with hashtags, posting times, and engagement strategies. Which platform are you focusing on?";
  }
  
  if (lowerMessage.includes('price') || lowerMessage.includes('pricing')) {
    return "I'll analyze the best pricing strategy for your game! I can recommend freemium vs premium models, suggest optimal price points, forecast revenue, and create A/B testing frameworks. Tell me about your game's genre and target audience.";
  }
  
  if (lowerMessage.includes('market') || lowerMessage.includes('trend')) {
    return "I can provide current market insights including genre trends, user behavior patterns, seasonal opportunities, competition analysis, and future predictions. What market segment are you interested in?";
  }
  
  // General gaming/marketing response
  return "I'm here to help with all aspects of game marketing! Whether you need market analysis, keyword optimization, social content, pricing strategies, or industry insights, just let me know what you're working on and I'll provide actionable advice.";
}

function determineResponseType(message: string): string {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('analyze') || lowerMessage.includes('analysis')) return 'analysis';
  if (lowerMessage.includes('keyword') || lowerMessage.includes('aso')) return 'keywords';
  if (lowerMessage.includes('social') || lowerMessage.includes('content') || lowerMessage.includes('post')) return 'social';
  if (lowerMessage.includes('price') || lowerMessage.includes('pricing')) return 'pricing';
  if (lowerMessage.includes('market') || lowerMessage.includes('insight') || lowerMessage.includes('trend')) return 'insights';
  
  return 'general';
}

async function generateAdditionalData(type: string, message: string, gameData?: any) {
  // Generate sample data based on type
  switch (type) {
    case 'analysis':
      return {
        marketPosition: "Strong positioning in the gaming market with unique features",
        successScore: 8.5,
        targetAudience: "Core gamers and casual players",
        competitiveAdvantages: ["Innovative gameplay", "Strong visual design", "Engaging mechanics"]
      };
    
    case 'keywords':
      return {
        primary: ["mobile game", "puzzle", "strategy", "adventure", "action"],
        secondary: ["gaming", "entertainment", "fun", "challenge", "multiplayer"],
        longTail: ["best mobile puzzle game", "free strategy adventure", "multiplayer action game"]
      };
    
    case 'social':
      return {
        content: "🎮 Discover an amazing new gaming experience! Join thousands of players in this epic adventure. Download now! #gaming #mobilegame #newrelease",
        hashtags: ["#gaming", "#mobilegame", "#newrelease", "#gamedev", "#indiegame"],
        platform: "twitter"
      };
    
    case 'pricing':
      return {
        strategy: "Freemium with Premium Features",
        recommendedPrice: "Free with $2.99-$9.99 IAP",
        reasoning: "Freemium model maximizes user acquisition while premium features drive revenue"
      };
    
    default:
      return null;
  }
}

function generateFallbackResponse(message: string, gameData?: any) {
  const responses = [
    "I'm here to help with your game marketing needs! Tell me about your game and I'll provide insights on market positioning, keywords, social content, pricing, or industry trends.",
    "As your AI marketing assistant, I can analyze your game's market potential, suggest ASO keywords, create social media content, optimize pricing strategies, and provide market insights. What would you like to explore?",
    "Let's boost your game's success! I can help with market analysis, keyword optimization, social media strategy, pricing decisions, and competitive insights. What's your biggest marketing challenge?",
    "I'm Stormie, your AI marketing companion! Whether you need game analysis, ASO optimization, content creation, pricing strategy, or market research, I'm here to help. What can we work on together?"
  ];
  
  const randomResponse = responses[Math.floor(Math.random() * responses.length)];
  
  return {
    content: randomResponse,
    type: 'general',
    data: null
  };
}
