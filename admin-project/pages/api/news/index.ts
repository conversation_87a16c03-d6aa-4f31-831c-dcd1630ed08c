import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';
import { NewsArticle, PaginatedResponse } from '../../../types/database';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PaginatedResponse<NewsArticle>>
) {
  try {
    if (req.method === 'GET') {
      return await getNews(req, res);
    } else if (req.method === 'POST') {
      return await createNews(req, res);
    } else {
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({
        success: false,
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
        error: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('News API error:', error);
    return res.status(500).json({
      success: false,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
      error: 'Internal server error',
    });
  }
}

async function getNews(
  req: NextApiRequest,
  res: NextApiResponse<PaginatedResponse<NewsArticle>>
) {
  try {
    const {
      page = '1',
      limit = '10',
      status = 'published',
      featured,
      category,
      search,
      author,
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const offset = (pageNum - 1) * limitNum;

    let query = adminDb.collection('news');

    // Apply filters
    if (status) {
      query = query.where('status', '==', status);
    }

    if (featured === 'true') {
      query = query.where('featured', '==', true);
    }

    if (category) {
      query = query.where('category', '==', category);
    }

    if (author) {
      query = query.where('author.email', '==', author);
    }

    // Order by publication date (newest first)
    query = query.orderBy('publishedAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const snapshot = await query.limit(limitNum).offset(offset).get();

    let articles: NewsArticle[] = [];
    snapshot.forEach((doc) => {
      const data = doc.data();
      articles.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        publishedAt: data.publishedAt?.toDate(),
      } as NewsArticle);
    });

    // Apply search filter (client-side for now)
    if (search) {
      const searchTerm = (search as string).toLowerCase();
      articles = articles.filter(
        (article) =>
          article.title.toLowerCase().includes(searchTerm) ||
          article.excerpt.toLowerCase().includes(searchTerm) ||
          article.content.toLowerCase().includes(searchTerm) ||
          article.author.name.toLowerCase().includes(searchTerm) ||
          article.tags.some((tag) => tag.toLowerCase().includes(searchTerm))
      );
    }

    const totalPages = Math.ceil(total / limitNum);

    return res.status(200).json({
      success: true,
      data: articles,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1,
      },
    });
  } catch (error) {
    console.error('Get news error:', error);
    return res.status(500).json({
      success: false,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
      error: 'Failed to fetch news',
    });
  }
}

async function createNews(
  req: NextApiRequest,
  res: NextApiResponse<PaginatedResponse<NewsArticle>>
) {
  try {
    const articleData = req.body;

    // Validate required fields
    if (!articleData.title || !articleData.content || !articleData.author) {
      return res.status(400).json({
        success: false,
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
        error: 'Missing required fields',
      });
    }

    // Add timestamps and default values
    const newArticle: Partial<NewsArticle> = {
      ...articleData,
      status: articleData.status || 'draft',
      featured: articleData.featured || false,
      createdAt: new Date(),
      updatedAt: new Date(),
      publishedAt: articleData.status === 'published' ? new Date() : undefined,
      stats: {
        views: 0,
        likes: 0,
        shares: 0,
      },
    };

    // Create the article document
    const docRef = await adminDb.collection('news').add(newArticle);

    return res.status(201).json({
      success: true,
      data: [{ id: docRef.id, ...newArticle } as NewsArticle],
      pagination: {
        page: 1,
        limit: 1,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    });
  } catch (error) {
    console.error('Create news error:', error);
    return res.status(500).json({
      success: false,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
      error: 'Failed to create news article',
    });
  }
}
