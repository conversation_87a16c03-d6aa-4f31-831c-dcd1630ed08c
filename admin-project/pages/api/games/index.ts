import type { NextApiRequest, NextApiResponse } from 'next';
import { adminDb } from '../../../lib/firebaseAdmin';
import { Game, PaginatedResponse, GameFilters } from '../../../types/database';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PaginatedResponse<Game>>
) {
  try {
    if (req.method === 'GET') {
      return await getGames(req, res);
    } else if (req.method === 'POST') {
      return await createGame(req, res);
    } else {
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({
        success: false,
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
        error: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Games API error:', error);
    return res.status(500).json({
      success: false,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
      error: 'Internal server error',
    });
  }
}

async function getGames(
  req: NextApiRequest,
  res: NextApiResponse<PaginatedResponse<Game>>
) {
  try {
    const {
      page = '1',
      limit = '12',
      status = 'approved',
      featured,
      genre,
      platform,
      search,
      developer,
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const offset = (pageNum - 1) * limitNum;

    let query = adminDb.collection('games');

    // Apply filters
    if (status) {
      query = query.where('status', '==', status);
    }

    if (featured === 'true') {
      query = query.where('featured', '==', true);
    }

    if (genre) {
      query = query.where('details.genre', 'array-contains', genre);
    }

    if (platform) {
      query = query.where('details.platforms', 'array-contains', platform);
    }

    if (developer) {
      query = query.where('developer.uid', '==', developer);
    }

    // Order by creation date (newest first)
    query = query.orderBy('createdAt', 'desc');

    // Get total count for pagination
    const totalSnapshot = await query.get();
    const total = totalSnapshot.size;

    // Apply pagination
    const snapshot = await query.limit(limitNum).offset(offset).get();

    let games: Game[] = [];
    snapshot.forEach((doc) => {
      const data = doc.data();
      games.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        approvedAt: data.approvedAt?.toDate(),
      } as Game);
    });

    // Apply search filter (client-side for now)
    if (search) {
      const searchTerm = (search as string).toLowerCase();
      games = games.filter(
        (game) =>
          game.title.toLowerCase().includes(searchTerm) ||
          game.description.toLowerCase().includes(searchTerm) ||
          game.developer.name.toLowerCase().includes(searchTerm) ||
          game.details.genre.some((g) => g.toLowerCase().includes(searchTerm))
      );
    }

    const totalPages = Math.ceil(total / limitNum);

    return res.status(200).json({
      success: true,
      data: games,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1,
      },
    });
  } catch (error) {
    console.error('Get games error:', error);
    return res.status(500).json({
      success: false,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
      error: 'Failed to fetch games',
    });
  }
}

async function createGame(
  req: NextApiRequest,
  res: NextApiResponse<PaginatedResponse<Game>>
) {
  try {
    const gameData = req.body;

    // Validate required fields
    if (!gameData.title || !gameData.description || !gameData.developer) {
      return res.status(400).json({
        success: false,
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
        error: 'Missing required fields',
      });
    }

    // Add timestamps and default values
    const newGame: Partial<Game> = {
      ...gameData,
      status: 'pending',
      featured: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      stats: {
        downloads: 0,
        views: 0,
        likes: 0,
        reviews: 0,
      },
    };

    // Create the game document
    const docRef = await adminDb.collection('games').add(newGame);

    return res.status(201).json({
      success: true,
      data: [{ id: docRef.id, ...newGame } as Game],
      pagination: {
        page: 1,
        limit: 1,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    });
  } catch (error) {
    console.error('Create game error:', error);
    return res.status(500).json({
      success: false,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
      error: 'Failed to create game',
    });
  }
}
