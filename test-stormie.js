// Test script for Stormie AI Marketing Platform
const axios = require('axios');

const BASE_URL = 'http://localhost:8000/api/stormie';

// Test game data
const testGameData = {
  title: 'Cosmic Adventure',
  genre: 'Space Exploration',
  description: 'An epic space adventure game with stunning visuals and immersive gameplay',
  platform: 'PC, Mobile',
  targetAudience: 'Core gamers aged 18-35',
  features: ['Multiplayer', 'Open World', 'Customization', 'Story Mode']
};

// Test functions
async function testGameAnalysis() {
  console.log('🧠 Testing Game Analysis...');
  try {
    const response = await axios.post(`${BASE_URL}/analyze-game`, {
      gameData: testGameData
    });
    
    console.log('✅ Game Analysis Success!');
    console.log('📊 Analysis Results:');
    console.log(`- Market Position: ${response.data.analysis?.marketPosition || 'Generated'}`);
    console.log(`- Success Score: ${response.data.analysis?.successScore || 'N/A'}/10`);
    console.log(`- Target Audience: ${response.data.analysis?.targetAudience || 'Generated'}`);
    return true;
  } catch (error) {
    console.log('❌ Game Analysis Failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testASOKeywords() {
  console.log('\n🔍 Testing ASO Keywords...');
  try {
    const response = await axios.post(`${BASE_URL}/aso-keywords`, {
      gameData: testGameData
    });
    
    console.log('✅ ASO Keywords Success!');
    console.log('🎯 Keywords Generated:');
    console.log(`- Primary: ${response.data.keywords?.primary?.slice(0, 3).join(', ') || 'Generated'}`);
    console.log(`- Secondary: ${response.data.keywords?.secondary?.slice(0, 3).join(', ') || 'Generated'}`);
    console.log(`- Difficulty: ${response.data.keywords?.difficulty || 'Medium'}`);
    return true;
  } catch (error) {
    console.log('❌ ASO Keywords Failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testSocialContent() {
  console.log('\n📱 Testing Social Content...');
  try {
    const response = await axios.post(`${BASE_URL}/social-content`, {
      gameData: testGameData,
      platform: 'twitter',
      contentType: 'post'
    });
    
    console.log('✅ Social Content Success!');
    console.log('🐦 Twitter Content:');
    console.log(`- Content: "${response.data.content?.text?.substring(0, 100) || 'Generated content'}..."`);
    console.log(`- Hashtags: ${response.data.content?.hashtags?.slice(0, 3).join(' ') || '#gaming #mobile'}`);
    console.log(`- Best Time: ${response.data.content?.bestTime || 'Peak hours'}`);
    return true;
  } catch (error) {
    console.log('❌ Social Content Failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testPricingStrategy() {
  console.log('\n💰 Testing Pricing Strategy...');
  try {
    const response = await axios.post(`${BASE_URL}/pricing-strategy`, {
      gameData: testGameData
    });
    
    console.log('✅ Pricing Strategy Success!');
    console.log('💵 Pricing Recommendations:');
    console.log(`- Strategy: ${response.data.pricingStrategy?.recommended?.strategy || 'Freemium'}`);
    console.log(`- Price: ${response.data.pricingStrategy?.recommended?.price || '$2.99'}`);
    console.log(`- Expected Revenue: ${response.data.pricingStrategy?.recommended?.expectedRevenue || '$5,000/month'}`);
    return true;
  } catch (error) {
    console.log('❌ Pricing Strategy Failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testMarketInsights() {
  console.log('\n📈 Testing Market Insights...');
  try {
    const response = await axios.post(`${BASE_URL}/market-insights`, {
      gameData: testGameData
    });
    
    console.log('✅ Market Insights Success!');
    console.log('📊 Market Analysis:');
    console.log(`- Genre Trends: ${response.data.insights?.trends?.genre?.slice(0, 2).join(', ') || 'Growing market'}`);
    console.log(`- Competition: ${response.data.insights?.competition?.saturation || 'Medium competition'}`);
    console.log(`- Opportunities: ${response.data.insights?.competition?.opportunities?.slice(0, 2).join(', ') || 'Multiple opportunities'}`);
    return true;
  } catch (error) {
    console.log('❌ Market Insights Failed:', error.response?.data?.error || error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 STORMIE AI MARKETING PLATFORM - TESTING SUITE');
  console.log('================================================\n');
  
  const results = [];
  
  results.push(await testGameAnalysis());
  results.push(await testASOKeywords());
  results.push(await testSocialContent());
  results.push(await testPricingStrategy());
  results.push(await testMarketInsights());
  
  const successCount = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('\n================================================');
  console.log('🎯 TEST RESULTS SUMMARY');
  console.log('================================================');
  console.log(`✅ Successful Tests: ${successCount}/${totalTests}`);
  console.log(`❌ Failed Tests: ${totalTests - successCount}/${totalTests}`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! Stormie AI is working perfectly! 🧠✨');
    console.log('\n🎮 Ready to test in the browser:');
    console.log('1. Go to http://localhost:8000');
    console.log('2. Navigate to Developer Dashboard');
    console.log('3. Click "Marketing AI" in the sidebar');
    console.log('4. Test Stormie Chat and AI features!');
  } else {
    console.log('\n⚠️  Some tests failed. Check the API endpoints and dependencies.');
    console.log('💡 Note: Tests may fail without a valid Hugging Face API key.');
    console.log('   The fallback responses should still work for basic functionality.');
  }
  
  console.log('\n🔗 Access Stormie AI at: http://localhost:8000/developer/dashboard');
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.log('\n❌ Test Error:', error.message);
  console.log('💡 Make sure the development server is running on port 8000');
});

// Run tests
runAllTests();
