<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="icoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="32" height="32" rx="6" fill="url(#icoGradient)"/>
  
  <!-- Gaming controller -->
  <g transform="translate(6, 10)">
    <!-- Controller body -->
    <rect x="2" y="4" width="16" height="8" rx="4" fill="#ffffff" opacity="0.9"/>
    
    <!-- D-pad -->
    <rect x="5" y="6" width="2" height="1" fill="#533483"/>
    <rect x="5.5" y="5.5" width="1" height="2" fill="#533483"/>
    
    <!-- Action buttons -->
    <circle cx="14" cy="7" r="1" fill="#4ecdc4"/>
    <circle cx="16" cy="8" r="1" fill="#ff6b6b"/>
  </g>
  
  <!-- Small accent -->
  <circle cx="26" cy="6" r="2" fill="#4ecdc4" opacity="0.6"/>
</svg>
