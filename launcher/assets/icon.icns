<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="macGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#45b7d1;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#533483;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    <filter id="macGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#macGradient)"/>
  
  <!-- Inner circle -->
  <circle cx="64" cy="64" r="45" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.6" filter="url(#macGlow)"/>
  
  <!-- Gaming controller -->
  <g transform="translate(32, 45)">
    <!-- Controller body -->
    <rect x="10" y="10" width="44" height="22" rx="11" fill="#1a1a2e" opacity="0.9"/>
    
    <!-- D-pad -->
    <rect x="20" y="18" width="8" height="2" fill="#4ecdc4"/>
    <rect x="23" y="15" width="2" height="8" fill="#4ecdc4"/>
    
    <!-- Action buttons -->
    <circle cx="44" cy="19" r="3" fill="#ff6b6b"/>
    <circle cx="48" cy="23" r="3" fill="#45b7d1"/>
    
    <!-- Analog sticks -->
    <circle cx="25" cy="26" r="4" fill="#533483" opacity="0.7"/>
    <circle cx="43" cy="26" r="4" fill="#533483" opacity="0.7"/>
  </g>
  
  <!-- Central power symbol -->
  <g transform="translate(64, 64)">
    <circle cx="0" cy="0" r="10" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.8"/>
    <line x1="0" y1="-10" x2="0" y2="-4" stroke="#4ecdc4" stroke-width="2" opacity="0.8"/>
  </g>
  
  <!-- Orbiting element -->
  <circle cx="94" cy="64" r="2" fill="#45b7d1" opacity="0.8">
    <animateTransform attributeName="transform" type="rotate" values="0 64 64;360 64 64" dur="4s" repeatCount="indefinite"/>
  </circle>
</svg>
