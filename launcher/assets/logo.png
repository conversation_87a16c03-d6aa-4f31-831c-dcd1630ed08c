<svg width="200" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45b7d1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#533483;stop-opacity:1" />
    </linearGradient>
    <filter id="textGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="200" height="80" fill="#1a1a2e" rx="8"/>
  
  <!-- Gaming controller icon -->
  <g transform="translate(10, 25)">
    <rect x="0" y="10" width="30" height="15" rx="7" fill="url(#logoGradient)" opacity="0.8"/>
    <circle cx="8" cy="17" r="2" fill="#4ecdc4"/>
    <circle cx="22" cy="17" r="2" fill="#ff6b6b"/>
  </g>
  
  <!-- Text -->
  <text x="50" y="35" fill="url(#logoGradient)" font-family="Arial, sans-serif" font-size="20" font-weight="bold" filter="url(#textGlow)">
    GAMESTORME
  </text>
  <text x="50" y="55" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="12" opacity="0.8">
    Game Launcher
  </text>
  
  <!-- Decorative elements -->
  <circle cx="180" cy="20" r="3" fill="#4ecdc4" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="185" cy="60" r="2" fill="#ff6b6b" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
  </circle>
</svg>
