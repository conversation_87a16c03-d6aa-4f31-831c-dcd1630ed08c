<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="launcherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#45b7d1;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#533483;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="innerGlow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:0.8" />
    </linearGradient>
    <filter id="dropShadow">
      <feDropShadow dx="0" dy="8" stdDeviation="8" flood-color="#000000" flood-opacity="0.4"/>
    </filter>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#launcherGradient)" filter="url(#dropShadow)"/>
  
  <!-- Inner circle -->
  <circle cx="128" cy="128" r="90" fill="none" stroke="url(#innerGlow)" stroke-width="4" opacity="0.8" filter="url(#glow)"/>
  
  <!-- Gaming controller -->
  <g transform="translate(64, 90)">
    <!-- Controller body -->
    <rect x="20" y="20" width="88" height="44" rx="22" fill="#1a1a2e" opacity="0.9"/>
    <rect x="24" y="24" width="80" height="36" rx="18" fill="#2a2a3e" opacity="0.8"/>
    
    <!-- D-pad -->
    <rect x="40" y="36" width="16" height="4" fill="#4ecdc4"/>
    <rect x="46" y="30" width="4" height="16" fill="#4ecdc4"/>
    
    <!-- Action buttons -->
    <circle cx="88" cy="38" r="6" fill="#ff6b6b"/>
    <circle cx="96" cy="46" r="6" fill="#45b7d1"/>
    <circle cx="80" cy="46" r="6" fill="#4ecdc4"/>
    <circle cx="88" cy="54" r="6" fill="#533483"/>
    
    <!-- Analog sticks -->
    <circle cx="50" cy="52" r="8" fill="#533483" opacity="0.8"/>
    <circle cx="86" cy="52" r="8" fill="#533483" opacity="0.8"/>
    <circle cx="50" cy="52" r="4" fill="#4ecdc4"/>
    <circle cx="86" cy="52" r="4" fill="#4ecdc4"/>
    
    <!-- Shoulder buttons -->
    <rect x="20" y="16" width="12" height="8" rx="4" fill="#2a2a3e"/>
    <rect x="96" y="16" width="12" height="8" rx="4" fill="#2a2a3e"/>
  </g>
  
  <!-- Gaming elements around the controller -->
  <g opacity="0.7">
    <!-- Pixel blocks -->
    <rect x="40" y="40" width="8" height="8" fill="#4ecdc4">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="208" y="50" width="8" height="8" fill="#ff6b6b">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="50" y="200" width="8" height="8" fill="#45b7d1">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
    </rect>
    <rect x="200" y="190" width="8" height="8" fill="#4ecdc4">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2.2s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Central power symbol -->
  <g transform="translate(128, 128)">
    <circle cx="0" cy="0" r="20" fill="none" stroke="#4ecdc4" stroke-width="4" opacity="0.9"/>
    <line x1="0" y1="-20" x2="0" y2="-8" stroke="#4ecdc4" stroke-width="4" opacity="0.9"/>
  </g>
  
  <!-- Orbiting elements -->
  <g>
    <circle cx="128" cy="128" r="60" fill="none" stroke="#45b7d1" stroke-width="2" opacity="0.4"/>
    <circle cx="188" cy="128" r="4" fill="#45b7d1" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 128 128;360 128 128" dur="6s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <g>
    <circle cx="128" cy="128" r="80" fill="none" stroke="#ff6b6b" stroke-width="2" opacity="0.3"/>
    <circle cx="208" cy="128" r="3" fill="#ff6b6b" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0 128 128;-360 128 128" dur="8s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Text -->
  <text x="128" y="220" text-anchor="middle" fill="#4ecdc4" font-family="Arial, sans-serif" font-size="16" font-weight="bold" opacity="0.8">
    GAMESTORME
  </text>
  <text x="128" y="240" text-anchor="middle" fill="#45b7d1" font-family="Arial, sans-serif" font-size="12" opacity="0.7">
    LAUNCHER
  </text>
</svg>
