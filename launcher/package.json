{"name": "gamestorme-launcher", "version": "1.0.0", "description": "Gamestorme Game Launcher", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux"}, "author": "Gamestorme", "license": "MIT", "devDependencies": {"electron": "^25.0.0", "electron-builder": "^24.4.0"}, "dependencies": {"electron-store": "^8.1.0"}, "build": {"appId": "com.gamestorme.launcher", "productName": "Gamestorme Launcher", "directories": {"output": "dist"}, "files": ["**/*", "!dist/**/*", "!build/**/*", "!node_modules/**/*"], "win": {"target": ["nsis"], "icon": "assets/icon.ico"}, "mac": {"target": ["dmg"], "icon": "assets/icon.icns"}, "linux": {"target": ["AppImage"], "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}