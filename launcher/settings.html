<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gamestorme Launcher Settings</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #121212;
      color: #ffffff;
    }
    h1 {
      color: #F0BC2B;
      margin-bottom: 20px;
    }
    .settings-container {
      max-width: 500px;
      margin: 0 auto;
    }
    .setting-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .setting-label {
      font-weight: 500;
    }
    .setting-description {
      font-size: 0.9em;
      color: #aaaaaa;
      margin-top: 5px;
    }
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #333333;
      transition: .4s;
      border-radius: 24px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #F0BC2B;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    .button-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 30px;
    }
    button {
      background-color: #F0BC2B;
      color: #121212;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    button:hover {
      background-color: #d9a926;
    }
    button.cancel {
      background-color: transparent;
      color: #ffffff;
      margin-right: 10px;
    }
    button.cancel:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  </style>
</head>
<body>
  <div class="settings-container">
    <h1>Settings</h1>
    
    <div class="setting-item">
      <div>
        <div class="setting-label">Use Local Server</div>
        <div class="setting-description">Connect to localhost:8000 instead of gamestorme.com</div>
      </div>
      <label class="toggle-switch">
        <input type="checkbox" id="useLocalServer">
        <span class="slider"></span>
      </label>
    </div>
    
    <div class="setting-item">
      <div>
        <div class="setting-label">Launch on Startup</div>
        <div class="setting-description">Automatically start the launcher when you log in</div>
      </div>
      <label class="toggle-switch">
        <input type="checkbox" id="autoLaunch">
        <span class="slider"></span>
      </label>
    </div>
    
    <div class="setting-item">
      <div>
        <div class="setting-label">Check for Updates</div>
        <div class="setting-description">Automatically check for launcher updates</div>
      </div>
      <label class="toggle-switch">
        <input type="checkbox" id="autoCheckUpdates">
        <span class="slider"></span>
      </label>
    </div>
    
    <div class="button-container">
      <button class="cancel" id="cancelBtn">Cancel</button>
      <button id="saveBtn">Save Changes</button>
    </div>
  </div>

  <script>
    // Get elements
    const useLocalServerToggle = document.getElementById('useLocalServer');
    const autoLaunchToggle = document.getElementById('autoLaunch');
    const autoCheckUpdatesToggle = document.getElementById('autoCheckUpdates');
    const saveBtn = document.getElementById('saveBtn');
    const cancelBtn = document.getElementById('cancelBtn');

    // Load settings
    async function loadSettings() {
      const settings = await window.api.getSettings();
      useLocalServerToggle.checked = settings.useLocalServer;
      autoLaunchToggle.checked = settings.autoLaunch;
      autoCheckUpdatesToggle.checked = settings.autoCheckUpdates;
    }

    // Save settings
    async function saveSettings() {
      const settings = {
        useLocalServer: useLocalServerToggle.checked,
        autoLaunch: autoLaunchToggle.checked,
        autoCheckUpdates: autoCheckUpdatesToggle.checked
      };
      
      await window.api.saveSettings(settings);
      window.close();
    }

    // Cancel
    function cancel() {
      window.close();
    }

    // Event listeners
    saveBtn.addEventListener('click', saveSettings);
    cancelBtn.addEventListener('click', cancel);

    // Load settings when page loads
    document.addEventListener('DOMContentLoaded', loadSettings);
  </script>
</body>
</html>
