# Gamestorme Launcher

The official desktop application for the Gamestorme platform.

## Features

- Access Gamestorme directly from your desktop
- Automatic updates
- Customizable settings
- Seamless gaming experience

## Development

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Run the application:
   ```
   npm start
   ```

### Building

To build the application for distribution:

```
npm run build
```

This will create distributable packages in the `dist` folder.

For specific platforms:

```
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

## License

Copyright © 2023 Gamestorme. All rights reserved.
