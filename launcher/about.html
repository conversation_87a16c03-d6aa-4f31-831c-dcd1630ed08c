<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>About Gamestorme Launcher</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #121212;
      color: #ffffff;
      text-align: center;
    }
    .about-container {
      max-width: 400px;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    .logo {
      width: 100px;
      height: 100px;
      margin-bottom: 20px;
    }
    h1 {
      color: #F0BC2B;
      margin: 10px 0;
    }
    .version {
      color: #aaaaaa;
      margin-bottom: 20px;
    }
    .description {
      margin-bottom: 20px;
      line-height: 1.5;
    }
    .copyright {
      font-size: 0.9em;
      color: #aaaaaa;
      margin-top: 20px;
    }
    .links {
      margin-top: 20px;
    }
    .links a {
      color: #F0BC2B;
      text-decoration: none;
      margin: 0 10px;
    }
    .links a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="about-container">
    <img src="assets/logo.png" alt="Gamestorme Logo" class="logo">
    <h1>Gamestorme Launcher</h1>
    <div class="version">Version <span id="version">1.0.0</span></div>
    <div class="description">
      The official desktop application for accessing the Gamestorme platform.
      Play games, manage your library, and connect with the gaming community.
    </div>
    <div class="links">
      <a href="https://gamestorme.com" target="_blank">Website</a>
      <a href="https://gamestorme.com/support" target="_blank">Support</a>
      <a href="https://gamestorme.com/terms" target="_blank">Terms</a>
    </div>
    <div class="copyright">
      © 2023 Gamestorme. All rights reserved.
    </div>
  </div>

  <script>
    // Get app version
    async function loadVersion() {
      try {
        const version = await window.api.getAppVersion();
        document.getElementById('version').textContent = version;
      } catch (error) {
        console.error('Error getting app version:', error);
      }
    }

    // Load version when page loads
    document.addEventListener('DOMContentLoaded', loadVersion);
  </script>
</body>
</html>
