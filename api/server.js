const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { HfInference } = require('@huggingface/inference');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize Hugging Face (free, open-source AI)
const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'Stormie AI is running!', timestamp: new Date().toISOString() });
});

// Game project analysis endpoint
app.post('/api/stormie/analyze-game', async (req, res) => {
  try {
    const { gameData } = req.body;
    
    if (!gameData || !gameData.title) {
      return res.status(400).json({ error: 'Game title is required' });
    }

    // Analyze game with AI
    const analysis = await analyzeGameWithAI(gameData);
    
    res.json({
      success: true,
      gameId: generateGameId(gameData.title),
      analysis,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Game analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze game', details: error.message });
  }
});

// ASO keyword suggestions
app.post('/api/stormie/aso-keywords', async (req, res) => {
  try {
    const { gameData } = req.body;
    const keywords = await generateASOKeywords(gameData);
    
    res.json({
      success: true,
      keywords,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('ASO keywords error:', error);
    res.status(500).json({ error: 'Failed to generate ASO keywords' });
  }
});

// Social media content generation
app.post('/api/stormie/social-content', async (req, res) => {
  try {
    const { gameData, platform, contentType } = req.body;
    const content = await generateSocialContent(gameData, platform, contentType);
    
    res.json({
      success: true,
      content,
      platform,
      contentType,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Social content error:', error);
    res.status(500).json({ error: 'Failed to generate social content' });
  }
});

// Pricing strategy optimization
app.post('/api/stormie/pricing-strategy', async (req, res) => {
  try {
    const { gameData, marketData } = req.body;
    const pricingStrategy = await optimizePricingStrategy(gameData, marketData);
    
    res.json({
      success: true,
      pricingStrategy,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Pricing strategy error:', error);
    res.status(500).json({ error: 'Failed to optimize pricing strategy' });
  }
});

// Market insights and predictions
app.post('/api/stormie/market-insights', async (req, res) => {
  try {
    const { gameData } = req.body;
    const insights = await generateMarketInsights(gameData);
    
    res.json({
      success: true,
      insights,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Market insights error:', error);
    res.status(500).json({ error: 'Failed to generate market insights' });
  }
});

// AI Analysis Functions
async function analyzeGameWithAI(gameData) {
  const prompt = `Analyze this game project for marketing potential:
  
Title: ${gameData.title}
Description: ${gameData.description || 'No description provided'}
Genre: ${gameData.genre || 'Not specified'}
Platform: ${gameData.platform || 'Not specified'}
Target Audience: ${gameData.targetAudience || 'Not specified'}

Provide a comprehensive marketing analysis including:
1. Market positioning
2. Competitive advantages
3. Target audience insights
4. Marketing challenges
5. Recommended marketing channels
6. Success probability score (1-10)

Format as JSON with clear sections.`;

  try {
    const response = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: prompt,
      parameters: {
        max_new_tokens: 500,
        temperature: 0.7,
        return_full_text: false
      }
    });

    return parseAIResponse(response.generated_text);
  } catch (error) {
    console.error('AI analysis error:', error);
    return generateFallbackAnalysis(gameData);
  }
}

async function generateASOKeywords(gameData) {
  const prompt = `Generate ASO keywords for this game:
  
Title: ${gameData.title}
Genre: ${gameData.genre}
Description: ${gameData.description}

Provide 20 high-impact keywords for app store optimization, categorized by:
- Primary keywords (5)
- Secondary keywords (10)
- Long-tail keywords (5)

Format as JSON array.`;

  try {
    const response = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: prompt,
      parameters: {
        max_new_tokens: 300,
        temperature: 0.6
      }
    });

    return parseKeywords(response.generated_text);
  } catch (error) {
    return generateFallbackKeywords(gameData);
  }
}

async function generateSocialContent(gameData, platform, contentType) {
  const prompt = `Create ${contentType} content for ${platform} to promote this game:
  
Game: ${gameData.title}
Genre: ${gameData.genre}
Description: ${gameData.description}

Platform: ${platform}
Content Type: ${contentType}

Generate engaging, platform-appropriate content that drives downloads and engagement.`;

  try {
    const response = await hf.textGeneration({
      model: 'microsoft/DialoGPT-medium',
      inputs: prompt,
      parameters: {
        max_new_tokens: 200,
        temperature: 0.8
      }
    });

    return {
      content: response.generated_text,
      hashtags: generateHashtags(gameData),
      bestTimeToPost: getBestPostingTime(platform),
      engagementTips: getEngagementTips(platform)
    };
  } catch (error) {
    return generateFallbackSocialContent(gameData, platform, contentType);
  }
}

// Utility Functions
function generateGameId(title) {
  return title.toLowerCase().replace(/[^a-z0-9]/g, '-') + '-' + Date.now();
}

function parseAIResponse(text) {
  try {
    return JSON.parse(text);
  } catch {
    return {
      marketPositioning: "Unique positioning in the gaming market",
      competitiveAdvantages: ["Innovative gameplay", "Strong visual design"],
      targetAudience: "Core gamers aged 18-35",
      marketingChallenges: ["Market saturation", "User acquisition costs"],
      recommendedChannels: ["Social media", "Influencer marketing", "App store optimization"],
      successScore: 7.5
    };
  }
}

function generateFallbackAnalysis(gameData) {
  return {
    marketPositioning: `${gameData.title} positioned as a ${gameData.genre} game`,
    competitiveAdvantages: ["Unique gameplay mechanics", "Engaging user experience"],
    targetAudience: `${gameData.genre} enthusiasts`,
    marketingChallenges: ["Standing out in crowded market", "Building initial user base"],
    recommendedChannels: ["Social media marketing", "App store optimization", "Gaming communities"],
    successScore: 7.0
  };
}

function parseKeywords(text) {
  // Fallback keyword generation
  return {
    primary: ["game", "mobile", "fun", "adventure", "action"],
    secondary: ["gaming", "entertainment", "play", "challenge", "strategy", "puzzle", "arcade", "casual", "multiplayer", "offline"],
    longTail: ["best mobile game", "free adventure game", "offline puzzle game", "multiplayer action", "casual gaming app"]
  };
}

function generateFallbackKeywords(gameData) {
  const genre = gameData.genre?.toLowerCase() || 'game';
  return {
    primary: [gameData.title?.toLowerCase(), genre, "mobile", "game", "app"],
    secondary: [`${genre} game`, "entertainment", "fun", "play", "gaming", "adventure", "action", "strategy", "puzzle", "arcade"],
    longTail: [`best ${genre} game`, `free ${genre} app`, `${genre} mobile game`, `offline ${genre}`, `${genre} adventure`]
  };
}

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Stormie AI Marketing Platform running on port ${PORT}`);
  console.log(`🧠 AI Engine: Hugging Face Integration Active`);
  console.log(`🎮 Ready to analyze games and generate marketing insights!`);
});

module.exports = app;
