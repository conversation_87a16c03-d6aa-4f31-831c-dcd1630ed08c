const { HfInference } = require('@huggingface/inference');
const axios = require('axios');

class StormieAI {
  constructor() {
    this.hf = new HfInference(process.env.HUGGINGFACE_API_KEY);
    this.models = {
      textGeneration: 'microsoft/DialoGPT-medium',
      sentiment: 'cardiffnlp/twitter-roberta-base-sentiment-latest',
      classification: 'facebook/bart-large-mnli'
    };
  }

  // Core AI Analysis Engine
  async analyzeGameProject(gameData) {
    try {
      const analysis = await Promise.all([
        this.analyzeMarketPosition(gameData),
        this.analyzeCompetition(gameData),
        this.analyzeTargetAudience(gameData),
        this.generateSuccessScore(gameData)
      ]);

      return {
        marketPosition: analysis[0],
        competition: analysis[1],
        targetAudience: analysis[2],
        successScore: analysis[3],
        recommendations: await this.generateRecommendations(gameData),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Game analysis error:', error);
      return this.getFallbackAnalysis(gameData);
    }
  }

  // Market Position Analysis
  async analyzeMarketPosition(gameData) {
    const prompt = `Analyze market position for: ${gameData.title} (${gameData.genre})
    Description: ${gameData.description}
    
    Provide market positioning analysis focusing on:
    1. Unique selling proposition
    2. Market gap identification
    3. Positioning strategy
    4. Differentiation factors`;

    try {
      const response = await this.hf.textGeneration({
        model: this.models.textGeneration,
        inputs: prompt,
        parameters: {
          max_new_tokens: 200,
          temperature: 0.7
        }
      });

      return this.parseMarketPosition(response.generated_text);
    } catch (error) {
      return this.getFallbackMarketPosition(gameData);
    }
  }

  // ASO Keyword Generation
  async generateASOKeywords(gameData) {
    const genreKeywords = this.getGenreKeywords(gameData.genre);
    const platformKeywords = this.getPlatformKeywords(gameData.platform);
    
    const prompt = `Generate ASO keywords for ${gameData.title}:
    Genre: ${gameData.genre}
    Platform: ${gameData.platform}
    Features: ${gameData.features?.join(', ') || 'Standard game features'}
    
    Create keyword strategy with high search volume and low competition.`;

    try {
      const aiKeywords = await this.hf.textGeneration({
        model: this.models.textGeneration,
        inputs: prompt,
        parameters: {
          max_new_tokens: 150,
          temperature: 0.6
        }
      });

      return {
        primary: this.extractPrimaryKeywords(gameData, aiKeywords.generated_text),
        secondary: [...genreKeywords, ...platformKeywords],
        longTail: this.generateLongTailKeywords(gameData),
        trending: await this.getTrendingKeywords(gameData.genre),
        difficulty: this.assessKeywordDifficulty(gameData)
      };
    } catch (error) {
      return this.getFallbackKeywords(gameData);
    }
  }

  // Social Media Content Generation
  async generateSocialContent(gameData, platform, contentType) {
    const platformSpecs = this.getPlatformSpecs(platform);
    
    const prompt = `Create ${contentType} for ${platform}:
    Game: ${gameData.title}
    Genre: ${gameData.genre}
    Key Features: ${gameData.features?.join(', ') || 'Engaging gameplay'}
    
    Requirements:
    - ${platformSpecs.characterLimit} character limit
    - ${platformSpecs.tone} tone
    - Include call-to-action
    - Platform-optimized hashtags`;

    try {
      const content = await this.hf.textGeneration({
        model: this.models.textGeneration,
        inputs: prompt,
        parameters: {
          max_new_tokens: 100,
          temperature: 0.8
        }
      });

      return {
        content: this.formatContent(content.generated_text, platform),
        hashtags: this.generateHashtags(gameData, platform),
        bestTime: this.getBestPostingTime(platform),
        engagement: this.getEngagementStrategy(platform),
        visualSuggestions: this.getVisualSuggestions(gameData, platform)
      };
    } catch (error) {
      return this.getFallbackSocialContent(gameData, platform, contentType);
    }
  }

  // Pricing Strategy Optimization
  async optimizePricingStrategy(gameData, marketData = {}) {
    const competitorPricing = await this.analyzeCompetitorPricing(gameData.genre);
    const userWillingness = this.estimateWillingnessToPay(gameData);
    
    const strategies = [
      this.calculateFreemiumStrategy(gameData),
      this.calculatePremiumStrategy(gameData, competitorPricing),
      this.calculatePenetrationStrategy(gameData, competitorPricing),
      this.calculateSkimmingStrategy(gameData)
    ];

    return {
      recommended: this.selectBestStrategy(strategies, gameData),
      alternatives: strategies,
      pricePoints: this.generatePricePoints(gameData, competitorPricing),
      monetization: this.suggestMonetizationMethods(gameData),
      testing: this.suggestPriceTesting(gameData),
      timeline: this.createPricingTimeline(gameData)
    };
  }

  // Market Insights & Predictions
  async generateMarketInsights(gameData) {
    try {
      const insights = await Promise.all([
        this.analyzeMarketTrends(gameData.genre),
        this.predictUserBehavior(gameData),
        this.analyzeSeasonality(gameData.genre),
        this.assessMarketSaturation(gameData.genre)
      ]);

      return {
        trends: insights[0],
        userBehavior: insights[1],
        seasonality: insights[2],
        saturation: insights[3],
        opportunities: this.identifyOpportunities(gameData, insights),
        risks: this.identifyRisks(gameData, insights),
        forecast: this.generateForecast(gameData, insights)
      };
    } catch (error) {
      return this.getFallbackInsights(gameData);
    }
  }

  // Utility Methods
  getGenreKeywords(genre) {
    const keywordMap = {
      'action': ['action', 'combat', 'fighting', 'shooter', 'battle'],
      'puzzle': ['puzzle', 'brain', 'logic', 'mind', 'challenge'],
      'strategy': ['strategy', 'tactical', 'planning', 'empire', 'war'],
      'rpg': ['rpg', 'adventure', 'quest', 'fantasy', 'character'],
      'casual': ['casual', 'easy', 'relaxing', 'simple', 'fun'],
      'arcade': ['arcade', 'retro', 'classic', 'score', 'high-score']
    };
    return keywordMap[genre?.toLowerCase()] || ['game', 'mobile', 'entertainment'];
  }

  getPlatformSpecs(platform) {
    const specs = {
      'twitter': { characterLimit: 280, tone: 'conversational', hashtags: 3 },
      'instagram': { characterLimit: 2200, tone: 'visual-focused', hashtags: 10 },
      'facebook': { characterLimit: 500, tone: 'community-focused', hashtags: 5 },
      'tiktok': { characterLimit: 150, tone: 'trendy', hashtags: 5 },
      'linkedin': { characterLimit: 700, tone: 'professional', hashtags: 3 }
    };
    return specs[platform.toLowerCase()] || specs.twitter;
  }

  generateHashtags(gameData, platform) {
    const base = ['#gaming', '#mobilegame', '#gamedev'];
    const genre = gameData.genre ? [`#${gameData.genre.toLowerCase()}game`] : [];
    const platform_specific = platform === 'instagram' ? ['#gamer', '#gameplay', '#indiegame'] : [];
    
    return [...base, ...genre, ...platform_specific].slice(0, this.getPlatformSpecs(platform).hashtags);
  }

  getBestPostingTime(platform) {
    const times = {
      'twitter': '9:00 AM - 10:00 AM, 7:00 PM - 9:00 PM',
      'instagram': '11:00 AM - 1:00 PM, 7:00 PM - 9:00 PM',
      'facebook': '1:00 PM - 3:00 PM, 7:00 PM - 9:00 PM',
      'tiktok': '6:00 AM - 10:00 AM, 7:00 PM - 9:00 PM',
      'linkedin': '8:00 AM - 10:00 AM, 12:00 PM - 2:00 PM'
    };
    return times[platform.toLowerCase()] || times.twitter;
  }

  // Fallback methods for when AI fails
  getFallbackAnalysis(gameData) {
    return {
      marketPosition: `${gameData.title} positioned in the ${gameData.genre} market`,
      competition: 'Moderate competition with opportunities for differentiation',
      targetAudience: `${gameData.genre} enthusiasts and casual gamers`,
      successScore: 7.5,
      recommendations: [
        'Focus on unique gameplay mechanics',
        'Implement strong ASO strategy',
        'Build community engagement',
        'Consider freemium monetization'
      ]
    };
  }

  getFallbackKeywords(gameData) {
    return {
      primary: [gameData.title?.toLowerCase(), gameData.genre?.toLowerCase(), 'mobile', 'game', 'app'],
      secondary: this.getGenreKeywords(gameData.genre),
      longTail: [`best ${gameData.genre} game`, `free ${gameData.genre} app`],
      trending: ['new game', 'mobile gaming', 'free download'],
      difficulty: 'Medium'
    };
  }

  getFallbackSocialContent(gameData, platform, contentType) {
    return {
      content: `🎮 Discover ${gameData.title}! An amazing ${gameData.genre} game that will keep you entertained for hours. Download now! #gaming #mobilegame`,
      hashtags: this.generateHashtags(gameData, platform),
      bestTime: this.getBestPostingTime(platform),
      engagement: 'Post consistently and engage with comments',
      visualSuggestions: ['Game screenshots', 'Gameplay videos', 'Character art']
    };
  }
}

module.exports = StormieAI;
